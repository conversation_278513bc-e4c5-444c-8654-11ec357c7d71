//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  $values: (
    'container-color': map.get($deps, 'md-sys-color', 'surface-container-low'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'container-shadow-color': map.get($deps, 'md-sys-color', 'shadow'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-medium'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'disabled-container-color': map.get($deps, 'md-sys-color', 'surface'),
    'disabled-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'disabled-container-opacity': if($exclude-hardcoded-values, null, 0.38),
    'dragged-container-elevation': map.get($deps, 'md-sys-elevation', 'level4'),
    'dragged-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'dragged-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'dragged-state-layer-opacity'),
    'focus-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'focus-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'hover-container-elevation': map.get($deps, 'md-sys-elevation', 'level2'),
    'hover-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'icon-size': if($exclude-hardcoded-values, null, 24px),
    'pressed-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'pressed-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity')
  );

  @return $values;
}
