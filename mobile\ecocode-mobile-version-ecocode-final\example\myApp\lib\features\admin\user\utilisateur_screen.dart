import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:NovaSchool/features/admin/A_appDrawer.dart';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/admin/profil/profil_Ascreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/admin/user/utiliateur_parent.dart';
import 'package:NovaSchool/features/admin/user/utilisateur_enseignant.dart';
import 'package:NovaSchool/features/chat/test.dart';

import '../../../commun/app_background.dart';
import '../../../commun/parent_app/app_secondheadBar.dart';
import '../../../utils/constants/colors.dart';
import 'package:get/get.dart';
class UserManagementPage extends StatefulWidget {
  @override
  _UserManagementPageState createState() => _UserManagementPageState();
}
Future<Map<String, String>> _getUserInfo() async {
  return await AuthService().getSenderDetails();
}
class _UserManagementPageState extends State<UserManagementPage> {
  bool showTeachers = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AdminDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            iconTheme: IconThemeData(
              color: Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            title: GestureDetector(
              onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => AdminAppNavBar()), // remplace THomeScreen par ton écran d'accueil
                );
              },
            /*title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<ANavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => AdminAppNavBar());
              },*/
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),
                ],
              ),
            ),/*Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/logos/ecocode.png',
                  height: 150,
                  color: Colors.white,
                ),
                SizedBox(width: 10),

              ],
            ),*/
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
             /* IconButton(
                icon: Icon(Icons.person_outline, color: Colors.white,size: 30,),
                onPressed: () async {
                  Map<String, String> userInfo = await _getUserInfo();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AProfilScreen(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                        email: userInfo['email'] ?? '',
                        profile: userInfo['profile'] ?? '',
                      ),
                    ),
                  );
                },
              ),*/
              /*IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  onPressed: () {},
                ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SecondHeadBar1 (title: 'Utilisateurs',icon: Icons.supervised_user_circle_rounded,),

            /*PrimaryHeaderContainer(
              child: Container(
                height: 100, // Adjust the height here
                child: SecondHeadBar(
                  title: 'Utilisateurs',
                  titleColor: Colors.white,
                  iconColor: Colors.white,
                ),
              ),
            ),*/
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          showTeachers = false;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: !showTeachers ? Colors.lightBlue.shade300 : Colors.lightBlue.shade300.withOpacity(0.5),//Color(0xFF4099FF) : Colors.grey,
                          width: 2.0,
                        ),
                      ),
                      child: Text(
                        'Parents',
                        style: TextStyle(
                          color: !showTeachers ? Colors.lightBlue.shade300 : Colors.lightBlue.shade300.withOpacity(0.5),//Color(0xFF4099FF) : Colors.black,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 10),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          showTeachers = true;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: showTeachers ? Colors.lightBlue.shade300 : Colors.lightBlue.shade300.withOpacity(0.5),//Color(0xFF4099FF) : Colors.grey,
                          width: 2.0,
                        ),
                      ),
                      child: Text(
                        'Enseignants',
                        style: TextStyle(
                          color: showTeachers ? Colors.lightBlue.shade300 : Colors.lightBlue.shade300.withOpacity(0.5),//Color(0xFF4099FF) : Colors.black,
                        ),
                      ),
                    ),
                  ),

                ],
              ),
            ),
            if (showTeachers)
              TeacherManagementWidget()
            else
              ParentManagementWidget(),
          ],
        ),
      ),
    );
  }
}
