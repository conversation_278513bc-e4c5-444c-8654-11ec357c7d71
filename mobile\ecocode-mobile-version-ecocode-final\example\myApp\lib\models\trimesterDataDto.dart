class TrimesterDataDto {
  final Map<String, Trimester> trimesters;

  TrimesterDataDto({required this.trimesters});

  factory TrimesterDataDto.fromJson(Map<String, dynamic> json) {
    Map<String, Trimester> parsedTrimesters = {};
    json['trimesters']?.forEach((key, value) {
      parsedTrimesters[key] = Trimester.fromJson(value);
    });
    return TrimesterDataDto(trimesters: parsedTrimesters);
  }
}

class Trimester {
  final Version versionEtatique;
  final Version versionPrive;

  Trimester({required this.versionEtatique, required this.versionPrive});

  factory Trimester.fromJson(Map<String, dynamic> json) {
    return Trimester(
      versionEtatique: Version.fromJson(json['versionEtatique']),
      versionPrive: Version.fromJson(json['versionPrive']),
    );
  }
}

class Version {
  final List<Module>? modules;
  final double? moyenneGenerale;
  final bool acces;

  Version({this.modules, this.moyenneGenerale, required this.acces});

  factory Version.fromJson(Map<String, dynamic> json) {
    return Version(
      modules: (json['modules'] as List<dynamic>?)
          ?.map((module) => Module.fromJson(module))
          .toList(),
      moyenneGenerale: json['moyenneGenerale'],
      acces: json['acces'],
    );
  }
}

class Module {
  final int moduleId;
  final String module;
  final double average;
  final List<Subject> subjects;

  Module({
    required this.moduleId,
    required this.module,
    required this.average,
    required this.subjects,
  });

  factory Module.fromJson(Map<String, dynamic> json) {
    return Module(
      moduleId: json['moduleId'],
      module: json['module'],
      average: json['average'],
      subjects: (json['subjects'] as List<dynamic>)
          .map((subject) => Subject.fromJson(subject))
          .toList(),
    );
  }
}

class Subject {
  final int idMatiere;
  final String matiere;
  final double valeur;
  final bool nonClassed;

  Subject({
    required this.idMatiere,
    required this.matiere,
    required this.valeur,
    required this.nonClassed,
  });

  factory Subject.fromJson(Map<String, dynamic> json) {
    return Subject(
      idMatiere: json['idMatiere'],
      matiere: json['matiere'],
      valeur: json['valeur'],
      nonClassed: json['nonClassed'],
    );
  }
}
