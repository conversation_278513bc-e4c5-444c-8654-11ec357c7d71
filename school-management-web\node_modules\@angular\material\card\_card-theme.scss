@use 'sass:map';
@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/typography/typography';
@use '../core/tokens/token-utils';
@use '../core/tokens/m2/mat/card' as tokens-mat-card;
@use '../core/tokens/m2/mdc/elevated-card' as tokens-mdc-elevated-card;
@use '../core/tokens/m2/mdc/outlined-card' as tokens-mdc-outlined-card;

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-elevated-card.$prefix,
        tokens-mdc-elevated-card.get-unthemable-tokens()
      );
      @include token-utils.create-token-values(
        tokens-mdc-outlined-card.$prefix,
        tokens-mdc-outlined-card.get-unthemable-tokens()
      );
      @include token-utils.create-token-values(
        tokens-mat-card.$prefix,
        tokens-mat-card.get-unthemable-tokens()
      );
    }
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-elevated-card.$prefix,
        tokens-mdc-elevated-card.get-color-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mdc-outlined-card.$prefix,
        tokens-mdc-outlined-card.get-color-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-card.$prefix,
        tokens-mat-card.get-color-tokens($theme)
      );
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-elevated-card.$prefix,
        tokens-mdc-elevated-card.get-typography-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mdc-outlined-card.$prefix,
        tokens-mdc-outlined-card.get-typography-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-card.$prefix,
        tokens-mat-card.get-typography-tokens($theme)
      );
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-elevated-card.$prefix,
        tokens-mdc-elevated-card.get-density-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mdc-outlined-card.$prefix,
        tokens-mdc-outlined-card.get-density-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-card.$prefix,
        tokens-mat-card.get-density-tokens($theme)
      );
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: tokens-mat-card.$prefix,
      tokens: tokens-mat-card.get-token-slots(),
    ),
    (
      namespace: tokens-mdc-elevated-card.$prefix,
      tokens: tokens-mdc-elevated-card.get-token-slots(),
      prefix: 'elevated-',
    ),
    (
      namespace: tokens-mdc-outlined-card.$prefix,
      tokens: tokens-mdc-outlined-card.get-token-slots(),
      prefix: 'outlined-',
    ),
  );
}

@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values($tokens, _define-overrides()...);
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-card') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme));
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens) {
  @include validation.selector-defined(
    'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector'
  );
  @if ($tokens != ()) {
    @include token-utils.create-token-values(
      tokens-mdc-elevated-card.$prefix,
      map.get($tokens, tokens-mdc-elevated-card.$prefix)
    );
    @include token-utils.create-token-values(
      tokens-mdc-outlined-card.$prefix,
      map.get($tokens, tokens-mdc-outlined-card.$prefix)
    );
    @include token-utils.create-token-values(
      tokens-mat-card.$prefix,
      map.get($tokens, tokens-mat-card.$prefix)
    );
  }
}
