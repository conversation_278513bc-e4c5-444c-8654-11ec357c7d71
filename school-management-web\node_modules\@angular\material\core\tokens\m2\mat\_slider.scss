@use 'sass:map';
@use '../../token-definition';
@use '../../../style/sass-utils';
@use '../../../theming/inspection';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, slider);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    value-indicator-width: auto,
    value-indicator-height: 32px,
    value-indicator-caret-display: block,
    value-indicator-border-radius: 4px,
    value-indicator-padding: 0 12px,
    value-indicator-text-transform: none,
    value-indicator-container-transform: translateX(-50%)
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $theme-color-tokens: private-get-color-palette-color-tokens($theme, primary);
  $is-dark: inspection.get-theme-type($theme) == dark;

  @return map.merge(
    $theme-color-tokens,
    (
      // Opacity of value indicator text container
      value-indicator-opacity: if($is-dark, 0.9, 0.6)
    ),
  );
}

// Generates tokens for the slider properties that change based on the theme.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  $ripple-color: inspection.get-theme-color($theme, $palette-name);
  $hover-ripple-color: if($ripple-color == null, null, #{rgba($ripple-color, 0.05)});
  $focus-ripple-color: if($ripple-color == null, null, #{rgba($ripple-color, 0.2)});

  @return (
    ripple-color: $ripple-color,
    hover-state-layer-color: $hover-ripple-color,
    focus-state-layer-color: $focus-ripple-color
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
    get-unthemable-tokens(),
    get-color-tokens(token-definition.$placeholder-color-config),
    get-typography-tokens(token-definition.$placeholder-typography-config),
    get-density-tokens(token-definition.$placeholder-density-config)
  );
}
