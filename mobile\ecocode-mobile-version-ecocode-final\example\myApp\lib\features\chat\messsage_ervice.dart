import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
//import '../../commun/base_url.dart';
import 'class_chat.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class MessageService {
  Future<List<String>> getMessages(int userId) async {

    String baseURL = dotenv.get('baseURL');
    final String url = '$baseURL/enseignants/$userId/messages';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');

    final response = await http.get(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      // Print the response body for debugging
      print('Response body: ${response.body}');

      // Try parsing the response body as a JSON array of strings
      try {
        List<dynamic> body = json.decode(response.body);
        List<String> messages = body.cast<String>();
        return messages;
      } catch (e) {
        // Handle JSON parsing error
        print('JSON parsing error: $e');
        throw Exception('Failed to parse JSON response');
      }
    } else {
      // Handle HTTP error status codes
      throw Exception('Failed to load messages. Status code: ${response.statusCode}');
    }
  }




  Future<void> sendMessage(int adminId, int userId, String content) async {
    String baseURL = dotenv.get('baseURL');
    final String url = '$baseURL/enseignants/$adminId/message/teacher/$userId'; // Corrigez l'URL
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');

    final response = await http.post(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({
        'message': content,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to send message');
    }
  }
}
