import 'dart:convert';
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/models/ligneArticleDTO.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/tokenInterceptor.dart';
import 'package:http/http.dart' as http;
import 'package:http_interceptor/http_interceptor.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class LigneArticleService {

  late final String baseURL;
  late final String baseUrl;
  // String baseURL = dotenv.get('baseURL');
  // final String baseUrl = "$baseURL/lignearticle";
  final auth = AuthService();
  final httpClient =
      InterceptedClient.build(interceptors: [TokenInterceptor()]);

  LigneArticleService(){
    baseURL = dotenv.get('baseURL');
    baseUrl = "$baseURL/lignearticle";
  }

  Future<Map<String, dynamic>?> getLigneArticlesByIdElevePage(
      {required int eleveId,
      int page = 0,
      int size = 10,
      // String sortBy = "dateAchat",
      // String sortDirection = "desc"
      }) async {


        // String baseURL = dotenv.get('baseURL');
        // final String baseUrl = "$baseURL/lignearticle";
    final url = Uri.parse(
        '$baseUrl/$eleveId/eleve/page?page=$page&size=$size');

    try {
      final response = await httpClient.get(url);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        List<dynamic> content = jsonData['content'];
        int totalPages = jsonData['totalPages'];
        int totalElements = jsonData['totalElements'];
        List<GetLigneArticleDTO> articles = content
            .map((articleJson) => GetLigneArticleDTO.fromJson(articleJson))
            .toList();

        return {
          'articles': articles,
          'totalPages': totalPages,
          'totalElements': totalElements
        };
      } else {
        print('Failed to load articles. Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error occurred while fetching articles: $e');
      return null;
    }
  }
}
