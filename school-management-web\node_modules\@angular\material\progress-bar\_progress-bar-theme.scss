@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/tokens/token-utils';
@use '../core/tokens/m2/mdc/linear-progress' as tokens-mdc-linear-progress;

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-progress-bar.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  } @else {
    // Add default values for tokens not related to color, typography, or density.
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mdc-linear-progress.$prefix,
        tokens-mdc-linear-progress.get-unthemable-tokens()
      );
    }
  }
}

@mixin _palette-styles($theme, $palette-name) {
  @include token-utils.create-token-values(
    tokens-mdc-linear-progress.$prefix,
    tokens-mdc-linear-progress.get-color-tokens($theme, $palette-name)
  );
}

/// Outputs color theme styles for the mat-progress-bar.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the progress bar: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  } @else {
    .mat-mdc-progress-bar {
      @include _palette-styles($theme, primary);

      &.mat-accent {
        @include _palette-styles($theme, accent);
      }

      &.mat-warn {
        @include _palette-styles($theme, warn);
      }
    }
  }
}

/// Outputs typography theme styles for the mat-progress-bar.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
}

/// Outputs density theme styles for the mat-progress-bar.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: tokens-mdc-linear-progress.$prefix,
      tokens: tokens-mdc-linear-progress.get-token-slots(),
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values($tokens, _define-overrides()...);
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-progress-bar.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the progress bar: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-progress-bar') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
    'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector'
  );
  $tokens: token-utils.get-tokens-for($tokens, tokens-mdc-linear-progress.$prefix, $options...);
  @include token-utils.create-token-values(tokens-mdc-linear-progress.$prefix, $tokens);
}
