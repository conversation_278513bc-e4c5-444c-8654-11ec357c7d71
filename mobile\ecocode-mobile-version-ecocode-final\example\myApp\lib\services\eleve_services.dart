import 'dart:async';
import 'dart:convert';
import 'package:NovaSchool/models/eleveResponseDTO.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/tokenInterceptor.dart';
import 'package:http/http.dart' as http;
import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
import 'package:NovaSchool/models/eleveParListClassesDTO.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class EleveServices {
  // String baseURL = dotenv.get('baseURL');
  late final String baseURL;
  late final String baseEleveUrl;
  late final String eleveUrl;
  // final String baseEleveUrl = "$baseURL/eleve";
  // final String eleveUrl = "$baseURL/eleveTuteur";
  String baseURL1 = dotenv.get('baseURL');
  final httpClient =
      InterceptedClient.build(interceptors: [TokenInterceptor()]);

  EleveServices() {
    baseURL = dotenv.get('baseURL');
    baseEleveUrl = "$baseURL/eleve";
    eleveUrl = "$baseURL/eleveTuteur";
  }
  final auth = AuthService();

  Future<List<EleveParListClassesDTO>> getEleveByListClasses(
      List<int> listClasse) async {
    final url = Uri.parse('$baseEleveUrl/eleveByListClasses')
        .replace(queryParameters: {'listclasse': listClasse.join(',')});

    final response = await httpClient.get(
      url,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => EleveParListClassesDTO.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load students');
    }
  }

  Future<List<EleveInfoPersoDTO>> getEleveByTuteurKeyklock(
      String? idTuteurKeyklock) async {
    final url = Uri.parse('$baseEleveUrl/eleveByTuteurKeyklock')
        .replace(queryParameters: {'idTuteurKeyklock': idTuteurKeyklock});

    final response = await httpClient.get(
      url,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((json) => EleveInfoPersoDTO.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load students');
    }
  }

  Future<EleveResponseDTO> getEleveById(int idEleve) async {
    final url = Uri.parse('$eleveUrl/$idEleve');

    final response = await httpClient.get(
      url,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      return EleveResponseDTO.fromJson(json);
    } else {
      throw Exception('Failed to load student');
    }
  }

  Future<List<EleveParListClassesDTO>> fetchStudents(
      List<int> selectedClassIds) async {
    return await getEleveByListClasses(selectedClassIds);
  }

  Future<void> updateFirebaseToken() async {
    String? userId = await auth.getUserId();
    String? token = await FirebaseMessaging.instance.getToken();  // Await to get the token

    if (token != null) {
      final url = '$baseURL1/tuteur/updateToken?userId=$userId&firebaseToken=$token'; // Path to your endpoint
      print("Updating Firebase Token--------------------------------------: $token"); // Log the token
      print("UserId--------------------------------------: $userId"); 
      print("API Request Path: $url"); // Log the path

      try {
        // Make the POST request to update the token
        final response = await httpClient.post(
          Uri.parse(url)
        );

        if (response.statusCode == 200) {
          print("Firebase token updated successfully.");
        } else {
          print("Failed to update Firebase token. Status code: ${response.statusCode}");
        }
      } catch (e) {
        print("Error while updating Firebase token: $e");
      }
    } else {
      print("Failed to get Firebase token.");
    }
  }
}
