package com.ecocode.dto;

import com.google.firebase.database.annotations.NotNull;

public class NotificationRequest {
	 @NotNull 
	    private String token;

	    @NotNull 
	    private String title;

	    @NotNull 
	    private String body;

    // Getters et Setters
    public String getToken() { return token; }
    public void setToken(String token) { this.token = token; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getBody() { return body; }
    public void setBody(String body) { this.body = body; }
}