//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-shape';

@use './md-sys-state';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'container-size': if($exclude-hardcoded-values, null, 40px),
    'disabled-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-icon-opacity': if($exclude-hardcoded-values, null, 0.38),
    'disabled-selected-container-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-selected-container-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'disabled-unselected-outline-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-unselected-outline-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'icon-size': if($exclude-hardcoded-values, null, 24px),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'selected-container-color':
      map.get($deps, 'md-sys-color', 'inverse-surface'),
    'selected-focus-icon-color':
      map.get($deps, 'md-sys-color', 'inverse-on-surface'),
    'selected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'inverse-on-surface'),
    'selected-hover-icon-color':
      map.get($deps, 'md-sys-color', 'inverse-on-surface'),
    'selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'inverse-on-surface'),
    'selected-icon-color': map.get($deps, 'md-sys-color', 'inverse-on-surface'),
    'selected-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'inverse-on-surface'),
    'selected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'inverse-on-surface'),
    'unselected-focus-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-hover-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-outline-color': map.get($deps, 'md-sys-color', 'outline'),
    'unselected-outline-width': if($exclude-hardcoded-values, null, 1px),
    'unselected-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface')
  );
}
