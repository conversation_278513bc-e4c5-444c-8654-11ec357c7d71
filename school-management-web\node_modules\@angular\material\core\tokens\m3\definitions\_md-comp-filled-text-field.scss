//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'active-indicator-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'active-indicator-height': if($exclude-hardcoded-values, null, 1px),
    'caret-color': map.get($deps, 'md-sys-color', 'primary'),
    'container-color': map.get($deps, 'md-sys-color', 'surface-variant'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-extra-small-top'),
    'disabled-active-indicator-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-active-indicator-height': if($exclude-hardcoded-values, null, 1px),
    'disabled-active-indicator-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'disabled-container-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-container-opacity': if($exclude-hardcoded-values, null, 0.04),
    'disabled-input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-input-text-opacity': if($exclude-hardcoded-values, null, 0.38),
    'disabled-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-label-text-opacity': if($exclude-hardcoded-values, null, 0.38),
    'disabled-leading-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-leading-icon-opacity': if($exclude-hardcoded-values, null, 0.38),
    'disabled-supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-supporting-text-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'disabled-trailing-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-trailing-icon-opacity': if($exclude-hardcoded-values, null, 0.38),
    'error-active-indicator-color': map.get($deps, 'md-sys-color', 'error'),
    'error-focus-active-indicator-color':
      map.get($deps, 'md-sys-color', 'error'),
    'error-focus-caret-color': map.get($deps, 'md-sys-color', 'error'),
    'error-focus-input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'error-focus-label-text-color': map.get($deps, 'md-sys-color', 'error'),
    'error-focus-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'error-focus-supporting-text-color': map.get($deps, 'md-sys-color', 'error'),
    'error-focus-trailing-icon-color': map.get($deps, 'md-sys-color', 'error'),
    'error-hover-active-indicator-color':
      map.get($deps, 'md-sys-color', 'on-error-container'),
    'error-hover-input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'error-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-error-container'),
    'error-hover-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'error-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'error-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'error-hover-supporting-text-color': map.get($deps, 'md-sys-color', 'error'),
    'error-hover-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-error-container'),
    'error-input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'error-label-text-color': map.get($deps, 'md-sys-color', 'error'),
    'error-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'error-supporting-text-color': map.get($deps, 'md-sys-color', 'error'),
    'error-trailing-icon-color': map.get($deps, 'md-sys-color', 'error'),
    'focus-active-indicator-color': map.get($deps, 'md-sys-color', 'primary'),
    'focus-active-indicator-height': if($exclude-hardcoded-values, null, 2px),
    'focus-input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'focus-label-text-color': map.get($deps, 'md-sys-color', 'primary'),
    'focus-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'focus-supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'focus-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'hover-active-indicator-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'hover-active-indicator-height': if($exclude-hardcoded-values, null, 1px),
    'hover-input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'hover-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'hover-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'hover-supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'hover-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'input-text-font': map.get($deps, 'md-sys-typescale', 'body-large-font'),
    'input-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-large-line-height'),
    'input-text-placeholder-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'input-text-prefix-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'input-text-size': map.get($deps, 'md-sys-typescale', 'body-large-size'),
    'input-text-suffix-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'input-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.filled-text-field.input-text.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'input-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-large-weight')
          map.get($deps, 'md-sys-typescale', 'body-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-large-font')
      ),
    'input-text-weight': map.get($deps, 'md-sys-typescale', 'body-large-weight'),
    'label-text-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'label-text-font': map.get($deps, 'md-sys-typescale', 'body-large-font'),
    'label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-large-line-height'),
    'label-text-populated-line-height':
      map.get($deps, 'md-sys-typescale', 'body-small-line-height'),
    'label-text-populated-size':
      map.get($deps, 'md-sys-typescale', 'body-small-size'),
    'label-text-size': map.get($deps, 'md-sys-typescale', 'body-large-size'),
    'label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.filled-text-field.label-text.tracking cannot be represented in the
    // "font" property shorthand. Consider using the discrete properties instead.
    'label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-large-weight')
          map.get($deps, 'md-sys-typescale', 'body-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-large-font')
      ),
    'label-text-weight': map.get($deps, 'md-sys-typescale', 'body-large-weight'),
    'leading-icon-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'leading-icon-size': if($exclude-hardcoded-values, null, 24px),
    'supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'supporting-text-font':
      map.get($deps, 'md-sys-typescale', 'body-small-font'),
    'supporting-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-small-line-height'),
    'supporting-text-size':
      map.get($deps, 'md-sys-typescale', 'body-small-size'),
    'supporting-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-small-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.filled-text-field.supporting-text.tracking cannot be represented in
    // the "font" property shorthand. Consider using the discrete properties instead.
    'supporting-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-small-weight')
          map.get($deps, 'md-sys-typescale', 'body-small-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-small-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-small-font')
      ),
    'supporting-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-small-weight'),
    'trailing-icon-color': map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'trailing-icon-size': if($exclude-hardcoded-values, null, 24px)
  );
}
