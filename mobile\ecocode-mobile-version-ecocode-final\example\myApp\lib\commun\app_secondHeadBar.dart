import 'package:NovaSchool/models/extensionConfig.dart';
import 'package:NovaSchool/services/extensionConfigService.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../utils/constants/text.dart';
import 'parent_app/appBar_config.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';

class SAppHeadBar extends StatefulWidget {
  final Widget profilPage;

  const SAppHeadBar({
    Key? key,
    required this.profilPage,
  }) : super(key: key);

  @override
  _SAppHeadBarState createState() => _SAppHeadBarState();
}

class _SAppHeadBarState extends State<SAppHeadBar> {
  final int configId = 1;
  final ExtensionConfigService service = ExtensionConfigService();
  final FileDownloadService fileDownloadService = FileDownloadService();

  void _showSnackbar(BuildContext context, String message, Color color,
      {String? actionLabel, VoidCallback? onPressed}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        action: actionLabel != null
            ? SnackBarAction(label: actionLabel, onPressed: onPressed ?? () {})
            : null,
      ),
    );
  }

  Future<void> _downloadFile(BuildContext context, String url) async {
    try {
      fileDownloadService.showLoadingDialog(
          context, 'Téléchargement en cours...');
      if (await canLaunch(url)) {
        await launch(url);
        _showSnackbar(
          context,
          'Téléchargement démarré. Vérifiez votre dossier Téléchargements.',
          Colors.green,
        );
      } else {
        _showSnackbar(
          context,
          'Impossible de lancer le téléchargement.',
          Colors.red,
        );
      }
    } catch (e) {
      _showSnackbar(
        context,
        'Une erreur s\'est produite pendant le téléchargement.',
        Colors.red,
      );
    } finally {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ExtensionConfig>(
        future: service.getById(configId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return CircularProgressIndicator();
          }

          ExtensionConfig? config = snapshot.data;

          return AppBarConfig(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [

                    Image.asset(
                      'assets/logos/ecocode.png',
                      color: Colors.white.withOpacity(0.89),
                      colorBlendMode: BlendMode.srcIn,
                      width: 120,
                      height: 120,
                    ),
                    /*Text(
                      AppText.Apptitle,
                      style: GoogleFonts.dmSerifDisplay(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),*/
                  ],
                ),
                //SizedBox(height: 10),
                if (config != null &&
                    config.message != null &&
                    config.message!.isNotEmpty)
                  GestureDetector(
                    onTap: config.path != null && config.path!.isNotEmpty
                        ? () => _downloadFile(context, config.path!)
                        : null,
                    child: Row(
                      children: [
                        if (config.path != null && config.path!.isNotEmpty)
                          Icon(
                            Icons.download,
                            color: Colors.yellow,
                            size: 20,
                          ),
                        SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            config.message!,
                            style: GoogleFonts.dmSerifDisplay(
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              color: Colors.yellow,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            actions: [/*
              Tooltip(
                message: 'Profile',
                child: IconButton(
                  icon: Icon(Icons.person_outline),
                  color: Colors.white,
                  iconSize: 20, // Ajustez la taille si nécessaire
                  onPressed: () async {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => widget.profilPage),
                    );
                  },
                ),
              ),
              Tooltip(
                message: 'Déconnexion',
                child: IconButton(
                  icon: Icon(Icons.logout),
                  color: Colors.white,
                  iconSize: 20, // Ajustez la taille si nécessaire
                  onPressed: () async {
                    await AuthService.logoutKeycloak();
                  },
                ),

              ),*/
            ],

          );
        });
  }
}
