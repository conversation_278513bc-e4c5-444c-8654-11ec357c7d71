.parent-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

// Header Styles
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 0 20px;
  height: 64px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #f0f0f0;
  }

  .hamburger {
    display: block;
    width: 20px;
    height: 2px;
    background-color: #333;
    position: relative;
    transition: all 0.3s ease;

    &::before,
    &::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 2px;
      background-color: #333;
      transition: all 0.3s ease;
    }

    &::before {
      top: -6px;
    }

    &::after {
      bottom: -6px;
    }
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;

  .logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
  }

  .logo-text {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

.header-right {
  display: flex;
  align-items: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  cursor: pointer;

  &:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  
  .user-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .user-role {
    font-size: 12px;
    color: #666;
  }
}

.user-avatar {
  .avatar-img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e9ecef;
  }
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 8px;
  min-width: 120px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1001;

  .logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 8px 12px;
    background: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #dc3545;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #f8f9fa;
    }
  }
}

// Sidebar Styles
.sidebar {
  position: fixed;
  left: 0;
  top: 64px;
  width: 260px;
  height: calc(100vh - 64px);
  background: white;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 999;
  overflow-y: auto;

  &.open {
    transform: translateX(0);
  }
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
  border-right: 3px solid transparent;

  &:hover {
    background-color: #f8f9fa;
    color: #333;
  }

  &.active {
    background-color: #e3f2fd;
    color: #1976d2;
    border-right-color: #1976d2;
  }
}

.nav-icon {
  font-size: 20px;
  width: 20px;
  text-align: center;
}

.nav-label {
  font-weight: 500;
}

// Main Content Styles
.main-content {
  flex: 1;
  margin-left: 0;
  transition: margin-left 0.3s ease;
  overflow: hidden;
}

.content-wrapper {
  padding: 20px;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

// Sidebar Overlay
.sidebar-overlay {
  position: fixed;
  top: 64px;
  left: 0;
  width: 100%;
  height: calc(100vh - 64px);
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 998;

  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// Responsive Design
@media (min-width: 768px) {
  .menu-toggle {
    display: none;
  }

  .sidebar {
    position: relative;
    transform: translateX(0);
    top: 0;
    height: calc(100vh - 64px);
  }

  .main-content {
    margin-left: 260px;
  }

  .sidebar-overlay {
    display: none;
  }
}

@media (max-width: 767px) {
  .menu-toggle {
    display: block;
  }

  .user-info {
    display: none;
  }

  .content-wrapper {
    padding: 16px;
  }
}

// Icon styles (you can replace with your preferred icon library)
[class^="icon-"] {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
}
