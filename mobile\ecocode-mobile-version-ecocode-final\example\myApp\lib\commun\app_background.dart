import 'package:flutter/material.dart';

class AppBackground extends StatelessWidget {
  const AppBackground({
    Key? key,
    required this.child,
  }) : super(key: key);


  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(

      decoration: BoxDecoration(
        color: Color(0xFFF2F2F2), // Set the background color to white
      ),
      child: child,
    );
  }
}