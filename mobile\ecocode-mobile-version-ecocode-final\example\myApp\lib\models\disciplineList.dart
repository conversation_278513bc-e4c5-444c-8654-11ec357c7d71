import 'package:NovaSchool/models/disciplineEntityResponse.dart';

class Disciplinelist {
  final List<DisciplineEntityResponse> content;
  final int totalPages;
  final int totalElements;

  Disciplinelist({
    required this.content,
    required this.totalPages,
    required this.totalElements,
  });

  factory Disciplinelist.fromJson(Map<String, dynamic> json) {
    var list = json['content'] as List;
    List<DisciplineEntityResponse> contentList =
        list.map((i) => DisciplineEntityResponse.fromJson(i)).toList();

    return Disciplinelist(
      content: contentList,
      totalPages: json['totalPages'],
      totalElements: json['totalElements'],
    );
  }
}
