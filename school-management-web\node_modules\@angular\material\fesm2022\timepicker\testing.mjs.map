{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/testing/timepicker-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/testing/timepicker-input-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/timepicker/testing/timepicker-toggle-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {MatOptionHarness, OptionHarnessFilters} from '../../core/testing';\nimport {TimepickerHarnessFilters} from './timepicker-harness-filters';\n\nexport class MatTimepickerHarness extends ComponentHarness {\n  private _documentRootLocator = this.documentRootLocatorFactory();\n  static hostSelector = 'mat-timepicker';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a timepicker with specific\n   * attributes.\n   * @param options Options for filtering which timepicker instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTimepickerHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TimepickerHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Whether the timepicker is open. */\n  async isOpen(): Promise<boolean> {\n    const selector = await this._getPanelSelector();\n    const panel = await this._documentRootLocator.locatorForOptional(selector)();\n    return panel !== null;\n  }\n\n  /** Gets the options inside the timepicker panel. */\n  async getOptions(filters?: Omit<OptionHarnessFilters, 'ancestor'>): Promise<MatOptionHarness[]> {\n    if (!(await this.isOpen())) {\n      throw new Error('Unable to retrieve options for timepicker. Timepicker panel is closed.');\n    }\n\n    return this._documentRootLocator.locatorForAll(\n      MatOptionHarness.with({\n        ...(filters || {}),\n        ancestor: await this._getPanelSelector(),\n      } as OptionHarnessFilters),\n    )();\n  }\n\n  /** Selects the first option matching the given filters. */\n  async selectOption(filters: OptionHarnessFilters): Promise<void> {\n    const options = await this.getOptions(filters);\n    if (!options.length) {\n      throw Error(`Could not find a mat-option matching ${JSON.stringify(filters)}`);\n    }\n    await options[0].click();\n  }\n\n  /** Gets the selector that can be used to find the timepicker's panel. */\n  protected async _getPanelSelector(): Promise<string> {\n    return `#${await (await this.host()).getAttribute('mat-timepicker-panel-id')}`;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  TestKey,\n} from '@angular/cdk/testing';\nimport {MatTimepickerHarness} from './timepicker-harness';\nimport {\n  TimepickerHarnessFilters,\n  TimepickerInputHarnessFilters,\n} from './timepicker-harness-filters';\n\n/** Harness for interacting with a standard Material timepicker inputs in tests. */\nexport class MatTimepickerInputHarness extends ComponentHarness {\n  private _documentRootLocator = this.documentRootLocatorFactory();\n  static hostSelector = '.mat-timepicker-input';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatTimepickerInputHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTimepickerInputHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TimepickerInputHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('value', options.value, (harness, value) => {\n        return HarnessPredicate.stringMatches(harness.getValue(), value);\n      })\n      .addOption('placeholder', options.placeholder, (harness, placeholder) => {\n        return HarnessPredicate.stringMatches(harness.getPlaceholder(), placeholder);\n      });\n  }\n\n  /** Gets whether the timepicker associated with the input is open. */\n  async isTimepickerOpen(): Promise<boolean> {\n    const host = await this.host();\n    return (await host.getAttribute('aria-expanded')) === 'true';\n  }\n\n  /** Opens the timepicker associated with the input and returns the timepicker instance. */\n  async openTimepicker(): Promise<MatTimepickerHarness> {\n    if (!(await this.isDisabled())) {\n      const host = await this.host();\n      await host.sendKeys(TestKey.DOWN_ARROW);\n    }\n\n    return this.getTimepicker();\n  }\n\n  /** Closes the timepicker associated with the input. */\n  async closeTimepicker(): Promise<void> {\n    await this._documentRootLocator.rootElement.click();\n\n    // This is necessary so that we wait for the closing animation.\n    await this.forceStabilize();\n  }\n\n  /**\n   * Gets the `MatTimepickerHarness` that is associated with the input.\n   * @param filter Optionally filters which timepicker is included.\n   */\n  async getTimepicker(filter: TimepickerHarnessFilters = {}): Promise<MatTimepickerHarness> {\n    const host = await this.host();\n    const timepickerId = await host.getAttribute('mat-timepicker-id');\n\n    if (!timepickerId) {\n      throw Error('Element is not associated with a timepicker');\n    }\n\n    return this._documentRootLocator.locatorFor(\n      MatTimepickerHarness.with({\n        ...filter,\n        selector: `[mat-timepicker-panel-id=\"${timepickerId}\"]`,\n      }),\n    )();\n  }\n\n  /** Whether the input is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Whether the input is required. */\n  async isRequired(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('required');\n  }\n\n  /** Gets the value of the input. */\n  async getValue(): Promise<string> {\n    // The \"value\" property of the native input is always defined.\n    return await (await this.host()).getProperty<string>('value');\n  }\n\n  /**\n   * Sets the value of the input. The value will be set by simulating\n   * keypresses that correspond to the given value.\n   */\n  async setValue(newValue: string): Promise<void> {\n    const inputEl = await this.host();\n    await inputEl.clear();\n\n    // We don't want to send keys for the value if the value is an empty\n    // string in order to clear the value. Sending keys with an empty string\n    // still results in unnecessary focus events.\n    if (newValue) {\n      await inputEl.sendKeys(newValue);\n    }\n  }\n\n  /** Gets the placeholder of the input. */\n  async getPlaceholder(): Promise<string> {\n    return await (await this.host()).getProperty<string>('placeholder');\n  }\n\n  /**\n   * Focuses the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /**\n   * Blurs the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the input is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {TimepickerToggleHarnessFilters} from './timepicker-harness-filters';\n\n/** <PERSON><PERSON><PERSON> for interacting with a standard Material timepicker toggle in tests. */\nexport class MatTimepickerToggleHarness extends ComponentHarness {\n  static hostSelector = '.mat-timepicker-toggle';\n\n  /** The clickable button inside the toggle. */\n  private _button = this.locatorFor('button');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatTimepickerToggleHarness` that\n   * meets certain criteria.\n   * @param options Options for filtering which timepicker toggle instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: TimepickerToggleHarnessFilters = {},\n  ): HarnessPredicate<MatTimepickerToggleHarness> {\n    return new HarnessPredicate(MatTimepickerToggleHarness, options);\n  }\n\n  /** Opens the timepicker associated with the toggle. */\n  async openTimepicker(): Promise<void> {\n    const isOpen = await this.isTimepickerOpen();\n\n    if (!isOpen) {\n      const button = await this._button();\n      await button.click();\n    }\n  }\n\n  /** Gets whether the timepicker associated with the toggle is open. */\n  async isTimepickerOpen(): Promise<boolean> {\n    const button = await this._button();\n    const ariaExpanded = await button.getAttribute('aria-expanded');\n    return ariaExpanded === 'true';\n  }\n\n  /** Whether the toggle is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const button = await this._button();\n    return coerceBooleanProperty(await button.getAttribute('disabled'));\n  }\n}\n"], "names": [], "mappings": ";;;;AAgBM,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;AAChD,IAAA,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,EAAE;AAChE,IAAA,OAAO,YAAY,GAAG,gBAAgB;AAEtC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAoC,EAAE,EAAA;AAEtC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;;;AAI5C,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE;AAC/C,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;QAC5E,OAAO,KAAK,KAAK,IAAI;;;IAIvB,MAAM,UAAU,CAAC,OAAgD,EAAA;QAC/D,IAAI,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC;;QAG3F,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAC5C,gBAAgB,CAAC,IAAI,CAAC;AACpB,YAAA,IAAI,OAAO,IAAI,EAAE,CAAC;AAClB,YAAA,QAAQ,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;SACjB,CAAC,CAC3B,EAAE;;;IAIL,MAAM,YAAY,CAAC,OAA6B,EAAA;QAC9C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AAC9C,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,MAAM,KAAK,CAAC,CAAA,qCAAA,EAAwC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAE,CAAA,CAAC;;AAEhF,QAAA,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;;AAIhB,IAAA,MAAM,iBAAiB,GAAA;AAC/B,QAAA,OAAO,CAAI,CAAA,EAAA,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,yBAAyB,CAAC,EAAE;;;;AC7ClF;AACM,MAAO,yBAA0B,SAAQ,gBAAgB,CAAA;AACrD,IAAA,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,EAAE;AAChE,IAAA,OAAO,YAAY,GAAG,uBAAuB;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAyC,EAAE,EAAA;AAE3C,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;AACtC,aAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAAI;YACpD,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAClE,SAAC;AACA,aAAA,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,WAAW,KAAI;YACtE,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC;AAC9E,SAAC,CAAC;;;AAIN,IAAA,MAAM,gBAAgB,GAAA;AACpB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,OAAO,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;;AAI9D,IAAA,MAAM,cAAc,GAAA;QAClB,IAAI,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;AAC9B,YAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;YAC9B,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC;;AAGzC,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE;;;AAI7B,IAAA,MAAM,eAAe,GAAA;QACnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,KAAK,EAAE;;AAGnD,QAAA,MAAM,IAAI,CAAC,cAAc,EAAE;;AAG7B;;;AAGG;AACH,IAAA,MAAM,aAAa,CAAC,MAAA,GAAmC,EAAE,EAAA;AACvD,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC;QAEjE,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,MAAM,KAAK,CAAC,6CAA6C,CAAC;;QAG5D,OAAO,IAAI,CAAC,oBAAoB,CAAC,UAAU,CACzC,oBAAoB,CAAC,IAAI,CAAC;AACxB,YAAA,GAAG,MAAM;YACT,QAAQ,EAAE,CAA6B,0BAAA,EAAA,YAAY,CAAI,EAAA,CAAA;SACxD,CAAC,CACH,EAAE;;;AAIL,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,QAAQ,GAAA;;AAEZ,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC;;AAG/D;;;AAGG;IACH,MAAM,QAAQ,CAAC,QAAgB,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AACjC,QAAA,MAAM,OAAO,CAAC,KAAK,EAAE;;;;QAKrB,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;;;;AAKpC,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,aAAa,CAAC;;AAGrE;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;AAGpC;;;AAGG;AACH,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;;ACnI1C;AACM,MAAO,0BAA2B,SAAQ,gBAAgB,CAAA;AAC9D,IAAA,OAAO,YAAY,GAAG,wBAAwB;;AAGtC,IAAA,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;AAE3C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAA0C,EAAE,EAAA;AAE5C,QAAA,OAAO,IAAI,gBAAgB,CAAC,0BAA0B,EAAE,OAAO,CAAC;;;AAIlE,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE;QAE5C,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;AACnC,YAAA,MAAM,MAAM,CAAC,KAAK,EAAE;;;;AAKxB,IAAA,MAAM,gBAAgB,GAAA;AACpB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;QACnC,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC;QAC/D,OAAO,YAAY,KAAK,MAAM;;;AAIhC,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;QACnC,OAAO,qBAAqB,CAAC,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;;;;;;"}