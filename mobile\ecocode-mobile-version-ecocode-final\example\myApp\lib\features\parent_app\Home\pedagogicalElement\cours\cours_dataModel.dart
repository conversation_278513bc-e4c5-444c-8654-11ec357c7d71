class CoursData {
  final String title;
  final String description;
  final String date;
  final String fichier;
  bool showDetails;

  CoursData({
    required this.title,
    required this.description,
    required this.date,
    required this.fichier,
    this.showDetails = false,
  });

  factory CoursData.fromJson(Map<String, dynamic> json) {
    return CoursData(
      title: json['title'],
      description: json['description'],
      date: json['date'],
      fichier: json['fichier'],
    );
  }
}