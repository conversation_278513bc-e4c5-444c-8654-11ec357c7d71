import 'dart:async';
import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/models/messageResponse.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import '../../../../../commun/parent_app/app_secondheadBar.dart';
import 'exercice_details.dart';
import 'package:get/get.dart';

class PExerciceScreen extends StatefulWidget {
  final Function(String) onMessageRead;
  const PExerciceScreen({Key? key, required this.onMessageRead})
      : super(key: key);

  @override
  _ExerciceScreenState createState() => _ExerciceScreenState();
}

class _ExerciceScreenState extends State<PExerciceScreen> {
  List<MessageResponse> exercices = [];
  int _currentPageIndex = 0;
  int _itemsPerPage = 0;
  bool _isLoading = true;
  final int _pageSize = 5;
  int _totalPages = 0;
  ScrollController _scrollController = ScrollController();
  MessageServices msg = MessageServices();
  AuthService auth = AuthService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateItemsPerPage();
    });
    _fetchExercices();
  }

  Future<void> _fetchExercices({int page = 0}) async {
    setState(() {
      _isLoading = true;
    });
    List<MessageResponse> fetchedExercices = await msg.fetchMessagesForParent(
      type: "Exercices",
      page: page,
      pageSize: _pageSize,
      isReceiver: true,
    );

    if (mounted) {
      setState(() {
        exercices = fetchedExercices;
        _totalPages = (fetchedExercices.length / _pageSize).ceil();
        _isLoading = false;
      });
    }
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true;
      });
      _fetchExercices(page: newIndex);
    }
  }

  Future<void> _readMessage(MessageResponse exercices) async {
    await msg.markMessageAsRead(exercices);
    setState(() {
      exercices.read = true;
    });
    widget.onMessageRead('Exercices');
  }

  Future<void> _handleMessageTap(BuildContext context,
      MessageResponse exercices, String messageType) async {
    if (!exercices.read) {
      await _readMessage(exercices);
    }

    await msg.navigateToMessageDetail(context, exercices, messageType);
  }

  @override
  Widget build(BuildContext context) {
    List<MessageResponse> currentPageExercices = exercices.isEmpty
        ? []
        : exercices
            .skip(_currentPageIndex * _pageSize)
            .take(_pageSize)
            .toList();

    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color: Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<PNavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => ParentAppNavBar());
              },
              child: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      /*body: AppBackground(
        child: Column(
          children: [
            Container(
              child: Column(
                children: [
                  /* PrimaryHeaderContainer(
                    child: Container(
                      height: 100, // Adjust the height here
                      child: SecondHeadBar(
                        title: 'Exercices',
                        titleColor: Colors.white,
                        iconColor: Colors.white,
                      ),
                    ),
                  ),*/
                  Padding(
                    padding: EdgeInsets.all(15.0),
                    child: Center(
                      child: Container(
                        padding: EdgeInsets.all(10.0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15.0),
                        ),
                        child: ClipRRect(
                            borderRadius: BorderRadius.circular(10.0),
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: Tooltip(
                                  message: 'Retour',
                                  child: IconButton(
                                    onPressed: () => Get.back(),
                                    icon: Icon(Icons.arrow_back_ios,
                                        color: Colors.blueGrey),
                                  ),
                                ),
                              ),
                              /*child: */ Image.asset(
                                AppImages.exercice,
                                width: 200,
                                height: 100,
                                fit: BoxFit.contain,
                              ),
                            ],),
                      ),
                    ),
                  ),
                ],
              ),
            ),*/
      body: AppBackground(
        child: Column(
          children: [
            // Conteneur haut avec bouton retour + image sur la même ligne
            Padding(
              padding: EdgeInsets.all(15.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Tooltip(
                    message: 'Retour',
                    child: IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Icons.arrow_back_ios, color: Colors.blueGrey),
                    ),
                  ),
                  SizedBox(width: 35), // Espacement entre l'icône et l'image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: Image.asset(
                      AppImages.exercice,
                      width: 200,
                      height: 100,
                      fit: BoxFit.contain,
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                      height: 510,
                      child: exercices.isEmpty
                          ? Center(
                              child: Text("Aucun exercice trouvé.",
                                  style: TextStyle(
                                      fontSize: 18, color: Colors.grey)))
                          : RefreshIndicator(
                              onRefresh: () => _fetchExercices(),
                              child: ListView.builder(
                                shrinkWrap: true,
                                padding: EdgeInsets.zero,
                                controller: _scrollController,
                                itemCount: currentPageExercices.length +
                                    1, // Add 1 for the pagination button
                                itemBuilder: (context, index) {
                                  if (index < currentPageExercices.length) {
                                    MessageResponse exercices =
                                        currentPageExercices[index];

                                    return GestureDetector(
                                      onTap: () async {
                                        await _handleMessageTap(
                                            context, exercices, 'Exercices');
                                      },
                                      child: Column(
                                        children: [
                                          SizedBox(height: 8.0),
                                          Container(
                                            padding: EdgeInsets.all(14.0),
                                            margin: EdgeInsets.only(
                                                bottom: 10.0,
                                                left: 10.0,
                                                right: 10.0),
                                            decoration: BoxDecoration(
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.5),
                                                  spreadRadius: 0,
                                                  blurRadius: 5,
                                                  offset: Offset(0, 1),
                                                ),
                                              ],
                                              color: exercices.read
                                                  ? Colors.white
                                                  : Color(0xFFE1F5FE),
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        exercices.messageEntity
                                                            .objet,
                                                        style: TextStyle(
                                                          fontSize: 18.0,
                                                          color:
                                                              Color(0xFF4099FF),
                                                          fontWeight: exercices
                                                                  .read
                                                              ? FontWeight
                                                                  .normal
                                                              : FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                    Tooltip(
                                                      message: 'Details',
                                                      child: Icon(Icons.info),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 3.0),
                                                Text(
                                                  "Date: ${exercices.messageEntity.dateEnvoie.toLocal().add(Duration(hours: 1)).toString().split(' ')[0]}",
                                                  style: TextStyle(
                                                      color: Colors.grey),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    return Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          IconButton(
                                            icon: Icon(Icons.arrow_back_ios,
                                                color: _currentPageIndex > 0
                                                    ? Color(0xFF4099FF)
                                                    : Colors.grey,
                                                size: 20),
                                            onPressed: _currentPageIndex > 0
                                                ? () {
                                                    _updatePageIndex(
                                                        _currentPageIndex - 1);
                                                  }
                                                : null,
                                          ),
                                          Text(
                                            '${_currentPageIndex + 1} / $_totalPages',
                                            style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: 15),
                                          ),
                                          IconButton(
                                            icon: Icon(Icons.arrow_forward_ios,
                                                color: _currentPageIndex <
                                                        _totalPages - 1
                                                    ? Color(0xFF4099FF)
                                                    : Colors.grey,
                                                size: 20),
                                            onPressed: _currentPageIndex <
                                                    _totalPages - 1
                                                ? () {
                                                    _updatePageIndex(
                                                        _currentPageIndex + 1);
                                                  }
                                                : null,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                },
                              ),
                            ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
