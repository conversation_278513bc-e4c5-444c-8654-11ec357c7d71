import 'package:flutter/material.dart';
import '../../../../../utils/constants/colors.dart';

class TPaginationButtons extends StatelessWidget {
  final int currentPageIndex;
  final int totalPages;
  final Function(int) onPageChanged;

  const TPaginationButtons({
    required this.currentPageIndex,
    required this.totalPages,
    required this.onPageChanged,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10.0),
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildPageNumbers(),
        ],
      ),
    );
  }

  Widget _buildPageNumbers() {
    List<Widget> buttons = [];

    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      buttons.add(
        InkWell(
          onTap: () {
            onPageChanged(pageIndex);
          },
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 5.0),
            width: 25.0,
            height: 25.0,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: pageIndex == currentPageIndex ? Color(0xFF4099FF) : Colors.grey.withOpacity(0.5),
            ),
            alignment: Alignment.center,
            child: Text(
              '${pageIndex + 1}',
              style: TextStyle(
                color: pageIndex == currentPageIndex ? Colors.white : Colors.black,
              ),
            ),
          ),
        ),
      );
    }

    return Row(
      children: buttons,
    );
  }
}
