@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, dialog);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // Border radius of the container.
    container-shape: 4px,
    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    container-elevation: null,
    container-shadow-color: null,
    with-divider-divider-height: null,
    with-divider-divider-color: null,
    with-icon-icon-size: null,
    with-icon-icon-color: null,
    action-label-text-font: null,
    action-label-text-line-height: null,
    action-label-text-size: null,
    action-label-text-weight: null,
    action-label-text-tracking: null,
    action-label-text-color: null,
    action-hover-state-layer-color: null,
    action-hover-state-layer-opacity: null,
    action-hover-label-text-color: null,
    action-focus-state-layer-color: null,
    action-focus-state-layer-opacity: null,
    action-focus-label-text-color: null,
    action-pressed-state-layer-color: null,
    action-pressed-state-layer-opacity: null,
    action-pressed-label-text-color: null,
    headline-color: null,
    headline-font: null,
    headline-line-height: null,
    headline-size: null,
    headline-tracking: null,
    headline-weight: null,
    z-index: null,
    container-surface-tint-layer-color: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;

  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  $text-base: if(token-definition.$private-is-internal-build,
    if($is-dark, #fff, #000),
    inspection.get-theme-color($theme, foreground, text, 1)
  );

  @return (
    // Background color of the container.
    container-color: inspection.get-theme-color($theme, background, dialog),
    // Color of the dialog header.
    subhead-color: sass-utils.safe-color-change($text-base, $alpha: 0.87),
    // Color of the dialog body text.
    supporting-text-color: sass-utils.safe-color-change($text-base, $alpha: 0.6),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    // Typography of the dialog header.
    subhead-font: inspection.get-theme-typography($theme, headline-6, font-family),
    subhead-line-height: inspection.get-theme-typography($theme, headline-6, line-height),
    subhead-size: inspection.get-theme-typography($theme, headline-6, font-size),
    subhead-weight: inspection.get-theme-typography($theme, headline-6, font-weight),
    subhead-tracking: inspection.get-theme-typography($theme, headline-6, letter-spacing),
    // Typography of the dialog body text.
    supporting-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    supporting-text-line-height: inspection.get-theme-typography($theme, body-1, line-height),
    supporting-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    supporting-text-weight: inspection.get-theme-typography($theme, body-1, font-weight),
    supporting-text-tracking: inspection.get-theme-typography($theme, body-1, letter-spacing),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
