import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/features/parent_app/Home/home_PHeadBar.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/app_background.dart';
//import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:NovaSchool/services/auth_service.dart';

class PContactScreen extends StatelessWidget {
  const PContactScreen({Key? key}) : super(key: key);

  // Helper function to open URLs
  Future<void> _launchUrl(String url) async {
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  // Helper function to call phone numbers
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri callUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunch(callUri.toString())) {
      await launch(callUri.toString());
    } else {
      throw 'Could not call $phoneNumber';
    }
  }

  @override
  Widget build(BuildContext context) {

    String facebookUrl = dotenv.get('facebookUrl');
    String nameFacebook = dotenv.get('nameFacebook');
    String instagramUrl = dotenv.get('instagramUrl');
    String nameInstagram = dotenv.get('nameInstagram');
    String youtubeUrl = dotenv.get('youtubeUrl');
    String adresse1 = dotenv.get('adresse1');
    String adresse2 = dotenv.get('adresse2');
    String tel1 = dotenv.get('tel1');
    String tel2 = dotenv.get('tel2');
    String tel3 = dotenv.get('tel3');
    String tel4 = dotenv.get('tel4');
    String mail = dotenv.get('mail');

    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: Image.asset(
              'assets/logos/ecocode.png', // Remplace avec ton chemin
              height: 150, // Ajuste la taille
              color: Colors
                  .white, // Applique du blanc au logo (si PNG avec transparence)
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: Column(
        children: [
        /*SizedBox(
        height: 100, // Fixe la hauteur de ton header
        child: PHeadHomeScreen(),
    ),*/
    Expanded(
    child: SingleChildScrollView(
    child: Column(
    children: [
    const SizedBox(height: 2),

              // Card for social media links
              Card(
                color: Colors.white,
                elevation: 5,
                margin:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15)),
                child: Padding(

                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Nos liens sociaux',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 10),

                      // Facebook link
                      if (facebookUrl != null && facebookUrl.isNotEmpty && 
                          nameFacebook != null && nameFacebook.isNotEmpty)
                        GestureDetector(
                          onTap: () => _launchUrl(facebookUrl),
                          child: Row(
                            children: [
                              const Icon(Icons.facebook, color: Colors.blue, size: 30),
                              const SizedBox(width: 10),
                              Text('Facebook: $nameFacebook', style: const TextStyle(fontSize: 16)),
                            ],
                          ),
                        ),
                      const SizedBox(height: 10),

                      // Instagram link
                       if (instagramUrl != null && instagramUrl.isNotEmpty && 
                          nameInstagram != null && nameInstagram.isNotEmpty)
                        GestureDetector(
                          onTap: () => _launchUrl(instagramUrl),
                          child: Row(
                            children: [
                              const Icon(Icons.camera_alt, color: Colors.purple, size: 30),
                              const SizedBox(width: 10),
                              Text('Instagram: $nameInstagram', style: const TextStyle(fontSize: 16)),
                            ],
                          ),
                        ),
                         const SizedBox(height: 10),

                      // Youtube link (check for youtubeUrl)
                      if (youtubeUrl != null && youtubeUrl.isNotEmpty)
                        GestureDetector(
                          onTap: () => _launchUrl(youtubeUrl),
                          child: Row(
                            children: [
                              const Icon(Icons.play_circle_filled, color: Colors.red, size: 30),
                              const SizedBox(width: 10),
                              const Text('YouTube', style: TextStyle(fontSize: 16)),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // Card for address and phone numbers
              Card(
                color: Colors.white,
                elevation: 5,
                margin:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15)),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Adresse',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        '🏫 $adresse1',
                        style: TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 5),
                       if (adresse2 != null && adresse2.isNotEmpty)
                        Text(
                          '🏫 $adresse2',
                          style: const TextStyle(fontSize: 16),
                        ),
                      const SizedBox(height: 20),
                      const Text(
                        'Numéros de téléphone',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          // First two phone numbers on the left
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (tel1 != null && tel1.isNotEmpty)
                                  GestureDetector(
                                    onTap: () => _makePhoneCall(tel1),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.phone,
                                            color: Colors.green, size: 30),
                                        const SizedBox(width: 10),
                                        Text(tel1,
                                            style:
                                                const TextStyle(fontSize: 16)),
                                      ],
                                    ),
                                  ),
                                const SizedBox(height: 10),
                                if (tel2 != null && tel2.isNotEmpty)
                                  GestureDetector(
                                    onTap: () => _makePhoneCall(tel2),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.phone,
                                            color: Colors.green, size: 30),
                                        const SizedBox(width: 10),
                                        Text(tel2,
                                            style:
                                                const TextStyle(fontSize: 16)),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          // Spacer to push the right column to the end
                          const SizedBox(width: 20),
                          // Last two phone numbers on the right
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                if (tel3 != null && tel3.isNotEmpty)
                                  GestureDetector(
                                    onTap: () => _makePhoneCall(tel3),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        const Icon(Icons.phone,
                                            color: Colors.green, size: 30),
                                        const SizedBox(width: 10),
                                        Text(tel3,
                                            style:
                                                const TextStyle(fontSize: 16)),
                                      ],
                                    ),
                                  ),
                                const SizedBox(height: 10),
                                if (tel4 != null && tel4.isNotEmpty)
                                  GestureDetector(
                                    onTap: () => _makePhoneCall(tel4),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        const Icon(Icons.phone,
                                            color: Colors.green, size: 30),
                                        const SizedBox(width: 10),
                                        Text(tel4,
                                            style:
                                                const TextStyle(fontSize: 16)),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Card for email contact
              Card(
                color: Colors.white,
                elevation: 5,
                margin:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15)),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Contactez-nous par e-Mail',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 10),

                      // Email
                     if (mail != null && mail.isNotEmpty)
                        GestureDetector(
                          onTap: () => _launchUrl('mailto:$mail'),
                          child: Row(
                            children: [
                              const Icon(Icons.email, color: Colors.red, size: 30),
                              const SizedBox(width: 10),
                              Text(mail, style: const TextStyle(fontSize: 16)),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ],),),);
  }
}
