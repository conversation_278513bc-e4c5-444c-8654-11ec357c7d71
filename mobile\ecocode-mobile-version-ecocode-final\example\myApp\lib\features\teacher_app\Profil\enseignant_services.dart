import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:NovaSchool/features/teacher_app/Profil/enseignant_class.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../commun/base_url.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnseignantService {
  // Obtenir le profil d'un enseignant
  Future<Enseignant> getProfilEnseignant(int enseignantId) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/enseignants/$enseignantId/profil';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String token = prefs.getString('token') ?? '';

    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = json.decode(response.body);
        return Enseignant.fromJson(responseData);
      } else {
        throw Exception('Failed to get profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get profile: $e');
    }
  }

  // Récupérer tous les enseignants
  // Future<List<Enseignant>> getAllEnseignants() async {
  //   final String apiUrl = '$baseURL/enseignant/all?page=0&size=10';
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   String token = prefs.getString('token') ?? '';

  //   try {
  //     final response = await http.get(
  //       Uri.parse(apiUrl),
  //       headers: <String, String>{
  //         'Authorization': 'Bearer $token',
  //         'Content-Type': 'application/json; charset=UTF-8',
  //       },
  //     );

  //     if (response.statusCode == 200) {
  //       // Parse the response body as a Map
  //       Map<String, dynamic> jsonResponse = json.decode(response.body);

  //       // Extract the 'content' list from the jsonResponse
  //       List<dynamic> content = jsonResponse['content'];

  //       // Map the content list to a list of Enseignant objects
  //       List<Enseignant> enseignants =
  //           content.map((data) => Enseignant.fromJson(data)).toList();

  //       return enseignants;
  //     } else {
  //       throw Exception(
  //           'Failed to get all enseignants: ${response.statusCode}');
  //     }
  //   } catch (e) {
  //     throw Exception('Failed to get all enseignants: $e');
  //   }
  // }
  // Future<Map<String, dynamic>> getEnseignantsByPage(int page, int size) async {
  //   final String apiUrl = '$baseURL/enseignant/all?page=$page&size=$size';
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   String token = prefs.getString('token') ?? '';

  //   try {
  //     final response = await http.get(
  //       Uri.parse(apiUrl),
  //       headers: <String, String>{
  //         'Authorization': 'Bearer $token',
  //         'Content-Type': 'application/json; charset=UTF-8',
  //       },
  //     );

  //     if (response.statusCode == 200) {
  //       // Parse the response body as a Map
  //       Map<String, dynamic> jsonResponse = json.decode(response.body);

  //       // Return the entire jsonResponse to handle pagination
  //       return jsonResponse;
  //     } else {
  //       throw Exception('Failed to get enseignants: ${response.statusCode}');
  //     }
  //   } catch (e) {
  //     throw Exception('Failed to get enseignants: $e');
  //   }
  // }

  Future<Map<String, dynamic>> getEnseignantsByPage(int page, int size) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/enseignant/all?page=$page&size=$size';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String token = prefs.getString('token') ?? '';

    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 200) {
        // Parse the response body as a Map
        Map<String, dynamic> jsonResponse = json.decode(response.body);

        // Return the entire jsonResponse to handle pagination
        return jsonResponse;
      } else {
        throw Exception('Failed to get enseignants: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get enseignants: $e');
    }
  }

  // Ajouter un nouvel enseignant
  // Future<void> addEnseignant(Enseignant enseignant) async {
  //   final String apiUrl = '$baseURL/enseignants/ajouterEnseignant';
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   String token = prefs.getString('token') ?? '';
  //   try {
  //     final response = await http.post(
  //       Uri.parse(apiUrl),
  //       headers: <String, String>{
  //         'Authorization': 'Bearer $token',
  //         'Content-Type': 'application/json; charset=UTF-8',
  //       },
  //       body: jsonEncode(enseignant.toJson()),
  //     );

  //     if (response.statusCode == 201) {
  //       // Enseignant ajouté avec succès
  //     } else {
  //       throw Exception('Failed to add enseignant: ${response.statusCode}');
  //     }
  //   } catch (e) {
  //     throw Exception('Failed to add enseignant: $e');
  //   }
  // }
  Future<void> addEnseignant(Enseignant enseignant) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/enseignant/add';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String token = prefs.getString('token') ?? '';

    try {
      var request = http.MultipartRequest('POST', Uri.parse(apiUrl))
        ..headers['Authorization'] = 'Bearer $token';

      // Add the EnseignantEntity as a string part
      request.fields['EnseignantEntity'] = jsonEncode(enseignant.toJson());

      final response = await request.send();

      if (response.statusCode == 200) {
        // enseignant added successfully
      } else {
        throw Exception('Failed to add enseignant: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to add enseignant: $e');
    }
  }

  // Mettre à jour le profil d'un enseignant
  Future<void> updateProfilEnseignant(Enseignant enseignant) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl =
        '$baseURL/enseignants/${enseignant.idEnseignant}/profil';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String token = prefs.getString('token') ?? '';
    try {
      final response = await http.put(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(enseignant.toJson()),
      );

      if (response.statusCode == 200) {
        // Profil de l'enseignant mis à jour avec succès
      } else {
        throw Exception('Failed to update profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to update profile: $e');
    }
  }

  // Supprimer le profil d'un enseignant
  Future<void> deleteProfilEnseignant(int enseignantId, String token) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/enseignants/$enseignantId/profil';

    try {
      final response = await http.delete(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 200) {
        // Profil de l'enseignant supprimé avec succès
      } else {
        throw Exception('Failed to delete profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to delete profile: $e');
    }
  }
}












// enum Sexe { Homme, Femme }

// enum Contrat { SansContrat, CIVP, CDD, CDI, ContratKARAMA, Vacataire, Autre }

// class Enseignant {
//   final int? idEnseignant;
//   final String? nomEnseignant;
//   final String? prenomEnseignant;
//   final String? nomArabeEnseignant;
//   final String? cinEnseignant;
//   final String? numeroTelephoneEnseignant;
//   final Contrat? typeContratEnseignant;
//   final String? matriculeEnseignant;
//   final String? dateEmbaucheEnseignant;
//   final Sexe? sexeEnseignant;
//   final bool? chefFamille;
//   final int? nombreHeurEnseignant;
//   final String? dateDebutContratEnseignant;
//   final String? dateFinContratEnseignant;

//   Enseignant({
//     this.idEnseignant,
//     this.nomEnseignant,
//     this.prenomEnseignant,
//     this.nomArabeEnseignant,
//     this.cinEnseignant,
//     this.numeroTelephoneEnseignant,
//     this.typeContratEnseignant,
//     this.matriculeEnseignant,
//     this.dateEmbaucheEnseignant,
//     this.sexeEnseignant,
//     this.chefFamille = false,
//     this.nombreHeurEnseignant = 25,
//     this.dateDebutContratEnseignant,
//     this.dateFinContratEnseignant,
//   });

//   factory Enseignant.fromJson(Map<String, dynamic> json) {
//     return Enseignant(
//       idEnseignant: json['idEnseignant'],
//       nomEnseignant: json['nomEnseignant'],
//       prenomEnseignant: json['prenomEnseignant'],
//       nomArabeEnseignant: json['nomArabeEnseignant'],
//       cinEnseignant: json['cinEnseignant'],
//       numeroTelephoneEnseignant: json['numeroTelephoneEnseignant'],
//       matriculeEnseignant: json['matriculeEnseignant'],
//       dateEmbaucheEnseignant: json['dateEmbaucheEnseignant'],
//       sexeEnseignant: _sexeFromString(json['sexeEnseignant']),
//       typeContratEnseignant: _contratFromString(json['typeContratEnseignant']),
//       chefFamille: json['chefFamille'] ?? false,
//       nombreHeurEnseignant: json['nombreHeurEnseignant'] ?? 25,
//       dateDebutContratEnseignant: json['dateDebutContratEnseignant'],
//       dateFinContratEnseignant: json['dateFinContratEnseignant'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'idEnseignant': idEnseignant,
//       'nomEnseignant': nomEnseignant,
//       'prenomEnseignant': prenomEnseignant,
//       'nomArabeEnseignant': nomArabeEnseignant,
//       'cinEnseignant': cinEnseignant,
//       'numeroTelephoneEnseignant': numeroTelephoneEnseignant,
//       'matriculeEnseignant': matriculeEnseignant,
//       'dateEmbaucheEnseignant': dateEmbaucheEnseignant,
//       'sexeEnseignant': sexeEnseignant?.toString().split('.').last,
//       'typeContratEnseignant': typeContratEnseignant?.toString().split('.').last,
//       'chefFamille': chefFamille,
//       'nombreHeurEnseignant': nombreHeurEnseignant,
//       'dateDebutContratEnseignant': dateDebutContratEnseignant,
//       'dateFinContratEnseignant': dateFinContratEnseignant,
//     };
//   }

//   static Sexe? _sexeFromString(String? sexe) {
//     if (sexe == null) return null;
//     return Sexe.values.firstWhere(
//         (e) => e.toString().split('.').last.toLowerCase() == sexe.toLowerCase(),
//         orElse: () => Sexe.Homme); // Default to Homme if not found
//   }

//   static Contrat? _contratFromString(String? contrat) {
//     if (contrat == null) return null;
//     return Contrat.values.firstWhere(
//         (e) => e.toString().split('.').last.toLowerCase() == contrat.toLowerCase(),
//         orElse: () => Contrat.Autre); // Default to Autre if not found
//   }
// }
