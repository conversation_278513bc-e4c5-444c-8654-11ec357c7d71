import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'dart:ui' as ui;
import '../../utils/constants/colors.dart';
import '../../utils/constants/size.dart';
import '../../utils/constants/text.dart';
import '../../utils/device/devices_utility.dart';
import '../parent_app/appBar_config.dart';
import 'package:google_fonts/google_fonts.dart';

class AAppSecondHead extends StatelessWidget {
  const AAppSecondHead({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AAppBarConfig(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                AppText.Apptitle,
                style: GoogleFonts.dmSerifDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}

class AAppBarConfig extends StatelessWidget implements PreferredSizeWidget {
  const AAppBarConfig({
    Key? key,
    this.title,
    this.showBackArrow = false,
    this.leadingIcon,
    this.actions,
    this.leadingPressed,
  }) : super(key: key);

  final Widget? title;
  final bool? showBackArrow;
  final IconData? leadingIcon;
  final List<Widget>? actions;
  final VoidCallback? leadingPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: AppSize.md, left: 5, top: 0),
      child: AppBar(
        automaticallyImplyLeading: true,
        leading: showBackArrow!
            ? IconButton(
                onPressed: () => Get.back(),
                icon: const Icon(Iconsax.arrow_left))
            : leadingIcon != null
                ? IconButton(onPressed: leadingPressed, icon: Icon(leadingIcon))
                : null,
        title: title,
        actions: actions,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(DevicesUtility.getAppBarHeight());
}
