declare const MAC_ENTER = 3;
declare const BACKSPACE = 8;
declare const TAB = 9;
declare const NUM_CENTER = 12;
declare const ENTER = 13;
declare const SHIFT = 16;
declare const CONTROL = 17;
declare const ALT = 18;
declare const PAUSE = 19;
declare const CAPS_LOCK = 20;
declare const ESCAPE = 27;
declare const SPACE = 32;
declare const PAGE_UP = 33;
declare const PAGE_DOWN = 34;
declare const END = 35;
declare const HOME = 36;
declare const LEFT_ARROW = 37;
declare const UP_ARROW = 38;
declare const RIGHT_ARROW = 39;
declare const DOWN_ARROW = 40;
declare const PLUS_SIGN = 43;
declare const PRINT_SCREEN = 44;
declare const INSERT = 45;
declare const DELETE = 46;
declare const ZERO = 48;
declare const ONE = 49;
declare const TWO = 50;
declare const THREE = 51;
declare const FOUR = 52;
declare const FIVE = 53;
declare const SIX = 54;
declare const SEVEN = 55;
declare const EIGHT = 56;
declare const NINE = 57;
declare const FF_SEMICOLON = 59;
declare const FF_EQUALS = 61;
declare const QUESTION_MARK = 63;
declare const AT_SIGN = 64;
declare const A = 65;
declare const B = 66;
declare const C = 67;
declare const D = 68;
declare const E = 69;
declare const F = 70;
declare const G = 71;
declare const H = 72;
declare const I = 73;
declare const J = 74;
declare const K = 75;
declare const L = 76;
declare const M = 77;
declare const N = 78;
declare const O = 79;
declare const P = 80;
declare const Q = 81;
declare const R = 82;
declare const S = 83;
declare const T = 84;
declare const U = 85;
declare const V = 86;
declare const W = 87;
declare const X = 88;
declare const Y = 89;
declare const Z = 90;
declare const META = 91;
declare const MAC_WK_CMD_LEFT = 91;
declare const MAC_WK_CMD_RIGHT = 93;
declare const CONTEXT_MENU = 93;
declare const NUMPAD_ZERO = 96;
declare const NUMPAD_ONE = 97;
declare const NUMPAD_TWO = 98;
declare const NUMPAD_THREE = 99;
declare const NUMPAD_FOUR = 100;
declare const NUMPAD_FIVE = 101;
declare const NUMPAD_SIX = 102;
declare const NUMPAD_SEVEN = 103;
declare const NUMPAD_EIGHT = 104;
declare const NUMPAD_NINE = 105;
declare const NUMPAD_MULTIPLY = 106;
declare const NUMPAD_PLUS = 107;
declare const NUMPAD_MINUS = 109;
declare const NUMPAD_PERIOD = 110;
declare const NUMPAD_DIVIDE = 111;
declare const F1 = 112;
declare const F2 = 113;
declare const F3 = 114;
declare const F4 = 115;
declare const F5 = 116;
declare const F6 = 117;
declare const F7 = 118;
declare const F8 = 119;
declare const F9 = 120;
declare const F10 = 121;
declare const F11 = 122;
declare const F12 = 123;
declare const NUM_LOCK = 144;
declare const SCROLL_LOCK = 145;
declare const FIRST_MEDIA = 166;
declare const FF_MINUS = 173;
declare const MUTE = 173;
declare const VOLUME_DOWN = 174;
declare const VOLUME_UP = 175;
declare const FF_MUTE = 181;
declare const FF_VOLUME_DOWN = 182;
declare const LAST_MEDIA = 183;
declare const FF_VOLUME_UP = 183;
declare const SEMICOLON = 186;
declare const EQUALS = 187;
declare const COMMA = 188;
declare const DASH = 189;
declare const PERIOD = 190;
declare const SLASH = 191;
declare const APOSTROPHE = 192;
declare const TILDE = 192;
declare const OPEN_SQUARE_BRACKET = 219;
declare const BACKSLASH = 220;
declare const CLOSE_SQUARE_BRACKET = 221;
declare const SINGLE_QUOTE = 222;
declare const MAC_META = 224;

type ModifierKey = 'altKey' | 'shiftKey' | 'ctrlKey' | 'metaKey';
/**
 * Checks whether a modifier key is pressed.
 * @param event Event to be checked.
 */
declare function hasModifierKey(event: KeyboardEvent, ...modifiers: ModifierKey[]): boolean;

export { A, ALT, APOSTROPHE, AT_SIGN, B, BACKSLASH, BACKSPACE, C, CAPS_LOCK, CLOSE_SQUARE_BRACKET, COMMA, CONTEXT_MENU, CONTROL, D, DASH, DELETE, DOWN_ARROW, E, EIGHT, END, ENTER, EQUALS, ESCAPE, F, F1, F10, F11, F12, F2, F3, F4, F5, F6, F7, F8, F9, FF_EQUALS, FF_MINUS, FF_MUTE, FF_SEMICOLON, FF_VOLUME_DOWN, FF_VOLUME_UP, FIRST_MEDIA, FIVE, FOUR, G, H, HOME, I, INSERT, J, K, L, LAST_MEDIA, LEFT_ARROW, M, MAC_ENTER, MAC_META, MAC_WK_CMD_LEFT, MAC_WK_CMD_RIGHT, META, MUTE, N, NINE, NUMPAD_DIVIDE, NUMPAD_EIGHT, NUMPAD_FIVE, NUMPAD_FOUR, NUMPAD_MINUS, NUMPAD_MULTIPLY, NUMPAD_NINE, NUMPAD_ONE, NUMPAD_PERIOD, NUMPAD_PLUS, NUMPAD_SEVEN, NUMPAD_SIX, NUMPAD_THREE, NUMPAD_TWO, NUMPAD_ZERO, NUM_CENTER, NUM_LOCK, O, ONE, OPEN_SQUARE_BRACKET, P, PAGE_DOWN, PAGE_UP, PAUSE, PERIOD, PLUS_SIGN, PRINT_SCREEN, Q, QUESTION_MARK, R, RIGHT_ARROW, S, SCROLL_LOCK, SEMICOLON, SEVEN, SHIFT, SINGLE_QUOTE, SIX, SLASH, SPACE, T, TAB, THREE, TILDE, TWO, U, UP_ARROW, V, VOLUME_DOWN, VOLUME_UP, W, X, Y, Z, ZERO, hasModifierKey };
export type { ModifierKey };
