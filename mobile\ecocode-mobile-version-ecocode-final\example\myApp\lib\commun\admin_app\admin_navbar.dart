import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/admin/profil/profil_Ascreen.dart';
import 'package:NovaSchool/features/chat/A_ChatScreen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ANavigationController extends GetxController {
  final Rx<int> selectedIndex = 0.obs;
  Map<String, String>? userInfo;

  ANavigationController({ this.userInfo});

  void changeTab(int index) => selectedIndex.value = index;
  void resetToHome() => selectedIndex.value = 0;

  List<Widget> get screens => [
    AHomeScreen(),
    AProfilScreen(
      firstName: userInfo?['senderUsername'] ?? '',
      lastName: userInfo?['senderUserSurname'] ?? '',
      email: userInfo?['email'] ?? '',
      profile: userInfo?['profile'] ?? '',
    ),
    AChatScreen(),
  ];
}
class AdminAppNavBar extends StatefulWidget {
  final int initialIndex;

  AdminAppNavBar({this.initialIndex = 0}); // par défaut à 0

  @override
  _AdminAppNavBarState createState() => _AdminAppNavBarState();
}

class _AdminAppNavBarState extends State<AdminAppNavBar> {

ANavigationController? controller;
  Map<String, String>? userInfo;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    try {
      userInfo = await AuthService().getSenderDetails();
      controller = Get.put(ANavigationController(userInfo: userInfo!), permanent: true);
      //
      controller!.changeTab(widget.initialIndex);
    } catch (e) {
      print("Erreur de chargement du profil: $e");
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (controller == null) {
      return Scaffold(body: Center(child: Text("Erreur de chargement du profil")));
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ModalRoute.of(context)?.isCurrent == true) {
        controller!.resetToHome();
      }
    });


    return WillPopScope(
      onWillPop: () async {
        if (controller!.selectedIndex.value != 0) {
          controller!.resetToHome();
          return false;
        }
        return true;
      },
      child: Scaffold(
        bottomNavigationBar: CurvedNavigationBar(
          backgroundColor: Color(0xFFF2F2F2),
          color: Colors.lightBlueAccent.shade400,
          buttonBackgroundColor: Colors.blueAccent.withOpacity(0.3),
          height: 65,
          animationDuration: Duration(milliseconds: 300),
          items: [
            Icon(Icons.home, size: 30, color: Colors.white),
            Icon(Icons.person, size: 30, color: Colors.white),
            Icon(Icons.chat, size: 30, color: Colors.white),
          ],
          onTap: (index) {
            controller!.changeTab(index);
          },
        ),
        body: Obx(() {
          final currentIndex = controller!.selectedIndex.value;
          return controller!.screens[currentIndex];
        }),
      ),
    );
  }
}