{"version": 3, "file": "table.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/tokens.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/cell.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/coalesced-style-scheduler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/row.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/sticky-styler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/table-errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/sticky-position-listener.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/table.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/text-column.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/table-module.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/src/cdk/table/can-stick.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/**\n * Used to provide a table to some of the sub-components without causing a circular dependency.\n * @docs-private\n */\nexport const CDK_TABLE = new InjectionToken<any>('CDK_TABLE');\n\n/** Configurable options for `CdkTextColumn`. */\nexport interface TextColumnOptions<T> {\n  /**\n   * Default function that provides the header text based on the column name if a header\n   * text is not provided.\n   */\n  defaultHeaderTextTransform?: (name: string) => string;\n\n  /** Default data accessor to use if one is not provided. */\n  defaultDataAccessor?: (data: T, name: string) => string;\n}\n\n/** Injection token that can be used to specify the text column options. */\nexport const TEXT_COLUMN_OPTIONS = new InjectionToken<TextColumnOptions<any>>(\n  'text-column-options',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ContentChild,\n  Directive,\n  ElementRef,\n  Input,\n  TemplateRef,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {CanStick} from './can-stick';\nimport {CDK_TABLE} from './tokens';\n\n/** Base interface for a cell definition. Captures a column's cell template definition. */\nexport interface CellDef {\n  template: TemplateRef<any>;\n}\n\n/**\n * Cell definition for a CDK table.\n * Captures the template of a column's data row cell as well as cell-specific properties.\n */\n@Directive({\n  selector: '[cdkCellDef]',\n})\nexport class CdkCellDef implements CellDef {\n  /** @docs-private */\n  template = inject<TemplateRef<any>>(TemplateRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n\n/**\n * Header cell definition for a CDK table.\n * Captures the template of a column's header cell and as well as cell-specific properties.\n */\n@Directive({\n  selector: '[cdkHeaderCellDef]',\n})\nexport class CdkHeaderCellDef implements CellDef {\n  /** @docs-private */\n  template = inject<TemplateRef<any>>(TemplateRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n\n/**\n * Footer cell definition for a CDK table.\n * Captures the template of a column's footer cell and as well as cell-specific properties.\n */\n@Directive({\n  selector: '[cdkFooterCellDef]',\n})\nexport class CdkFooterCellDef implements CellDef {\n  /** @docs-private */\n  template = inject<TemplateRef<any>>(TemplateRef);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n\n/**\n * Column definition for the CDK table.\n * Defines a set of cells available for a table column.\n */\n@Directive({\n  selector: '[cdkColumnDef]',\n  providers: [{provide: 'MAT_SORT_HEADER_COLUMN_DEF', useExisting: CdkColumnDef}],\n})\nexport class CdkColumnDef implements CanStick {\n  _table? = inject(CDK_TABLE, {optional: true});\n\n  private _hasStickyChanged = false;\n\n  /** Unique name for this column. */\n  @Input('cdkColumnDef')\n  get name(): string {\n    return this._name;\n  }\n  set name(name: string) {\n    this._setNameInput(name);\n  }\n  protected _name: string;\n\n  /** Whether the cell is sticky. */\n  @Input({transform: booleanAttribute})\n  get sticky(): boolean {\n    return this._sticky;\n  }\n  set sticky(value: boolean) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  private _sticky = false;\n\n  /**\n   * Whether this column should be sticky positioned on the end of the row. Should make sure\n   * that it mimics the `CanStick` mixin such that `_hasStickyChanged` is set to true if the value\n   * has been changed.\n   */\n  @Input({transform: booleanAttribute})\n  get stickyEnd(): boolean {\n    return this._stickyEnd;\n  }\n  set stickyEnd(value: boolean) {\n    if (value !== this._stickyEnd) {\n      this._stickyEnd = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  _stickyEnd: boolean = false;\n\n  /** @docs-private */\n  @ContentChild(CdkCellDef) cell: CdkCellDef;\n\n  /** @docs-private */\n  @ContentChild(CdkHeaderCellDef) headerCell: CdkHeaderCellDef;\n\n  /** @docs-private */\n  @ContentChild(CdkFooterCellDef) footerCell: CdkFooterCellDef;\n\n  /**\n   * Transformed version of the column name that can be used as part of a CSS classname. Excludes\n   * all non-alphanumeric characters and the special characters '-' and '_'. Any characters that\n   * do not match are replaced by the '-' character.\n   */\n  cssClassFriendlyName: string;\n\n  /**\n   * Class name for cells in this column.\n   * @docs-private\n   */\n  _columnCssClassName: string[];\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Whether the sticky state has changed. */\n  hasStickyChanged(): boolean {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n\n  /** Resets the sticky changed state. */\n  resetStickyChanged(): void {\n    this._hasStickyChanged = false;\n  }\n\n  /**\n   * Overridable method that sets the css classes that will be added to every cell in this\n   * column.\n   * In the future, columnCssClassName will change from type string[] to string and this\n   * will set a single string value.\n   * @docs-private\n   */\n  protected _updateColumnCssClassName() {\n    this._columnCssClassName = [`cdk-column-${this.cssClassFriendlyName}`];\n  }\n\n  /**\n   * This has been extracted to a util because of TS 4 and VE.\n   * View Engine doesn't support property rename inheritance.\n   * TS 4.0 doesn't allow properties to override accessors or vice-versa.\n   * @docs-private\n   */\n  protected _setNameInput(value: string) {\n    // If the directive is set without a name (updated programmatically), then this setter will\n    // trigger with an empty string and should not overwrite the programmatically set value.\n    if (value) {\n      this._name = value;\n      this.cssClassFriendlyName = value.replace(/[^a-z0-9_-]/gi, '-');\n      this._updateColumnCssClassName();\n    }\n  }\n}\n\n/** Base class for the cells. Adds a CSS classname that identifies the column it renders in. */\nexport class BaseCdkCell {\n  constructor(columnDef: CdkColumnDef, elementRef: ElementRef) {\n    elementRef.nativeElement.classList.add(...columnDef._columnCssClassName);\n  }\n}\n\n/** Header cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'cdk-header-cell, th[cdk-header-cell]',\n  host: {\n    'class': 'cdk-header-cell',\n    'role': 'columnheader',\n  },\n})\nexport class CdkHeaderCell extends BaseCdkCell {\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super(inject(CdkColumnDef), inject(ElementRef));\n  }\n}\n\n/** Footer cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'cdk-footer-cell, td[cdk-footer-cell]',\n  host: {\n    'class': 'cdk-footer-cell',\n  },\n})\nexport class CdkFooterCell extends BaseCdkCell {\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const columnDef = inject(CdkColumnDef);\n    const elementRef = inject(ElementRef);\n\n    super(columnDef, elementRef);\n\n    const role = columnDef._table?._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n}\n\n/** Cell template container that adds the right classes and role. */\n@Directive({\n  selector: 'cdk-cell, td[cdk-cell]',\n  host: {\n    'class': 'cdk-cell',\n  },\n})\nexport class CdkCell extends BaseCdkCell {\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const columnDef = inject(CdkColumnDef);\n    const elementRef = inject(ElementRef);\n\n    super(columnDef, elementRef);\n\n    const role = columnDef._table?._getCellRole();\n    if (role) {\n      elementRef.nativeElement.setAttribute('role', role);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, InjectionToken, NgZone, inject} from '@angular/core';\n\n/**\n * @docs-private\n */\nexport class _Schedule {\n  tasks: (() => unknown)[] = [];\n  endTasks: (() => unknown)[] = [];\n}\n\n/** Injection token used to provide a coalesced style scheduler. */\nexport const _COALESCED_STYLE_SCHEDULER = new InjectionToken<_CoalescedStyleScheduler>(\n  '_COALESCED_STYLE_SCHEDULER',\n);\n\n/**\n * Allows grouping up CSSDom mutations after the current execution context.\n * This can significantly improve performance when separate consecutive functions are\n * reading from the CSSDom and then mutating it.\n *\n * @docs-private\n */\n@Injectable()\nexport class _CoalescedStyleScheduler {\n  private _currentSchedule: _Schedule | null = null;\n  private _ngZone = inject(NgZone);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /**\n   * Schedules the specified task to run at the end of the current VM turn.\n   */\n  schedule(task: () => unknown): void {\n    this._createScheduleIfNeeded();\n\n    this._currentSchedule!.tasks.push(task);\n  }\n\n  /**\n   * Schedules the specified task to run after other scheduled tasks at the end of the current\n   * VM turn.\n   */\n  scheduleEnd(task: () => unknown): void {\n    this._createScheduleIfNeeded();\n\n    this._currentSchedule!.endTasks.push(task);\n  }\n\n  private _createScheduleIfNeeded() {\n    if (this._currentSchedule) {\n      return;\n    }\n\n    this._currentSchedule = new _Schedule();\n\n    this._ngZone.runOutsideAngular(() =>\n      // TODO(mmalerba): Scheduling this using something that runs less frequently\n      //  (e.g. requestAnimationFrame, setTimeout, etc.) causes noticeable jank with the column\n      //  resizer. We should audit the usages of schedule / scheduleEnd in that component and see\n      //  if we can refactor it so that we don't need to flush the tasks quite so frequently.\n      queueMicrotask(() => {\n        while (this._currentSchedule!.tasks.length || this._currentSchedule!.endTasks.length) {\n          const schedule = this._currentSchedule!;\n\n          // Capture new tasks scheduled by the current set of tasks.\n          this._currentSchedule = new _Schedule();\n\n          for (const task of schedule.tasks) {\n            task();\n          }\n\n          for (const task of schedule.endTasks) {\n            task();\n          }\n        }\n\n        this._currentSchedule = null;\n      }),\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  IterableChanges,\n  IterableDiffer,\n  IterableDiffers,\n  OnChanges,\n  OnDestroy,\n  SimpleChanges,\n  TemplateRef,\n  ViewContainerRef,\n  ViewEncapsulation,\n  Input,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\nimport {CanStick} from './can-stick';\nimport {CdkCellDef, CdkColumnDef} from './cell';\nimport {CDK_TABLE} from './tokens';\n\n/**\n * The row template that can be used by the mat-table. Should not be used outside of the\n * material library.\n */\nexport const CDK_ROW_TEMPLATE = `<ng-container cdkCellOutlet></ng-container>`;\n\n/**\n * Base class for the CdkHeaderRowDef and CdkRowDef that handles checking their columns inputs\n * for changes and notifying the table.\n */\n@Directive()\nexport abstract class BaseRowDef implements OnChanges {\n  template = inject<TemplateRef<any>>(TemplateRef);\n  protected _differs = inject(IterableDiffers);\n\n  /** The columns to be displayed on this row. */\n  columns: Iterable<string>;\n\n  /** Differ used to check if any changes were made to the columns. */\n  protected _columnsDiffer: IterableDiffer<any>;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngOnChanges(changes: SimpleChanges): void {\n    // Create a new columns differ if one does not yet exist. Initialize it based on initial value\n    // of the columns property or an empty array if none is provided.\n    if (!this._columnsDiffer) {\n      const columns = (changes['columns'] && changes['columns'].currentValue) || [];\n      this._columnsDiffer = this._differs.find(columns).create();\n      this._columnsDiffer.diff(columns);\n    }\n  }\n\n  /**\n   * Returns the difference between the current columns and the columns from the last diff, or null\n   * if there is no difference.\n   */\n  getColumnsDiff(): IterableChanges<any> | null {\n    return this._columnsDiffer.diff(this.columns);\n  }\n\n  /** Gets this row def's relevant cell template from the provided column def. */\n  extractCellTemplate(column: CdkColumnDef): TemplateRef<any> {\n    if (this instanceof CdkHeaderRowDef) {\n      return column.headerCell.template;\n    }\n    if (this instanceof CdkFooterRowDef) {\n      return column.footerCell.template;\n    } else {\n      return column.cell.template;\n    }\n  }\n}\n\n/**\n * Header row definition for the CDK table.\n * Captures the header row's template and other header properties such as the columns to display.\n */\n@Directive({\n  selector: '[cdkHeaderRowDef]',\n  inputs: [{name: 'columns', alias: 'cdkHeaderRowDef'}],\n})\nexport class CdkHeaderRowDef extends BaseRowDef implements CanStick, OnChanges {\n  _table? = inject(CDK_TABLE, {optional: true});\n\n  private _hasStickyChanged = false;\n\n  /** Whether the row is sticky. */\n  @Input({alias: 'cdkHeaderRowDefSticky', transform: booleanAttribute})\n  get sticky(): boolean {\n    return this._sticky;\n  }\n  set sticky(value: boolean) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  private _sticky = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super(inject<TemplateRef<any>>(TemplateRef), inject(IterableDiffers));\n  }\n\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  override ngOnChanges(changes: SimpleChanges): void {\n    super.ngOnChanges(changes);\n  }\n\n  /** Whether the sticky state has changed. */\n  hasStickyChanged(): boolean {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n\n  /** Resets the sticky changed state. */\n  resetStickyChanged(): void {\n    this._hasStickyChanged = false;\n  }\n}\n\n/**\n * Footer row definition for the CDK table.\n * Captures the footer row's template and other footer properties such as the columns to display.\n */\n@Directive({\n  selector: '[cdkFooterRowDef]',\n  inputs: [{name: 'columns', alias: 'cdkFooterRowDef'}],\n})\nexport class CdkFooterRowDef extends BaseRowDef implements CanStick, OnChanges {\n  _table? = inject(CDK_TABLE, {optional: true});\n\n  private _hasStickyChanged = false;\n\n  /** Whether the row is sticky. */\n  @Input({alias: 'cdkFooterRowDefSticky', transform: booleanAttribute})\n  get sticky(): boolean {\n    return this._sticky;\n  }\n  set sticky(value: boolean) {\n    if (value !== this._sticky) {\n      this._sticky = value;\n      this._hasStickyChanged = true;\n    }\n  }\n  private _sticky = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super(inject<TemplateRef<any>>(TemplateRef), inject(IterableDiffers));\n  }\n\n  // Prerender fails to recognize that ngOnChanges in a part of this class through inheritance.\n  // Explicitly define it so that the method is called as part of the Angular lifecycle.\n  override ngOnChanges(changes: SimpleChanges): void {\n    super.ngOnChanges(changes);\n  }\n\n  /** Whether the sticky state has changed. */\n  hasStickyChanged(): boolean {\n    const hasStickyChanged = this._hasStickyChanged;\n    this.resetStickyChanged();\n    return hasStickyChanged;\n  }\n\n  /** Resets the sticky changed state. */\n  resetStickyChanged(): void {\n    this._hasStickyChanged = false;\n  }\n}\n\n/**\n * Data row definition for the CDK table.\n * Captures the header row's template and other row properties such as the columns to display and\n * a when predicate that describes when this row should be used.\n */\n@Directive({\n  selector: '[cdkRowDef]',\n  inputs: [\n    {name: 'columns', alias: 'cdkRowDefColumns'},\n    {name: 'when', alias: 'cdkRowDefWhen'},\n  ],\n})\nexport class CdkRowDef<T> extends BaseRowDef {\n  _table? = inject(CDK_TABLE, {optional: true});\n\n  /**\n   * Function that should return true if this row template should be used for the provided index\n   * and row data. If left undefined, this row will be considered the default row template to use\n   * when no other when functions return true for the data.\n   * For every row, there must be at least one when function that passes or an undefined to default.\n   */\n  when: (index: number, rowData: T) => boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    // TODO(andrewseguin): Add an input for providing a switch function to determine\n    //   if this template should be used.\n    super(inject<TemplateRef<any>>(TemplateRef), inject(IterableDiffers));\n  }\n}\n\n/** Context provided to the row cells when `multiTemplateDataRows` is false */\nexport interface CdkCellOutletRowContext<T> {\n  /** Data for the row that this cell is located within. */\n  $implicit?: T;\n\n  /** Index of the data object in the provided data array. */\n  index?: number;\n\n  /** Length of the number of total rows. */\n  count?: number;\n\n  /** True if this cell is contained in the first row. */\n  first?: boolean;\n\n  /** True if this cell is contained in the last row. */\n  last?: boolean;\n\n  /** True if this cell is contained in a row with an even-numbered index. */\n  even?: boolean;\n\n  /** True if this cell is contained in a row with an odd-numbered index. */\n  odd?: boolean;\n}\n\n/**\n * Context provided to the row cells when `multiTemplateDataRows` is true. This context is the same\n * as CdkCellOutletRowContext except that the single `index` value is replaced by `dataIndex` and\n * `renderIndex`.\n */\nexport interface CdkCellOutletMultiRowContext<T> {\n  /** Data for the row that this cell is located within. */\n  $implicit?: T;\n\n  /** Index of the data object in the provided data array. */\n  dataIndex?: number;\n\n  /** Index location of the rendered row that this cell is located within. */\n  renderIndex?: number;\n\n  /** Length of the number of total rows. */\n  count?: number;\n\n  /** True if this cell is contained in the first row. */\n  first?: boolean;\n\n  /** True if this cell is contained in the last row. */\n  last?: boolean;\n\n  /** True if this cell is contained in a row with an even-numbered index. */\n  even?: boolean;\n\n  /** True if this cell is contained in a row with an odd-numbered index. */\n  odd?: boolean;\n}\n\n/**\n * Outlet for rendering cells inside of a row or header row.\n * @docs-private\n */\n@Directive({\n  selector: '[cdkCellOutlet]',\n})\nexport class CdkCellOutlet implements OnDestroy {\n  _viewContainer = inject(ViewContainerRef);\n\n  /** The ordered list of cells to render within this outlet's view container */\n  cells: CdkCellDef[];\n\n  /** The data context to be provided to each cell */\n  context: any;\n\n  /**\n   * Static property containing the latest constructed instance of this class.\n   * Used by the CDK table when each CdkHeaderRow and CdkRow component is created using\n   * createEmbeddedView. After one of these components are created, this property will provide\n   * a handle to provide that component's cells and context. After init, the CdkCellOutlet will\n   * construct the cells with the provided context.\n   */\n  static mostRecentCellOutlet: CdkCellOutlet | null = null;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    CdkCellOutlet.mostRecentCellOutlet = this;\n  }\n\n  ngOnDestroy() {\n    // If this was the last outlet being rendered in the view, remove the reference\n    // from the static property after it has been destroyed to avoid leaking memory.\n    if (CdkCellOutlet.mostRecentCellOutlet === this) {\n      CdkCellOutlet.mostRecentCellOutlet = null;\n    }\n  }\n}\n\n/** Header template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  selector: 'cdk-header-row, tr[cdk-header-row]',\n  template: CDK_ROW_TEMPLATE,\n  host: {\n    'class': 'cdk-header-row',\n    'role': 'row',\n  },\n  // See note on CdkTable for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  imports: [CdkCellOutlet],\n})\nexport class CdkHeaderRow {}\n\n/** Footer template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  selector: 'cdk-footer-row, tr[cdk-footer-row]',\n  template: CDK_ROW_TEMPLATE,\n  host: {\n    'class': 'cdk-footer-row',\n    'role': 'row',\n  },\n  // See note on CdkTable for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  imports: [CdkCellOutlet],\n})\nexport class CdkFooterRow {}\n\n/** Data row template container that contains the cell outlet. Adds the right class and role. */\n@Component({\n  selector: 'cdk-row, tr[cdk-row]',\n  template: CDK_ROW_TEMPLATE,\n  host: {\n    'class': 'cdk-row',\n    'role': 'row',\n  },\n  // See note on CdkTable for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  imports: [CdkCellOutlet],\n})\nexport class CdkRow {}\n\n/** Row that can be used to display a message when no data is shown in the table. */\n@Directive({\n  selector: 'ng-template[cdkNoDataRow]',\n})\nexport class CdkNoDataRow {\n  templateRef = inject<TemplateRef<any>>(TemplateRef);\n\n  _contentClassName = 'cdk-no-data-row';\n\n  constructor(...args: unknown[]);\n  constructor() {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Directions that can be used when setting sticky positioning.\n * @docs-private\n */\nimport {afterNextRender, Injector} from '@angular/core';\nimport {Direction} from '../bidi';\nimport {_CoalescedStyleScheduler} from './coalesced-style-scheduler';\nimport {StickyPositioningListener} from './sticky-position-listener';\n\nexport type StickyDirection = 'top' | 'bottom' | 'left' | 'right';\n\ninterface UpdateStickyColumnsParams {\n  rows: HTMLElement[];\n  stickyStartStates: boolean[];\n  stickyEndStates: boolean[];\n}\n\n/**\n * List of all possible directions that can be used for sticky positioning.\n * @docs-private\n */\nexport const STICKY_DIRECTIONS: StickyDirection[] = ['top', 'bottom', 'left', 'right'];\n\n/**\n * Applies and removes sticky positioning styles to the `CdkTable` rows and columns cells.\n * @docs-private\n */\nexport class StickyStyler {\n  private _elemSizeCache = new WeakMap<HTMLElement, {width: number; height: number}>();\n  private _resizeObserver = globalThis?.ResizeObserver\n    ? new globalThis.ResizeObserver(entries => this._updateCachedSizes(entries))\n    : null;\n  private _updatedStickyColumnsParamsToReplay: UpdateStickyColumnsParams[] = [];\n  private _stickyColumnsReplayTimeout: ReturnType<typeof setTimeout> | null = null;\n  private _cachedCellWidths: number[] = [];\n  private readonly _borderCellCss: Readonly<{[d in StickyDirection]: string}>;\n  private _destroyed = false;\n\n  /**\n   * @param _isNativeHtmlTable Whether the sticky logic should be based on a table\n   *     that uses the native `<table>` element.\n   * @param _stickCellCss The CSS class that will be applied to every row/cell that has\n   *     sticky positioning applied.\n   * @param direction The directionality context of the table (ltr/rtl); affects column positioning\n   *     by reversing left/right positions.\n   * @param _isBrowser Whether the table is currently being rendered on the server or the client.\n   * @param _needsPositionStickyOnElement Whether we need to specify position: sticky on cells\n   *     using inline styles. If false, it is assumed that position: sticky is included in\n   *     the component stylesheet for _stickCellCss.\n   * @param _positionListener A listener that is notified of changes to sticky rows/columns\n   *     and their dimensions.\n   * @param _tableInjector The table's Injector.\n   */\n  constructor(\n    private _isNativeHtmlTable: boolean,\n    private _stickCellCss: string,\n    public direction: Direction,\n    private _coalescedStyleScheduler: _CoalescedStyleScheduler,\n    private _isBrowser = true,\n    private readonly _needsPositionStickyOnElement = true,\n    private readonly _positionListener?: StickyPositioningListener,\n    private readonly _tableInjector?: Injector,\n  ) {\n    this._borderCellCss = {\n      'top': `${_stickCellCss}-border-elem-top`,\n      'bottom': `${_stickCellCss}-border-elem-bottom`,\n      'left': `${_stickCellCss}-border-elem-left`,\n      'right': `${_stickCellCss}-border-elem-right`,\n    };\n  }\n\n  /**\n   * Clears the sticky positioning styles from the row and its cells by resetting the `position`\n   * style, setting the zIndex to 0, and unsetting each provided sticky direction.\n   * @param rows The list of rows that should be cleared from sticking in the provided directions\n   * @param stickyDirections The directions that should no longer be set as sticky on the rows.\n   */\n  clearStickyPositioning(rows: HTMLElement[], stickyDirections: StickyDirection[]) {\n    if (stickyDirections.includes('left') || stickyDirections.includes('right')) {\n      this._removeFromStickyColumnReplayQueue(rows);\n    }\n\n    const elementsToClear: HTMLElement[] = [];\n    for (const row of rows) {\n      // If the row isn't an element (e.g. if it's an `ng-container`),\n      // it won't have inline styles or `children` so we skip it.\n      if (row.nodeType !== row.ELEMENT_NODE) {\n        continue;\n      }\n\n      elementsToClear.push(row, ...(Array.from(row.children) as HTMLElement[]));\n    }\n\n    // Coalesce with sticky row/column updates (and potentially other changes like column resize).\n    this._afterNextRender({\n      write: () => {\n        for (const element of elementsToClear) {\n          this._removeStickyStyle(element, stickyDirections);\n        }\n      },\n    });\n  }\n\n  /**\n   * Applies sticky left and right positions to the cells of each row according to the sticky\n   * states of the rendered column definitions.\n   * @param rows The rows that should have its set of cells stuck according to the sticky states.\n   * @param stickyStartStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the start of the row.\n   * @param stickyEndStates A list of boolean states where each state represents whether the cell\n   *     in this index position should be stuck to the end of the row.\n   * @param recalculateCellWidths Whether the sticky styler should recalculate the width of each\n   *     column cell. If `false` cached widths will be used instead.\n   * @param replay Whether to enqueue this call for replay after a ResizeObserver update.\n   */\n  updateStickyColumns(\n    rows: HTMLElement[],\n    stickyStartStates: boolean[],\n    stickyEndStates: boolean[],\n    recalculateCellWidths = true,\n    replay = true,\n  ) {\n    // Don't cache any state if none of the columns are sticky.\n    if (\n      !rows.length ||\n      !this._isBrowser ||\n      !(stickyStartStates.some(state => state) || stickyEndStates.some(state => state))\n    ) {\n      this._positionListener?.stickyColumnsUpdated({sizes: []});\n      this._positionListener?.stickyEndColumnsUpdated({sizes: []});\n      return;\n    }\n\n    // Coalesce with sticky row updates (and potentially other changes like column resize).\n    const firstRow = rows[0];\n    const numCells = firstRow.children.length;\n\n    const isRtl = this.direction === 'rtl';\n    const start = isRtl ? 'right' : 'left';\n    const end = isRtl ? 'left' : 'right';\n\n    const lastStickyStart = stickyStartStates.lastIndexOf(true);\n    const firstStickyEnd = stickyEndStates.indexOf(true);\n\n    let cellWidths: number[];\n    let startPositions: number[];\n    let endPositions: number[];\n\n    if (replay) {\n      this._updateStickyColumnReplayQueue({\n        rows: [...rows],\n        stickyStartStates: [...stickyStartStates],\n        stickyEndStates: [...stickyEndStates],\n      });\n    }\n\n    this._afterNextRender({\n      earlyRead: () => {\n        cellWidths = this._getCellWidths(firstRow, recalculateCellWidths);\n\n        startPositions = this._getStickyStartColumnPositions(cellWidths, stickyStartStates);\n        endPositions = this._getStickyEndColumnPositions(cellWidths, stickyEndStates);\n      },\n      write: () => {\n        for (const row of rows) {\n          for (let i = 0; i < numCells; i++) {\n            const cell = row.children[i] as HTMLElement;\n            if (stickyStartStates[i]) {\n              this._addStickyStyle(cell, start, startPositions[i], i === lastStickyStart);\n            }\n\n            if (stickyEndStates[i]) {\n              this._addStickyStyle(cell, end, endPositions[i], i === firstStickyEnd);\n            }\n          }\n        }\n\n        if (this._positionListener && cellWidths.some(w => !!w)) {\n          this._positionListener.stickyColumnsUpdated({\n            sizes:\n              lastStickyStart === -1\n                ? []\n                : cellWidths\n                    .slice(0, lastStickyStart + 1)\n                    .map((width, index) => (stickyStartStates[index] ? width : null)),\n          });\n          this._positionListener.stickyEndColumnsUpdated({\n            sizes:\n              firstStickyEnd === -1\n                ? []\n                : cellWidths\n                    .slice(firstStickyEnd)\n                    .map((width, index) => (stickyEndStates[index + firstStickyEnd] ? width : null))\n                    .reverse(),\n          });\n        }\n      },\n    });\n  }\n\n  /**\n   * Applies sticky positioning to the row's cells if using the native table layout, and to the\n   * row itself otherwise.\n   * @param rowsToStick The list of rows that should be stuck according to their corresponding\n   *     sticky state and to the provided top or bottom position.\n   * @param stickyStates A list of boolean states where each state represents whether the row\n   *     should be stuck in the particular top or bottom position.\n   * @param position The position direction in which the row should be stuck if that row should be\n   *     sticky.\n   *\n   */\n  stickRows(rowsToStick: HTMLElement[], stickyStates: boolean[], position: 'top' | 'bottom') {\n    // Since we can't measure the rows on the server, we can't stick the rows properly.\n    if (!this._isBrowser) {\n      return;\n    }\n\n    // If positioning the rows to the bottom, reverse their order when evaluating the sticky\n    // position such that the last row stuck will be \"bottom: 0px\" and so on. Note that the\n    // sticky states need to be reversed as well.\n    const rows = position === 'bottom' ? rowsToStick.slice().reverse() : rowsToStick;\n    const states = position === 'bottom' ? stickyStates.slice().reverse() : stickyStates;\n\n    // Measure row heights all at once before adding sticky styles to reduce layout thrashing.\n    const stickyOffsets: number[] = [];\n    const stickyCellHeights: (number | undefined)[] = [];\n    const elementsToStick: HTMLElement[][] = [];\n\n    // Coalesce with other sticky row updates (top/bottom), sticky columns updates\n    // (and potentially other changes like column resize).\n    this._afterNextRender({\n      earlyRead: () => {\n        for (let rowIndex = 0, stickyOffset = 0; rowIndex < rows.length; rowIndex++) {\n          if (!states[rowIndex]) {\n            continue;\n          }\n\n          stickyOffsets[rowIndex] = stickyOffset;\n          const row = rows[rowIndex];\n          elementsToStick[rowIndex] = this._isNativeHtmlTable\n            ? (Array.from(row.children) as HTMLElement[])\n            : [row];\n\n          const height = this._retrieveElementSize(row).height;\n          stickyOffset += height;\n          stickyCellHeights[rowIndex] = height;\n        }\n      },\n      write: () => {\n        const borderedRowIndex = states.lastIndexOf(true);\n\n        for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n          if (!states[rowIndex]) {\n            continue;\n          }\n\n          const offset = stickyOffsets[rowIndex];\n          const isBorderedRowIndex = rowIndex === borderedRowIndex;\n          for (const element of elementsToStick[rowIndex]) {\n            this._addStickyStyle(element, position, offset, isBorderedRowIndex);\n          }\n        }\n\n        if (position === 'top') {\n          this._positionListener?.stickyHeaderRowsUpdated({\n            sizes: stickyCellHeights,\n            offsets: stickyOffsets,\n            elements: elementsToStick,\n          });\n        } else {\n          this._positionListener?.stickyFooterRowsUpdated({\n            sizes: stickyCellHeights,\n            offsets: stickyOffsets,\n            elements: elementsToStick,\n          });\n        }\n      },\n    });\n  }\n\n  /**\n   * When using the native table in Safari, sticky footer cells do not stick. The only way to stick\n   * footer rows is to apply sticky styling to the tfoot container. This should only be done if\n   * all footer rows are sticky. If not all footer rows are sticky, remove sticky positioning from\n   * the tfoot element.\n   */\n  updateStickyFooterContainer(tableElement: Element, stickyStates: boolean[]) {\n    if (!this._isNativeHtmlTable) {\n      return;\n    }\n\n    // Coalesce with other sticky updates (and potentially other changes like column resize).\n    this._afterNextRender({\n      write: () => {\n        const tfoot = tableElement.querySelector('tfoot')!;\n\n        if (tfoot) {\n          if (stickyStates.some(state => !state)) {\n            this._removeStickyStyle(tfoot, ['bottom']);\n          } else {\n            this._addStickyStyle(tfoot, 'bottom', 0, false);\n          }\n        }\n      },\n    });\n  }\n\n  /** Triggered by the table's OnDestroy hook. */\n  destroy() {\n    if (this._stickyColumnsReplayTimeout) {\n      clearTimeout(this._stickyColumnsReplayTimeout);\n    }\n\n    this._resizeObserver?.disconnect();\n    this._destroyed = true;\n  }\n\n  /**\n   * Removes the sticky style on the element by removing the sticky cell CSS class, re-evaluating\n   * the zIndex, removing each of the provided sticky directions, and removing the\n   * sticky position if there are no more directions.\n   */\n  _removeStickyStyle(element: HTMLElement, stickyDirections: StickyDirection[]) {\n    if (!element.classList.contains(this._stickCellCss)) {\n      return;\n    }\n\n    for (const dir of stickyDirections) {\n      element.style[dir] = '';\n      element.classList.remove(this._borderCellCss[dir]);\n    }\n\n    // If the element no longer has any more sticky directions, remove sticky positioning and\n    // the sticky CSS class.\n    // Short-circuit checking element.style[dir] for stickyDirections as they\n    // were already removed above.\n    const hasDirection = STICKY_DIRECTIONS.some(\n      dir => stickyDirections.indexOf(dir) === -1 && element.style[dir],\n    );\n    if (hasDirection) {\n      element.style.zIndex = this._getCalculatedZIndex(element);\n    } else {\n      // When not hasDirection, _getCalculatedZIndex will always return ''.\n      element.style.zIndex = '';\n      if (this._needsPositionStickyOnElement) {\n        element.style.position = '';\n      }\n      element.classList.remove(this._stickCellCss);\n    }\n  }\n\n  /**\n   * Adds the sticky styling to the element by adding the sticky style class, changing position\n   * to be sticky (and -webkit-sticky), setting the appropriate zIndex, and adding a sticky\n   * direction and value.\n   */\n  _addStickyStyle(\n    element: HTMLElement,\n    dir: StickyDirection,\n    dirValue: number,\n    isBorderElement: boolean,\n  ) {\n    element.classList.add(this._stickCellCss);\n    if (isBorderElement) {\n      element.classList.add(this._borderCellCss[dir]);\n    }\n    element.style[dir] = `${dirValue}px`;\n    element.style.zIndex = this._getCalculatedZIndex(element);\n    if (this._needsPositionStickyOnElement) {\n      element.style.cssText += 'position: -webkit-sticky; position: sticky; ';\n    }\n  }\n\n  /**\n   * Calculate what the z-index should be for the element, depending on what directions (top,\n   * bottom, left, right) have been set. It should be true that elements with a top direction\n   * should have the highest index since these are elements like a table header. If any of those\n   * elements are also sticky in another direction, then they should appear above other elements\n   * that are only sticky top (e.g. a sticky column on a sticky header). Bottom-sticky elements\n   * (e.g. footer rows) should then be next in the ordering such that they are below the header\n   * but above any non-sticky elements. Finally, left/right sticky elements (e.g. sticky columns)\n   * should minimally increment so that they are above non-sticky elements but below top and bottom\n   * elements.\n   */\n  _getCalculatedZIndex(element: HTMLElement): string {\n    const zIndexIncrements = {\n      top: 100,\n      bottom: 10,\n      left: 1,\n      right: 1,\n    };\n\n    let zIndex = 0;\n    // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n    // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n    // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n    for (const dir of STICKY_DIRECTIONS as Iterable<StickyDirection> & StickyDirection[]) {\n      if (element.style[dir]) {\n        zIndex += zIndexIncrements[dir];\n      }\n    }\n\n    return zIndex ? `${zIndex}` : '';\n  }\n\n  /** Gets the widths for each cell in the provided row. */\n  _getCellWidths(row: HTMLElement, recalculateCellWidths = true): number[] {\n    if (!recalculateCellWidths && this._cachedCellWidths.length) {\n      return this._cachedCellWidths;\n    }\n\n    const cellWidths: number[] = [];\n    const firstRowCells = row.children;\n    for (let i = 0; i < firstRowCells.length; i++) {\n      const cell = firstRowCells[i] as HTMLElement;\n      cellWidths.push(this._retrieveElementSize(cell).width);\n    }\n\n    this._cachedCellWidths = cellWidths;\n    return cellWidths;\n  }\n\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyStartColumnPositions(widths: number[], stickyStates: boolean[]): number[] {\n    const positions: number[] = [];\n    let nextPosition = 0;\n\n    for (let i = 0; i < widths.length; i++) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n\n    return positions;\n  }\n\n  /**\n   * Determines the left and right positions of each sticky column cell, which will be the\n   * accumulation of all sticky column cell widths to the left and right, respectively.\n   * Non-sticky cells do not need to have a value set since their positions will not be applied.\n   */\n  _getStickyEndColumnPositions(widths: number[], stickyStates: boolean[]): number[] {\n    const positions: number[] = [];\n    let nextPosition = 0;\n\n    for (let i = widths.length; i > 0; i--) {\n      if (stickyStates[i]) {\n        positions[i] = nextPosition;\n        nextPosition += widths[i];\n      }\n    }\n\n    return positions;\n  }\n\n  /**\n   * Retreives the most recently observed size of the specified element from the cache, or\n   * meaures it directly if not yet cached.\n   */\n  private _retrieveElementSize(element: HTMLElement): {width: number; height: number} {\n    const cachedSize = this._elemSizeCache.get(element);\n    if (cachedSize) {\n      return cachedSize;\n    }\n\n    const clientRect = element.getBoundingClientRect();\n    const size = {width: clientRect.width, height: clientRect.height};\n\n    if (!this._resizeObserver) {\n      return size;\n    }\n\n    this._elemSizeCache.set(element, size);\n    this._resizeObserver.observe(element, {box: 'border-box'});\n    return size;\n  }\n\n  /**\n   * Conditionally enqueue the requested sticky update and clear previously queued updates\n   * for the same rows.\n   */\n  private _updateStickyColumnReplayQueue(params: UpdateStickyColumnsParams) {\n    this._removeFromStickyColumnReplayQueue(params.rows);\n\n    // No need to replay if a flush is pending.\n    if (!this._stickyColumnsReplayTimeout) {\n      this._updatedStickyColumnsParamsToReplay.push(params);\n    }\n  }\n\n  /** Remove updates for the specified rows from the queue. */\n  private _removeFromStickyColumnReplayQueue(rows: HTMLElement[]) {\n    const rowsSet = new Set(rows);\n    for (const update of this._updatedStickyColumnsParamsToReplay) {\n      update.rows = update.rows.filter(row => !rowsSet.has(row));\n    }\n    this._updatedStickyColumnsParamsToReplay = this._updatedStickyColumnsParamsToReplay.filter(\n      update => !!update.rows.length,\n    );\n  }\n\n  /** Update _elemSizeCache with the observed sizes. */\n  private _updateCachedSizes(entries: ResizeObserverEntry[]) {\n    let needsColumnUpdate = false;\n    for (const entry of entries) {\n      const newEntry = entry.borderBoxSize?.length\n        ? {\n            width: entry.borderBoxSize[0].inlineSize,\n            height: entry.borderBoxSize[0].blockSize,\n          }\n        : {\n            width: entry.contentRect.width,\n            height: entry.contentRect.height,\n          };\n\n      if (\n        newEntry.width !== this._elemSizeCache.get(entry.target as HTMLElement)?.width &&\n        isCell(entry.target)\n      ) {\n        needsColumnUpdate = true;\n      }\n\n      this._elemSizeCache.set(entry.target as HTMLElement, newEntry);\n    }\n\n    if (needsColumnUpdate && this._updatedStickyColumnsParamsToReplay.length) {\n      if (this._stickyColumnsReplayTimeout) {\n        clearTimeout(this._stickyColumnsReplayTimeout);\n      }\n\n      this._stickyColumnsReplayTimeout = setTimeout(() => {\n        if (this._destroyed) {\n          return;\n        }\n\n        for (const update of this._updatedStickyColumnsParamsToReplay) {\n          this.updateStickyColumns(\n            update.rows,\n            update.stickyStartStates,\n            update.stickyEndStates,\n            true,\n            false,\n          );\n        }\n        this._updatedStickyColumnsParamsToReplay = [];\n        this._stickyColumnsReplayTimeout = null;\n      }, 0);\n    }\n  }\n\n  /**\n   * Invoke afterNextRender with the table's injector, falling back to CoalescedStyleScheduler\n   * if the injector was not provided.\n   */\n  private _afterNextRender(spec: {earlyRead?: () => void; write: () => void}) {\n    if (this._tableInjector) {\n      afterNextRender(spec, {injector: this._tableInjector});\n    } else {\n      this._coalescedStyleScheduler.schedule(() => {\n        spec.earlyRead?.();\n        spec.write();\n      });\n    }\n  }\n}\n\nfunction isCell(element: Element) {\n  return ['cdk-cell', 'cdk-header-cell', 'cdk-footer-cell'].some(klass =>\n    element.classList.contains(klass),\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Returns an error to be thrown when attempting to find an nonexistent column.\n * @param id Id whose lookup failed.\n * @docs-private\n */\nexport function getTableUnknownColumnError(id: string) {\n  return Error(`Could not find column with id \"${id}\".`);\n}\n\n/**\n * Returns an error to be thrown when two column definitions have the same name.\n * @docs-private\n */\nexport function getTableDuplicateColumnNameError(name: string) {\n  return Error(`Duplicate column definition name provided: \"${name}\".`);\n}\n\n/**\n * Returns an error to be thrown when there are multiple rows that are missing a when function.\n * @docs-private\n */\nexport function getTableMultipleDefaultRowDefsError() {\n  return Error(`There can only be one default row without a when predicate function.`);\n}\n\n/**\n * Returns an error to be thrown when there are no matching row defs for a particular set of data.\n * @docs-private\n */\nexport function getTableMissingMatchingRowDefError(data: any) {\n  return Error(\n    `Could not find a matching row definition for the` +\n      `provided row data: ${JSON.stringify(data)}`,\n  );\n}\n\n/**\n * Returns an error to be thrown when there is no row definitions present in the content.\n * @docs-private\n */\nexport function getTableMissingRowDefsError() {\n  return Error(\n    'Missing definitions for header, footer, and row; ' +\n      'cannot determine which columns should be rendered.',\n  );\n}\n\n/**\n * Returns an error to be thrown when the data source does not match the compatible types.\n * @docs-private\n */\nexport function getTableUnknownDataSourceError() {\n  return Error(`Provided data source did not match an array, Observable, or DataSource`);\n}\n\n/**\n * Returns an error to be thrown when the text column cannot find a parent table to inject.\n * @docs-private\n */\nexport function getTableTextColumnMissingParentTableError() {\n  return Error(`Text column could not find a parent table for registration.`);\n}\n\n/**\n * Returns an error to be thrown when a table text column doesn't have a name.\n * @docs-private\n */\nexport function getTableTextColumnMissingNameError() {\n  return Error(`Table text column must have a name.`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '@angular/core';\n\n/** The injection token used to specify the StickyPositioningListener. */\nexport const STICKY_POSITIONING_LISTENER = new InjectionToken<StickyPositioningListener>('CDK_SPL');\n\nexport type StickySize = number | null | undefined;\nexport type StickyOffset = number | null | undefined;\n\nexport interface StickyUpdate {\n  elements?: readonly (HTMLElement[] | undefined)[];\n  offsets?: StickyOffset[];\n  sizes: StickySize[];\n}\n\n/**\n * If provided, CdkTable will call the methods below when it updates the size/\n * position/etc of its sticky rows and columns.\n */\nexport interface StickyPositioningListener {\n  /** Called when CdkTable updates its sticky start columns. */\n  stickyColumnsUpdated(update: StickyUpdate): void;\n\n  /** Called when CdkTable updates its sticky end columns. */\n  stickyEndColumnsUpdated(update: StickyUpdate): void;\n\n  /** Called when CdkTable updates its sticky header rows. */\n  stickyHeaderRowsUpdated(update: StickyUpdate): void;\n\n  /** Called when CdkTable updates its sticky footer rows. */\n  stickyFooterRowsUpdated(update: StickyUpdate): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Direction, Directionality} from '../bidi';\nimport {\n  CollectionViewer,\n  DataSource,\n  _DisposeViewRepeaterStrategy,\n  _RecycleViewRepeaterStrategy,\n  isDataSource,\n  _VIEW_REPEATER_STRATEGY,\n  _ViewRepeater,\n  _ViewRepeaterItemChange,\n  _ViewRepeaterItemInsertArgs,\n  _ViewRepeaterOperation,\n} from '../collections';\nimport {Platform} from '../platform';\nimport {ViewportRuler} from '../scrolling';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterContentChecked,\n  AfterContentInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  EmbeddedViewRef,\n  EventEmitter,\n  Input,\n  IterableChangeRecord,\n  IterableDiffer,\n  IterableDiffers,\n  OnDestroy,\n  OnInit,\n  Output,\n  QueryList,\n  TemplateRef,\n  TrackByFunction,\n  ViewContainerRef,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n  Injector,\n  HostAttributeToken,\n} from '@angular/core';\nimport {\n  BehaviorSubject,\n  isObservable,\n  Observable,\n  of as observableOf,\n  Subject,\n  Subscription,\n} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\nimport {CdkColumnDef} from './cell';\nimport {_CoalescedStyleScheduler, _COALESCED_STYLE_SCHEDULER} from './coalesced-style-scheduler';\nimport {\n  BaseRowDef,\n  CdkCellOutlet,\n  CdkCellOutletMultiRowContext,\n  CdkCellOutletRowContext,\n  CdkFooterRowDef,\n  CdkHeaderRowDef,\n  CdkNoDataRow,\n  CdkRowDef,\n} from './row';\nimport {StickyStyler} from './sticky-styler';\nimport {\n  getTableDuplicateColumnNameError,\n  getTableMissingMatchingRowDefError,\n  getTableMissingRowDefsError,\n  getTableMultipleDefaultRowDefsError,\n  getTableUnknownColumnError,\n  getTableUnknownDataSourceError,\n} from './table-errors';\nimport {STICKY_POSITIONING_LISTENER, StickyPositioningListener} from './sticky-position-listener';\nimport {CDK_TABLE} from './tokens';\n\n/**\n * Enables the recycle view repeater strategy, which reduces rendering latency. Not compatible with\n * tables that animate rows.\n */\n@Directive({\n  selector: 'cdk-table[recycleRows], table[cdk-table][recycleRows]',\n  providers: [{provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy}],\n})\nexport class CdkRecycleRows {}\n\n/** Interface used to provide an outlet for rows to be inserted into. */\nexport interface RowOutlet {\n  viewContainer: ViewContainerRef;\n}\n\n/** Possible types that can be set as the data source for a `CdkTable`. */\nexport type CdkTableDataSourceInput<T> = readonly T[] | DataSource<T> | Observable<readonly T[]>;\n\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert data rows.\n * @docs-private\n */\n@Directive({\n  selector: '[rowOutlet]',\n})\nexport class DataRowOutlet implements RowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const table = inject<CdkTable<unknown>>(CDK_TABLE);\n    table._rowOutlet = this;\n    table._outletAssigned();\n  }\n}\n\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the header.\n * @docs-private\n */\n@Directive({\n  selector: '[headerRowOutlet]',\n})\nexport class HeaderRowOutlet implements RowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const table = inject<CdkTable<unknown>>(CDK_TABLE);\n    table._headerRowOutlet = this;\n    table._outletAssigned();\n  }\n}\n\n/**\n * Provides a handle for the table to grab the view container's ng-container to insert the footer.\n * @docs-private\n */\n@Directive({\n  selector: '[footerRowOutlet]',\n})\nexport class FooterRowOutlet implements RowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const table = inject<CdkTable<unknown>>(CDK_TABLE);\n    table._footerRowOutlet = this;\n    table._outletAssigned();\n  }\n}\n\n/**\n * Provides a handle for the table to grab the view\n * container's ng-container to insert the no data row.\n * @docs-private\n */\n@Directive({\n  selector: '[noDataRowOutlet]',\n})\nexport class NoDataRowOutlet implements RowOutlet {\n  viewContainer = inject(ViewContainerRef);\n  elementRef = inject(ElementRef);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const table = inject<CdkTable<unknown>>(CDK_TABLE);\n    table._noDataRowOutlet = this;\n    table._outletAssigned();\n  }\n}\n\n/**\n * The table template that can be used by the mat-table. Should not be used outside of the\n * material library.\n * @docs-private\n */\nexport const CDK_TABLE_TEMPLATE =\n  // Note that according to MDN, the `caption` element has to be projected as the **first**\n  // element in the table. See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/caption\n  `\n  <ng-content select=\"caption\"/>\n  <ng-content select=\"colgroup, col\"/>\n\n  <!--\n    Unprojected content throws a hydration error so we need this to capture it.\n    It gets removed on the client so it doesn't affect the layout.\n  -->\n  @if (_isServer) {\n    <ng-content/>\n  }\n\n  @if (_isNativeHtmlTable) {\n    <thead role=\"rowgroup\">\n      <ng-container headerRowOutlet/>\n    </thead>\n    <tbody role=\"rowgroup\">\n      <ng-container rowOutlet/>\n      <ng-container noDataRowOutlet/>\n    </tbody>\n    <tfoot role=\"rowgroup\">\n      <ng-container footerRowOutlet/>\n    </tfoot>\n  } @else {\n    <ng-container headerRowOutlet/>\n    <ng-container rowOutlet/>\n    <ng-container noDataRowOutlet/>\n    <ng-container footerRowOutlet/>\n  }\n`;\n\n/**\n * Interface used to conveniently type the possible context interfaces for the render row.\n * @docs-private\n */\nexport interface RowContext<T>\n  extends CdkCellOutletMultiRowContext<T>,\n    CdkCellOutletRowContext<T> {}\n\n/**\n * Class used to conveniently type the embedded view ref for rows with a context.\n * @docs-private\n */\nabstract class RowViewRef<T> extends EmbeddedViewRef<RowContext<T>> {}\n\n/**\n * Set of properties that represents the identity of a single rendered row.\n *\n * When the table needs to determine the list of rows to render, it will do so by iterating through\n * each data object and evaluating its list of row templates to display (when multiTemplateDataRows\n * is false, there is only one template per data object). For each pair of data object and row\n * template, a `RenderRow` is added to the list of rows to render. If the data object and row\n * template pair has already been rendered, the previously used `RenderRow` is added; else a new\n * `RenderRow` is * created. Once the list is complete and all data objects have been iterated\n * through, a diff is performed to determine the changes that need to be made to the rendered rows.\n *\n * @docs-private\n */\nexport interface RenderRow<T> {\n  data: T;\n  dataIndex: number;\n  rowDef: CdkRowDef<T>;\n}\n\n/**\n * A data table that can render a header row, data rows, and a footer row.\n * Uses the dataSource input to determine the data to be rendered. The data can be provided either\n * as a data array, an Observable stream that emits the data array to render, or a DataSource with a\n * connect function that will return an Observable stream that emits the data array to render.\n */\n@Component({\n  selector: 'cdk-table, table[cdk-table]',\n  exportAs: 'cdkTable',\n  template: CDK_TABLE_TEMPLATE,\n  styleUrl: 'table.css',\n  host: {\n    'class': 'cdk-table',\n    '[class.cdk-table-fixed-layout]': 'fixedLayout',\n  },\n  encapsulation: ViewEncapsulation.None,\n  // The \"OnPush\" status for the `MatTable` component is effectively a noop, so we are removing it.\n  // The view for `MatTable` consists entirely of templates declared in other views. As they are\n  // declared elsewhere, they are checked when their declaration points are checked.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  providers: [\n    {provide: CDK_TABLE, useExisting: CdkTable},\n    {provide: _VIEW_REPEATER_STRATEGY, useClass: _DisposeViewRepeaterStrategy},\n    {provide: _COALESCED_STYLE_SCHEDULER, useClass: _CoalescedStyleScheduler},\n    // Prevent nested tables from seeing this table's StickyPositioningListener.\n    {provide: STICKY_POSITIONING_LISTENER, useValue: null},\n  ],\n  imports: [HeaderRowOutlet, DataRowOutlet, NoDataRowOutlet, FooterRowOutlet],\n})\nexport class CdkTable<T>\n  implements AfterContentInit, AfterContentChecked, CollectionViewer, OnDestroy, OnInit\n{\n  protected readonly _differs = inject(IterableDiffers);\n  protected readonly _changeDetectorRef = inject(ChangeDetectorRef);\n  protected readonly _elementRef = inject(ElementRef);\n  protected readonly _dir = inject(Directionality, {optional: true});\n  private _platform = inject(Platform);\n  protected readonly _viewRepeater =\n    inject<_ViewRepeater<T, RenderRow<T>, RowContext<T>>>(_VIEW_REPEATER_STRATEGY);\n  protected readonly _coalescedStyleScheduler = inject<_CoalescedStyleScheduler>(\n    _COALESCED_STYLE_SCHEDULER,\n  );\n  private readonly _viewportRuler = inject(ViewportRuler);\n  protected readonly _stickyPositioningListener = inject<StickyPositioningListener>(\n    STICKY_POSITIONING_LISTENER,\n    {optional: true, skipSelf: true},\n  )!;\n\n  private _document = inject(DOCUMENT);\n\n  /** Latest data provided by the data source. */\n  protected _data: readonly T[] | undefined;\n\n  /** Subject that emits when the component has been destroyed. */\n  private readonly _onDestroy = new Subject<void>();\n\n  /** List of the rendered rows as identified by their `RenderRow` object. */\n  private _renderRows: RenderRow<T>[];\n\n  /** Subscription that listens for the data provided by the data source. */\n  private _renderChangeSubscription: Subscription | null;\n\n  /**\n   * Map of all the user's defined columns (header, data, and footer cell template) identified by\n   * name. Collection populated by the column definitions gathered by `ContentChildren` as well as\n   * any custom column definitions added to `_customColumnDefs`.\n   */\n  private _columnDefsByName = new Map<string, CdkColumnDef>();\n\n  /**\n   * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n   * using `ContentChildren` as well as any custom row definitions added to `_customRowDefs`.\n   */\n  private _rowDefs: CdkRowDef<T>[];\n\n  /**\n   * Set of all header row definitions that can be used by this table. Populated by the rows\n   * gathered by using `ContentChildren` as well as any custom row definitions added to\n   * `_customHeaderRowDefs`.\n   */\n  private _headerRowDefs: CdkHeaderRowDef[];\n\n  /**\n   * Set of all row definitions that can be used by this table. Populated by the rows gathered by\n   * using `ContentChildren` as well as any custom row definitions added to\n   * `_customFooterRowDefs`.\n   */\n  private _footerRowDefs: CdkFooterRowDef[];\n\n  /** Differ used to find the changes in the data provided by the data source. */\n  private _dataDiffer: IterableDiffer<RenderRow<T>>;\n\n  /** Stores the row definition that does not have a when predicate. */\n  private _defaultRowDef: CdkRowDef<T> | null;\n\n  /**\n   * Column definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * column definitions as *its* content child.\n   */\n  private _customColumnDefs = new Set<CdkColumnDef>();\n\n  /**\n   * Data row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * built-in data rows as *its* content child.\n   */\n  private _customRowDefs = new Set<CdkRowDef<T>>();\n\n  /**\n   * Header row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has\n   * built-in header rows as *its* content child.\n   */\n  private _customHeaderRowDefs = new Set<CdkHeaderRowDef>();\n\n  /**\n   * Footer row definitions that were defined outside of the direct content children of the table.\n   * These will be defined when, e.g., creating a wrapper around the cdkTable that has a\n   * built-in footer row as *its* content child.\n   */\n  private _customFooterRowDefs = new Set<CdkFooterRowDef>();\n\n  /** No data row that was defined outside of the direct content children of the table. */\n  private _customNoDataRow: CdkNoDataRow | null;\n\n  /**\n   * Whether the header row definition has been changed. Triggers an update to the header row after\n   * content is checked. Initialized as true so that the table renders the initial set of rows.\n   */\n  private _headerRowDefChanged = true;\n\n  /**\n   * Whether the footer row definition has been changed. Triggers an update to the footer row after\n   * content is checked. Initialized as true so that the table renders the initial set of rows.\n   */\n  private _footerRowDefChanged = true;\n\n  /**\n   * Whether the sticky column styles need to be updated. Set to `true` when the visible columns\n   * change.\n   */\n  private _stickyColumnStylesNeedReset = true;\n\n  /**\n   * Whether the sticky styler should recalculate cell widths when applying sticky styles. If\n   * `false`, cached values will be used instead. This is only applicable to tables with\n   * {@link fixedLayout} enabled. For other tables, cell widths will always be recalculated.\n   */\n  private _forceRecalculateCellWidths = true;\n\n  /**\n   * Cache of the latest rendered `RenderRow` objects as a map for easy retrieval when constructing\n   * a new list of `RenderRow` objects for rendering rows. Since the new list is constructed with\n   * the cached `RenderRow` objects when possible, the row identity is preserved when the data\n   * and row template matches, which allows the `IterableDiffer` to check rows by reference\n   * and understand which rows are added/moved/removed.\n   *\n   * Implemented as a map of maps where the first key is the `data: T` object and the second is the\n   * `CdkRowDef<T>` object. With the two keys, the cache points to a `RenderRow<T>` object that\n   * contains an array of created pairs. The array is necessary to handle cases where the data\n   * array contains multiple duplicate data objects and each instantiated `RenderRow` must be\n   * stored.\n   */\n  private _cachedRenderRowsMap = new Map<T, WeakMap<CdkRowDef<T>, RenderRow<T>[]>>();\n\n  /** Whether the table is applied to a native `<table>`. */\n  protected _isNativeHtmlTable: boolean;\n\n  /**\n   * Utility class that is responsible for applying the appropriate sticky positioning styles to\n   * the table's rows and cells.\n   */\n  private _stickyStyler: StickyStyler;\n\n  /**\n   * CSS class added to any row or cell that has sticky positioning applied. May be overridden by\n   * table subclasses.\n   */\n  protected stickyCssClass: string = 'cdk-table-sticky';\n\n  /**\n   * Whether to manually add position: sticky to all sticky cell elements. Not needed if\n   * the position is set in a selector associated with the value of stickyCssClass. May be\n   * overridden by table subclasses\n   */\n  protected needsPositionStickyOnElement = true;\n\n  /** Whether the component is being rendered on the server. */\n  protected _isServer: boolean;\n\n  /** Whether the no data row is currently showing anything. */\n  private _isShowingNoDataRow = false;\n\n  /** Whether the table has rendered out all the outlets for the first time. */\n  private _hasAllOutlets = false;\n\n  /** Whether the table is done initializing. */\n  private _hasInitialized = false;\n\n  /** Aria role to apply to the table's cells based on the table's own role. */\n  _getCellRole(): string | null {\n    // Perform this lazily in case the table's role was updated by a directive after construction.\n    if (this._cellRoleInternal === undefined) {\n      // Note that we set `role=\"cell\"` even on native `td` elements,\n      // because some browsers seem to require it. See #29784.\n      const tableRole = this._elementRef.nativeElement.getAttribute('role');\n      return tableRole === 'grid' || tableRole === 'treegrid' ? 'gridcell' : 'cell';\n    }\n\n    return this._cellRoleInternal;\n  }\n  private _cellRoleInternal: string | null | undefined = undefined;\n\n  /**\n   * Tracking function that will be used to check the differences in data changes. Used similarly\n   * to `ngFor` `trackBy` function. Optimize row operations by identifying a row based on its data\n   * relative to the function to know if a row should be added/removed/moved.\n   * Accepts a function that takes two parameters, `index` and `item`.\n   */\n  @Input()\n  get trackBy(): TrackByFunction<T> {\n    return this._trackByFn;\n  }\n  set trackBy(fn: TrackByFunction<T>) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && fn != null && typeof fn !== 'function') {\n      console.warn(`trackBy must be a function, but received ${JSON.stringify(fn)}.`);\n    }\n    this._trackByFn = fn;\n  }\n  private _trackByFn: TrackByFunction<T>;\n\n  /**\n   * The table's source of data, which can be provided in three ways (in order of complexity):\n   *   - Simple data array (each object represents one table row)\n   *   - Stream that emits a data array each time the array changes\n   *   - `DataSource` object that implements the connect/disconnect interface.\n   *\n   * If a data array is provided, the table must be notified when the array's objects are\n   * added, removed, or moved. This can be done by calling the `renderRows()` function which will\n   * render the diff since the last table render. If the data array reference is changed, the table\n   * will automatically trigger an update to the rows.\n   *\n   * When providing an Observable stream, the table will trigger an update automatically when the\n   * stream emits a new array of data.\n   *\n   * Finally, when providing a `DataSource` object, the table will use the Observable stream\n   * provided by the connect function and trigger updates when that stream emits new data array\n   * values. During the table's ngOnDestroy or when the data source is removed from the table, the\n   * table will call the DataSource's `disconnect` function (may be useful for cleaning up any\n   * subscriptions registered during the connect process).\n   */\n  @Input()\n  get dataSource(): CdkTableDataSourceInput<T> {\n    return this._dataSource;\n  }\n  set dataSource(dataSource: CdkTableDataSourceInput<T>) {\n    if (this._dataSource !== dataSource) {\n      this._switchDataSource(dataSource);\n    }\n  }\n  private _dataSource: CdkTableDataSourceInput<T>;\n\n  /**\n   * Whether to allow multiple rows per data object by evaluating which rows evaluate their 'when'\n   * predicate to true. If `multiTemplateDataRows` is false, which is the default value, then each\n   * dataobject will render the first row that evaluates its when predicate to true, in the order\n   * defined in the table, or otherwise the default row which does not have a when predicate.\n   */\n  @Input({transform: booleanAttribute})\n  get multiTemplateDataRows(): boolean {\n    return this._multiTemplateDataRows;\n  }\n  set multiTemplateDataRows(value: boolean) {\n    this._multiTemplateDataRows = value;\n\n    // In Ivy if this value is set via a static attribute (e.g. <table multiTemplateDataRows>),\n    // this setter will be invoked before the row outlet has been defined hence the null check.\n    if (this._rowOutlet && this._rowOutlet.viewContainer.length) {\n      this._forceRenderDataRows();\n      this.updateStickyColumnStyles();\n    }\n  }\n  _multiTemplateDataRows: boolean = false;\n\n  /**\n   * Whether to use a fixed table layout. Enabling this option will enforce consistent column widths\n   * and optimize rendering sticky styles for native tables. No-op for flex tables.\n   */\n  @Input({transform: booleanAttribute})\n  get fixedLayout(): boolean {\n    return this._fixedLayout;\n  }\n  set fixedLayout(value: boolean) {\n    this._fixedLayout = value;\n\n    // Toggling `fixedLayout` may change column widths. Sticky column styles should be recalculated.\n    this._forceRecalculateCellWidths = true;\n    this._stickyColumnStylesNeedReset = true;\n  }\n  private _fixedLayout: boolean = false;\n\n  /**\n   * Emits when the table completes rendering a set of data rows based on the latest data from the\n   * data source, even if the set of rows is empty.\n   */\n  @Output()\n  readonly contentChanged = new EventEmitter<void>();\n\n  // TODO(andrewseguin): Remove max value as the end index\n  //   and instead calculate the view on init and scroll.\n  /**\n   * Stream containing the latest information on what rows are being displayed on screen.\n   * Can be used by the data source to as a heuristic of what data should be provided.\n   *\n   * @docs-private\n   */\n  readonly viewChange = new BehaviorSubject<{start: number; end: number}>({\n    start: 0,\n    end: Number.MAX_VALUE,\n  });\n\n  // Outlets in the table's template where the header, data rows, and footer will be inserted.\n  _rowOutlet: DataRowOutlet;\n  _headerRowOutlet: HeaderRowOutlet;\n  _footerRowOutlet: FooterRowOutlet;\n  _noDataRowOutlet: NoDataRowOutlet;\n\n  /**\n   * The column definitions provided by the user that contain what the header, data, and footer\n   * cells should render for each column.\n   */\n  @ContentChildren(CdkColumnDef, {descendants: true}) _contentColumnDefs: QueryList<CdkColumnDef>;\n\n  /** Set of data row definitions that were provided to the table as content children. */\n  @ContentChildren(CdkRowDef, {descendants: true}) _contentRowDefs: QueryList<CdkRowDef<T>>;\n\n  /** Set of header row definitions that were provided to the table as content children. */\n  @ContentChildren(CdkHeaderRowDef, {\n    descendants: true,\n  })\n  _contentHeaderRowDefs: QueryList<CdkHeaderRowDef>;\n\n  /** Set of footer row definitions that were provided to the table as content children. */\n  @ContentChildren(CdkFooterRowDef, {\n    descendants: true,\n  })\n  _contentFooterRowDefs: QueryList<CdkFooterRowDef>;\n\n  /** Row definition that will only be rendered if there's no data in the table. */\n  @ContentChild(CdkNoDataRow) _noDataRow: CdkNoDataRow;\n\n  private _injector = inject(Injector);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const role = inject(new HostAttributeToken('role'), {optional: true});\n\n    if (!role) {\n      this._elementRef.nativeElement.setAttribute('role', 'table');\n    }\n\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeHtmlTable = this._elementRef.nativeElement.nodeName === 'TABLE';\n\n    // Set up the trackBy function so that it uses the `RenderRow` as its identity by default. If\n    // the user has provided a custom trackBy, return the result of that function as evaluated\n    // with the values of the `RenderRow`'s data and index.\n    this._dataDiffer = this._differs.find([]).create((_i: number, dataRow: RenderRow<T>) => {\n      return this.trackBy ? this.trackBy(dataRow.dataIndex, dataRow.data) : dataRow;\n    });\n  }\n\n  ngOnInit() {\n    this._setupStickyStyler();\n\n    this._viewportRuler\n      .change()\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe(() => {\n        this._forceRecalculateCellWidths = true;\n      });\n  }\n\n  ngAfterContentInit() {\n    this._hasInitialized = true;\n  }\n\n  ngAfterContentChecked() {\n    // Only start re-rendering in `ngAfterContentChecked` after the first render.\n    if (this._canRender()) {\n      this._render();\n    }\n  }\n\n  ngOnDestroy() {\n    this._stickyStyler?.destroy();\n\n    [\n      this._rowOutlet?.viewContainer,\n      this._headerRowOutlet?.viewContainer,\n      this._footerRowOutlet?.viewContainer,\n      this._cachedRenderRowsMap,\n      this._customColumnDefs,\n      this._customRowDefs,\n      this._customHeaderRowDefs,\n      this._customFooterRowDefs,\n      this._columnDefsByName,\n    ].forEach((def: ViewContainerRef | Set<unknown> | Map<unknown, unknown> | undefined) => {\n      def?.clear();\n    });\n\n    this._headerRowDefs = [];\n    this._footerRowDefs = [];\n    this._defaultRowDef = null;\n    this._onDestroy.next();\n    this._onDestroy.complete();\n\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n  }\n\n  /**\n   * Renders rows based on the table's latest set of data, which was either provided directly as an\n   * input or retrieved through an Observable stream (directly or from a DataSource).\n   * Checks for differences in the data since the last diff to perform only the necessary\n   * changes (add/remove/move rows).\n   *\n   * If the table's data source is a DataSource or Observable, this will be invoked automatically\n   * each time the provided Observable stream emits a new data array. Otherwise if your data is\n   * an array, this function will need to be called to render any changes.\n   */\n  renderRows() {\n    this._renderRows = this._getAllRenderRows();\n    const changes = this._dataDiffer.diff(this._renderRows);\n    if (!changes) {\n      this._updateNoDataRow();\n      this.contentChanged.next();\n      return;\n    }\n    const viewContainer = this._rowOutlet.viewContainer;\n\n    this._viewRepeater.applyChanges(\n      changes,\n      viewContainer,\n      (\n        record: IterableChangeRecord<RenderRow<T>>,\n        _adjustedPreviousIndex: number | null,\n        currentIndex: number | null,\n      ) => this._getEmbeddedViewArgs(record.item, currentIndex!),\n      record => record.item.data,\n      (change: _ViewRepeaterItemChange<RenderRow<T>, RowContext<T>>) => {\n        if (change.operation === _ViewRepeaterOperation.INSERTED && change.context) {\n          this._renderCellTemplateForItem(change.record.item.rowDef, change.context);\n        }\n      },\n    );\n\n    // Update the meta context of a row's context data (index, count, first, last, ...)\n    this._updateRowIndexContext();\n\n    // Update rows that did not get added/removed/moved but may have had their identity changed,\n    // e.g. if trackBy matched data on some property but the actual data reference changed.\n    changes.forEachIdentityChange((record: IterableChangeRecord<RenderRow<T>>) => {\n      const rowView = <RowViewRef<T>>viewContainer.get(record.currentIndex!);\n      rowView.context.$implicit = record.item.data;\n    });\n\n    this._updateNoDataRow();\n\n    this.contentChanged.next();\n    this.updateStickyColumnStyles();\n  }\n\n  /** Adds a column definition that was not included as part of the content children. */\n  addColumnDef(columnDef: CdkColumnDef) {\n    this._customColumnDefs.add(columnDef);\n  }\n\n  /** Removes a column definition that was not included as part of the content children. */\n  removeColumnDef(columnDef: CdkColumnDef) {\n    this._customColumnDefs.delete(columnDef);\n  }\n\n  /** Adds a row definition that was not included as part of the content children. */\n  addRowDef(rowDef: CdkRowDef<T>) {\n    this._customRowDefs.add(rowDef);\n  }\n\n  /** Removes a row definition that was not included as part of the content children. */\n  removeRowDef(rowDef: CdkRowDef<T>) {\n    this._customRowDefs.delete(rowDef);\n  }\n\n  /** Adds a header row definition that was not included as part of the content children. */\n  addHeaderRowDef(headerRowDef: CdkHeaderRowDef) {\n    this._customHeaderRowDefs.add(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n\n  /** Removes a header row definition that was not included as part of the content children. */\n  removeHeaderRowDef(headerRowDef: CdkHeaderRowDef) {\n    this._customHeaderRowDefs.delete(headerRowDef);\n    this._headerRowDefChanged = true;\n  }\n\n  /** Adds a footer row definition that was not included as part of the content children. */\n  addFooterRowDef(footerRowDef: CdkFooterRowDef) {\n    this._customFooterRowDefs.add(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n\n  /** Removes a footer row definition that was not included as part of the content children. */\n  removeFooterRowDef(footerRowDef: CdkFooterRowDef) {\n    this._customFooterRowDefs.delete(footerRowDef);\n    this._footerRowDefChanged = true;\n  }\n\n  /** Sets a no data row definition that was not included as a part of the content children. */\n  setNoDataRow(noDataRow: CdkNoDataRow | null) {\n    this._customNoDataRow = noDataRow;\n  }\n\n  /**\n   * Updates the header sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the top. Then, evaluating which cells need to be stuck to the top. This is\n   * automatically called when the header row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyHeaderRowStyles(): void {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n\n    // Hide the thead element if there are no header rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const thead = closestTableSection(this._headerRowOutlet, 'thead');\n      if (thead) {\n        thead.style.display = headerRows.length ? '' : 'none';\n      }\n    }\n\n    const stickyStates = this._headerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(headerRows, ['top']);\n    this._stickyStyler.stickRows(headerRows, stickyStates, 'top');\n\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._headerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n\n  /**\n   * Updates the footer sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the bottom. Then, evaluating which cells need to be stuck to the bottom. This is\n   * automatically called when the footer row changes its displayed set of columns, or if its\n   * sticky input changes. May be called manually for cases where the cell content changes outside\n   * of these events.\n   */\n  updateStickyFooterRowStyles(): void {\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n\n    // Hide the tfoot element if there are no footer rows. This is necessary to satisfy\n    // overzealous a11y checkers that fail because the `rowgroup` element does not contain\n    // required child `row`.\n    if (this._isNativeHtmlTable) {\n      const tfoot = closestTableSection(this._footerRowOutlet, 'tfoot');\n      if (tfoot) {\n        tfoot.style.display = footerRows.length ? '' : 'none';\n      }\n    }\n\n    const stickyStates = this._footerRowDefs.map(def => def.sticky);\n    this._stickyStyler.clearStickyPositioning(footerRows, ['bottom']);\n    this._stickyStyler.stickRows(footerRows, stickyStates, 'bottom');\n    this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement, stickyStates);\n\n    // Reset the dirty state of the sticky input change since it has been used.\n    this._footerRowDefs.forEach(def => def.resetStickyChanged());\n  }\n\n  /**\n   * Updates the column sticky styles. First resets all applied styles with respect to the cells\n   * sticking to the left and right. Then sticky styles are added for the left and right according\n   * to the column definitions for each cell in each row. This is automatically called when\n   * the data source provides a new set of data or when a column definition changes its sticky\n   * input. May be called manually for cases where the cell content changes outside of these events.\n   */\n  updateStickyColumnStyles() {\n    const headerRows = this._getRenderedRows(this._headerRowOutlet);\n    const dataRows = this._getRenderedRows(this._rowOutlet);\n    const footerRows = this._getRenderedRows(this._footerRowOutlet);\n\n    // For tables not using a fixed layout, the column widths may change when new rows are rendered.\n    // In a table using a fixed layout, row content won't affect column width, so sticky styles\n    // don't need to be cleared unless either the sticky column config changes or one of the row\n    // defs change.\n    if ((this._isNativeHtmlTable && !this._fixedLayout) || this._stickyColumnStylesNeedReset) {\n      // Clear the left and right positioning from all columns in the table across all rows since\n      // sticky columns span across all table sections (header, data, footer)\n      this._stickyStyler.clearStickyPositioning(\n        [...headerRows, ...dataRows, ...footerRows],\n        ['left', 'right'],\n      );\n      this._stickyColumnStylesNeedReset = false;\n    }\n\n    // Update the sticky styles for each header row depending on the def's sticky state\n    headerRows.forEach((headerRow, i) => {\n      this._addStickyColumnStyles([headerRow], this._headerRowDefs[i]);\n    });\n\n    // Update the sticky styles for each data row depending on its def's sticky state\n    this._rowDefs.forEach(rowDef => {\n      // Collect all the rows rendered with this row definition.\n      const rows: HTMLElement[] = [];\n      for (let i = 0; i < dataRows.length; i++) {\n        if (this._renderRows[i].rowDef === rowDef) {\n          rows.push(dataRows[i]);\n        }\n      }\n\n      this._addStickyColumnStyles(rows, rowDef);\n    });\n\n    // Update the sticky styles for each footer row depending on the def's sticky state\n    footerRows.forEach((footerRow, i) => {\n      this._addStickyColumnStyles([footerRow], this._footerRowDefs[i]);\n    });\n\n    // Reset the dirty state of the sticky input change since it has been used.\n    Array.from(this._columnDefsByName.values()).forEach(def => def.resetStickyChanged());\n  }\n\n  /** Invoked whenever an outlet is created and has been assigned to the table. */\n  _outletAssigned(): void {\n    // Trigger the first render once all outlets have been assigned. We do it this way, as\n    // opposed to waiting for the next `ngAfterContentChecked`, because we don't know when\n    // the next change detection will happen.\n    // Also we can't use queries to resolve the outlets, because they're wrapped in a\n    // conditional, so we have to rely on them being assigned via DI.\n    if (\n      !this._hasAllOutlets &&\n      this._rowOutlet &&\n      this._headerRowOutlet &&\n      this._footerRowOutlet &&\n      this._noDataRowOutlet\n    ) {\n      this._hasAllOutlets = true;\n\n      // In some setups this may fire before `ngAfterContentInit`\n      // so we need a check here. See #28538.\n      if (this._canRender()) {\n        this._render();\n      }\n    }\n  }\n\n  /** Whether the table has all the information to start rendering. */\n  private _canRender(): boolean {\n    return this._hasAllOutlets && this._hasInitialized;\n  }\n\n  /** Renders the table if its state has changed. */\n  private _render(): void {\n    // Cache the row and column definitions gathered by ContentChildren and programmatic injection.\n    this._cacheRowDefs();\n    this._cacheColumnDefs();\n\n    // Make sure that the user has at least added header, footer, or data row def.\n    if (\n      !this._headerRowDefs.length &&\n      !this._footerRowDefs.length &&\n      !this._rowDefs.length &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw getTableMissingRowDefsError();\n    }\n\n    // Render updates if the list of columns have been changed for the header, row, or footer defs.\n    const columnsChanged = this._renderUpdatedColumns();\n    const rowDefsChanged = columnsChanged || this._headerRowDefChanged || this._footerRowDefChanged;\n    // Ensure sticky column styles are reset if set to `true` elsewhere.\n    this._stickyColumnStylesNeedReset = this._stickyColumnStylesNeedReset || rowDefsChanged;\n    this._forceRecalculateCellWidths = rowDefsChanged;\n\n    // If the header row definition has been changed, trigger a render to the header row.\n    if (this._headerRowDefChanged) {\n      this._forceRenderHeaderRows();\n      this._headerRowDefChanged = false;\n    }\n\n    // If the footer row definition has been changed, trigger a render to the footer row.\n    if (this._footerRowDefChanged) {\n      this._forceRenderFooterRows();\n      this._footerRowDefChanged = false;\n    }\n\n    // If there is a data source and row definitions, connect to the data source unless a\n    // connection has already been made.\n    if (this.dataSource && this._rowDefs.length > 0 && !this._renderChangeSubscription) {\n      this._observeRenderChanges();\n    } else if (this._stickyColumnStylesNeedReset) {\n      // In the above case, _observeRenderChanges will result in updateStickyColumnStyles being\n      // called when it row data arrives. Otherwise, we need to call it proactively.\n      this.updateStickyColumnStyles();\n    }\n\n    this._checkStickyStates();\n  }\n\n  /**\n   * Get the list of RenderRow objects to render according to the current list of data and defined\n   * row definitions. If the previous list already contained a particular pair, it should be reused\n   * so that the differ equates their references.\n   */\n  private _getAllRenderRows(): RenderRow<T>[] {\n    const renderRows: RenderRow<T>[] = [];\n\n    // Store the cache and create a new one. Any re-used RenderRow objects will be moved into the\n    // new cache while unused ones can be picked up by garbage collection.\n    const prevCachedRenderRows = this._cachedRenderRowsMap;\n    this._cachedRenderRowsMap = new Map();\n\n    if (!this._data) {\n      return renderRows;\n    }\n\n    // For each data object, get the list of rows that should be rendered, represented by the\n    // respective `RenderRow` object which is the pair of `data` and `CdkRowDef`.\n    for (let i = 0; i < this._data.length; i++) {\n      let data = this._data[i];\n      const renderRowsForData = this._getRenderRowsForData(data, i, prevCachedRenderRows.get(data));\n\n      if (!this._cachedRenderRowsMap.has(data)) {\n        this._cachedRenderRowsMap.set(data, new WeakMap());\n      }\n\n      for (let j = 0; j < renderRowsForData.length; j++) {\n        let renderRow = renderRowsForData[j];\n\n        const cache = this._cachedRenderRowsMap.get(renderRow.data)!;\n        if (cache.has(renderRow.rowDef)) {\n          cache.get(renderRow.rowDef)!.push(renderRow);\n        } else {\n          cache.set(renderRow.rowDef, [renderRow]);\n        }\n        renderRows.push(renderRow);\n      }\n    }\n\n    return renderRows;\n  }\n\n  /**\n   * Gets a list of `RenderRow<T>` for the provided data object and any `CdkRowDef` objects that\n   * should be rendered for this data. Reuses the cached RenderRow objects if they match the same\n   * `(T, CdkRowDef)` pair.\n   */\n  private _getRenderRowsForData(\n    data: T,\n    dataIndex: number,\n    cache?: WeakMap<CdkRowDef<T>, RenderRow<T>[]>,\n  ): RenderRow<T>[] {\n    const rowDefs = this._getRowDefs(data, dataIndex);\n\n    return rowDefs.map(rowDef => {\n      const cachedRenderRows = cache && cache.has(rowDef) ? cache.get(rowDef)! : [];\n      if (cachedRenderRows.length) {\n        const dataRow = cachedRenderRows.shift()!;\n        dataRow.dataIndex = dataIndex;\n        return dataRow;\n      } else {\n        return {data, rowDef, dataIndex};\n      }\n    });\n  }\n\n  /** Update the map containing the content's column definitions. */\n  private _cacheColumnDefs() {\n    this._columnDefsByName.clear();\n\n    const columnDefs = mergeArrayAndSet(\n      this._getOwnDefs(this._contentColumnDefs),\n      this._customColumnDefs,\n    );\n    columnDefs.forEach(columnDef => {\n      if (\n        this._columnDefsByName.has(columnDef.name) &&\n        (typeof ngDevMode === 'undefined' || ngDevMode)\n      ) {\n        throw getTableDuplicateColumnNameError(columnDef.name);\n      }\n      this._columnDefsByName.set(columnDef.name, columnDef);\n    });\n  }\n\n  /** Update the list of all available row definitions that can be used. */\n  private _cacheRowDefs() {\n    this._headerRowDefs = mergeArrayAndSet(\n      this._getOwnDefs(this._contentHeaderRowDefs),\n      this._customHeaderRowDefs,\n    );\n    this._footerRowDefs = mergeArrayAndSet(\n      this._getOwnDefs(this._contentFooterRowDefs),\n      this._customFooterRowDefs,\n    );\n    this._rowDefs = mergeArrayAndSet(this._getOwnDefs(this._contentRowDefs), this._customRowDefs);\n\n    // After all row definitions are determined, find the row definition to be considered default.\n    const defaultRowDefs = this._rowDefs.filter(def => !def.when);\n    if (\n      !this.multiTemplateDataRows &&\n      defaultRowDefs.length > 1 &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw getTableMultipleDefaultRowDefsError();\n    }\n    this._defaultRowDef = defaultRowDefs[0];\n  }\n\n  /**\n   * Check if the header, data, or footer rows have changed what columns they want to display or\n   * whether the sticky states have changed for the header or footer. If there is a diff, then\n   * re-render that section.\n   */\n  private _renderUpdatedColumns(): boolean {\n    const columnsDiffReducer = (acc: boolean, def: BaseRowDef) => {\n      // The differ should be run for every column, even if `acc` is already\n      // true (see #29922)\n      const diff = !!def.getColumnsDiff();\n      return acc || diff;\n    };\n\n    // Force re-render data rows if the list of column definitions have changed.\n    const dataColumnsChanged = this._rowDefs.reduce(columnsDiffReducer, false);\n    if (dataColumnsChanged) {\n      this._forceRenderDataRows();\n    }\n\n    // Force re-render header/footer rows if the list of column definitions have changed.\n    const headerColumnsChanged = this._headerRowDefs.reduce(columnsDiffReducer, false);\n    if (headerColumnsChanged) {\n      this._forceRenderHeaderRows();\n    }\n\n    const footerColumnsChanged = this._footerRowDefs.reduce(columnsDiffReducer, false);\n    if (footerColumnsChanged) {\n      this._forceRenderFooterRows();\n    }\n\n    return dataColumnsChanged || headerColumnsChanged || footerColumnsChanged;\n  }\n\n  /**\n   * Switch to the provided data source by resetting the data and unsubscribing from the current\n   * render change subscription if one exists. If the data source is null, interpret this by\n   * clearing the row outlet. Otherwise start listening for new data.\n   */\n  private _switchDataSource(dataSource: CdkTableDataSourceInput<T>) {\n    this._data = [];\n\n    if (isDataSource(this.dataSource)) {\n      this.dataSource.disconnect(this);\n    }\n\n    // Stop listening for data from the previous data source.\n    if (this._renderChangeSubscription) {\n      this._renderChangeSubscription.unsubscribe();\n      this._renderChangeSubscription = null;\n    }\n\n    if (!dataSource) {\n      if (this._dataDiffer) {\n        this._dataDiffer.diff([]);\n      }\n      if (this._rowOutlet) {\n        this._rowOutlet.viewContainer.clear();\n      }\n    }\n\n    this._dataSource = dataSource;\n  }\n\n  /** Set up a subscription for the data provided by the data source. */\n  private _observeRenderChanges() {\n    // If no data source has been set, there is nothing to observe for changes.\n    if (!this.dataSource) {\n      return;\n    }\n\n    let dataStream: Observable<readonly T[]> | undefined;\n\n    if (isDataSource(this.dataSource)) {\n      dataStream = this.dataSource.connect(this);\n    } else if (isObservable(this.dataSource)) {\n      dataStream = this.dataSource;\n    } else if (Array.isArray(this.dataSource)) {\n      dataStream = observableOf(this.dataSource);\n    }\n\n    if (dataStream === undefined && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableUnknownDataSourceError();\n    }\n\n    this._renderChangeSubscription = dataStream!\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe(data => {\n        this._data = data || [];\n        this.renderRows();\n      });\n  }\n\n  /**\n   * Clears any existing content in the header row outlet and creates a new embedded view\n   * in the outlet using the header row definition.\n   */\n  private _forceRenderHeaderRows() {\n    // Clear the header row outlet if any content exists.\n    if (this._headerRowOutlet.viewContainer.length > 0) {\n      this._headerRowOutlet.viewContainer.clear();\n    }\n\n    this._headerRowDefs.forEach((def, i) => this._renderRow(this._headerRowOutlet, def, i));\n    this.updateStickyHeaderRowStyles();\n  }\n\n  /**\n   * Clears any existing content in the footer row outlet and creates a new embedded view\n   * in the outlet using the footer row definition.\n   */\n  private _forceRenderFooterRows() {\n    // Clear the footer row outlet if any content exists.\n    if (this._footerRowOutlet.viewContainer.length > 0) {\n      this._footerRowOutlet.viewContainer.clear();\n    }\n\n    this._footerRowDefs.forEach((def, i) => this._renderRow(this._footerRowOutlet, def, i));\n    this.updateStickyFooterRowStyles();\n  }\n\n  /** Adds the sticky column styles for the rows according to the columns' stick states. */\n  private _addStickyColumnStyles(rows: HTMLElement[], rowDef: BaseRowDef) {\n    const columnDefs = Array.from(rowDef?.columns || []).map(columnName => {\n      const columnDef = this._columnDefsByName.get(columnName);\n      if (!columnDef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnName);\n      }\n      return columnDef!;\n    });\n    const stickyStartStates = columnDefs.map(columnDef => columnDef.sticky);\n    const stickyEndStates = columnDefs.map(columnDef => columnDef.stickyEnd);\n    this._stickyStyler.updateStickyColumns(\n      rows,\n      stickyStartStates,\n      stickyEndStates,\n      !this._fixedLayout || this._forceRecalculateCellWidths,\n    );\n  }\n\n  /** Gets the list of rows that have been rendered in the row outlet. */\n  _getRenderedRows(rowOutlet: RowOutlet): HTMLElement[] {\n    const renderedRows: HTMLElement[] = [];\n\n    for (let i = 0; i < rowOutlet.viewContainer.length; i++) {\n      const viewRef = rowOutlet.viewContainer.get(i)! as EmbeddedViewRef<any>;\n      renderedRows.push(viewRef.rootNodes[0]);\n    }\n\n    return renderedRows;\n  }\n\n  /**\n   * Get the matching row definitions that should be used for this row data. If there is only\n   * one row definition, it is returned. Otherwise, find the row definitions that has a when\n   * predicate that returns true with the data. If none return true, return the default row\n   * definition.\n   */\n  _getRowDefs(data: T, dataIndex: number): CdkRowDef<T>[] {\n    if (this._rowDefs.length == 1) {\n      return [this._rowDefs[0]];\n    }\n\n    let rowDefs: CdkRowDef<T>[] = [];\n    if (this.multiTemplateDataRows) {\n      rowDefs = this._rowDefs.filter(def => !def.when || def.when(dataIndex, data));\n    } else {\n      let rowDef =\n        this._rowDefs.find(def => def.when && def.when(dataIndex, data)) || this._defaultRowDef;\n      if (rowDef) {\n        rowDefs.push(rowDef);\n      }\n    }\n\n    if (!rowDefs.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableMissingMatchingRowDefError(data);\n    }\n\n    return rowDefs;\n  }\n\n  private _getEmbeddedViewArgs(\n    renderRow: RenderRow<T>,\n    index: number,\n  ): _ViewRepeaterItemInsertArgs<RowContext<T>> {\n    const rowDef = renderRow.rowDef;\n    const context: RowContext<T> = {$implicit: renderRow.data};\n    return {\n      templateRef: rowDef.template,\n      context,\n      index,\n    };\n  }\n\n  /**\n   * Creates a new row template in the outlet and fills it with the set of cell templates.\n   * Optionally takes a context to provide to the row and cells, as well as an optional index\n   * of where to place the new row template in the outlet.\n   */\n  private _renderRow(\n    outlet: RowOutlet,\n    rowDef: BaseRowDef,\n    index: number,\n    context: RowContext<T> = {},\n  ): EmbeddedViewRef<RowContext<T>> {\n    // TODO(andrewseguin): enforce that one outlet was instantiated from createEmbeddedView\n    const view = outlet.viewContainer.createEmbeddedView(rowDef.template, context, index);\n    this._renderCellTemplateForItem(rowDef, context);\n    return view;\n  }\n\n  private _renderCellTemplateForItem(rowDef: BaseRowDef, context: RowContext<T>) {\n    for (let cellTemplate of this._getCellTemplates(rowDef)) {\n      if (CdkCellOutlet.mostRecentCellOutlet) {\n        CdkCellOutlet.mostRecentCellOutlet._viewContainer.createEmbeddedView(cellTemplate, context);\n      }\n    }\n\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Updates the index-related context for each row to reflect any changes in the index of the rows,\n   * e.g. first/last/even/odd.\n   */\n  private _updateRowIndexContext() {\n    const viewContainer = this._rowOutlet.viewContainer;\n    for (let renderIndex = 0, count = viewContainer.length; renderIndex < count; renderIndex++) {\n      const viewRef = viewContainer.get(renderIndex) as RowViewRef<T>;\n      const context = viewRef.context as RowContext<T>;\n      context.count = count;\n      context.first = renderIndex === 0;\n      context.last = renderIndex === count - 1;\n      context.even = renderIndex % 2 === 0;\n      context.odd = !context.even;\n\n      if (this.multiTemplateDataRows) {\n        context.dataIndex = this._renderRows[renderIndex].dataIndex;\n        context.renderIndex = renderIndex;\n      } else {\n        context.index = this._renderRows[renderIndex].dataIndex;\n      }\n    }\n  }\n\n  /** Gets the column definitions for the provided row def. */\n  private _getCellTemplates(rowDef: BaseRowDef): TemplateRef<any>[] {\n    if (!rowDef || !rowDef.columns) {\n      return [];\n    }\n    return Array.from(rowDef.columns, columnId => {\n      const column = this._columnDefsByName.get(columnId);\n\n      if (!column && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getTableUnknownColumnError(columnId);\n      }\n\n      return rowDef.extractCellTemplate(column!);\n    });\n  }\n\n  /**\n   * Forces a re-render of the data rows. Should be called in cases where there has been an input\n   * change that affects the evaluation of which rows should be rendered, e.g. toggling\n   * `multiTemplateDataRows` or adding/removing row definitions.\n   */\n  private _forceRenderDataRows() {\n    this._dataDiffer.diff([]);\n    this._rowOutlet.viewContainer.clear();\n    this.renderRows();\n  }\n\n  /**\n   * Checks if there has been a change in sticky states since last check and applies the correct\n   * sticky styles. Since checking resets the \"dirty\" state, this should only be performed once\n   * during a change detection and after the inputs are settled (after content check).\n   */\n  private _checkStickyStates() {\n    const stickyCheckReducer = (\n      acc: boolean,\n      d: CdkHeaderRowDef | CdkFooterRowDef | CdkColumnDef,\n    ) => {\n      return acc || d.hasStickyChanged();\n    };\n\n    // Note that the check needs to occur for every definition since it notifies the definition\n    // that it can reset its dirty state. Using another operator like `some` may short-circuit\n    // remaining definitions and leave them in an unchecked state.\n\n    if (this._headerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyHeaderRowStyles();\n    }\n\n    if (this._footerRowDefs.reduce(stickyCheckReducer, false)) {\n      this.updateStickyFooterRowStyles();\n    }\n\n    if (Array.from(this._columnDefsByName.values()).reduce(stickyCheckReducer, false)) {\n      this._stickyColumnStylesNeedReset = true;\n      this.updateStickyColumnStyles();\n    }\n  }\n\n  /**\n   * Creates the sticky styler that will be used for sticky rows and columns. Listens\n   * for directionality changes and provides the latest direction to the styler. Re-applies column\n   * stickiness when directionality changes.\n   */\n  private _setupStickyStyler() {\n    const direction: Direction = this._dir ? this._dir.value : 'ltr';\n    this._stickyStyler = new StickyStyler(\n      this._isNativeHtmlTable,\n      this.stickyCssClass,\n      direction,\n      this._coalescedStyleScheduler,\n      this._platform.isBrowser,\n      this.needsPositionStickyOnElement,\n      this._stickyPositioningListener,\n      this._injector,\n    );\n    (this._dir ? this._dir.change : observableOf<Direction>())\n      .pipe(takeUntil(this._onDestroy))\n      .subscribe(value => {\n        this._stickyStyler.direction = value;\n        this.updateStickyColumnStyles();\n      });\n  }\n\n  /** Filters definitions that belong to this table from a QueryList. */\n  private _getOwnDefs<I extends {_table?: any}>(items: QueryList<I>): I[] {\n    return items.filter(item => !item._table || item._table === this);\n  }\n\n  /** Creates or removes the no data row, depending on whether any data is being shown. */\n  private _updateNoDataRow() {\n    const noDataRow = this._customNoDataRow || this._noDataRow;\n\n    if (!noDataRow) {\n      return;\n    }\n\n    const shouldShow = this._rowOutlet.viewContainer.length === 0;\n\n    if (shouldShow === this._isShowingNoDataRow) {\n      return;\n    }\n\n    const container = this._noDataRowOutlet.viewContainer;\n\n    if (shouldShow) {\n      const view = container.createEmbeddedView(noDataRow.templateRef);\n      const rootNode: HTMLElement | undefined = view.rootNodes[0];\n\n      // Only add the attributes if we have a single root node since it's hard\n      // to figure out which one to add it to when there are multiple.\n      if (view.rootNodes.length === 1 && rootNode?.nodeType === this._document.ELEMENT_NODE) {\n        rootNode.setAttribute('role', 'row');\n        rootNode.classList.add(noDataRow._contentClassName);\n      }\n    } else {\n      container.clear();\n    }\n\n    this._isShowingNoDataRow = shouldShow;\n\n    this._changeDetectorRef.markForCheck();\n  }\n}\n\n/** Utility function that gets a merged list of the entries in an array and values of a Set. */\nfunction mergeArrayAndSet<T>(array: T[], set: Set<T>): T[] {\n  return array.concat(Array.from(set));\n}\n\n/**\n * Finds the closest table section to an outlet. We can't use `HTMLElement.closest` for this,\n * because the node representing the outlet is a comment.\n */\nfunction closestTableSection(outlet: RowOutlet, section: string): HTMLElement | null {\n  const uppercaseSection = section.toUpperCase();\n  let current: Node | null = outlet.viewContainer.element.nativeElement;\n\n  while (current) {\n    // 1 is an element node.\n    const nodeName = current.nodeType === 1 ? (current as HTMLElement).nodeName : null;\n    if (nodeName === uppercaseSection) {\n      return current as HTMLElement;\n    } else if (nodeName === 'TABLE') {\n      // Stop traversing past the `table` node.\n      break;\n    }\n    current = current.parentNode;\n  }\n\n  return null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Input,\n  OnDestroy,\n  OnInit,\n  ViewChild,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {CdkCellDef, CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCell} from './cell';\nimport {CdkTable} from './table';\nimport {\n  getTableTextColumnMissingParentTableError,\n  getTableTextColumnMissingNameError,\n} from './table-errors';\nimport {TEXT_COLUMN_OPTIONS, TextColumnOptions} from './tokens';\n\n/**\n * Column that simply shows text content for the header and row cells. Assumes that the table\n * is using the native table implementation (`<table>`).\n *\n * By default, the name of this column will be the header text and data property accessor.\n * The header text can be overridden with the `headerText` input. Cell values can be overridden with\n * the `dataAccessor` input. Change the text justification to the start or end using the `justify`\n * input.\n */\n@Component({\n  selector: 'cdk-text-column',\n  template: `\n    <ng-container cdkColumnDef>\n      <th cdk-header-cell *cdkHeaderCellDef [style.text-align]=\"justify\">\n        {{headerText}}\n      </th>\n      <td cdk-cell *cdkCellDef=\"let data\" [style.text-align]=\"justify\">\n        {{dataAccessor(data, name)}}\n      </td>\n    </ng-container>\n  `,\n  encapsulation: ViewEncapsulation.None,\n  // Change detection is intentionally not set to OnPush. This component's template will be provided\n  // to the table to be inserted into its view. This is problematic when change detection runs since\n  // the bindings in this template will be evaluated _after_ the table's view is evaluated, which\n  // mean's the template in the table's view will not have the updated value (and in fact will cause\n  // an ExpressionChangedAfterItHasBeenCheckedError).\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  imports: [CdkColumnDef, CdkHeaderCellDef, CdkHeaderCell, CdkCellDef, CdkCell],\n})\nexport class CdkTextColumn<T> implements OnDestroy, OnInit {\n  private _table = inject<CdkTable<T>>(CdkTable, {optional: true});\n  private _options = inject<TextColumnOptions<T>>(TEXT_COLUMN_OPTIONS, {optional: true})!;\n\n  /** Column name that should be used to reference this column. */\n  @Input()\n  get name(): string {\n    return this._name;\n  }\n  set name(name: string) {\n    this._name = name;\n\n    // With Ivy, inputs can be initialized before static query results are\n    // available. In that case, we defer the synchronization until \"ngOnInit\" fires.\n    this._syncColumnDefName();\n  }\n  _name: string;\n\n  /**\n   * Text label that should be used for the column header. If this property is not\n   * set, the header text will default to the column name with its first letter capitalized.\n   */\n  @Input() headerText: string;\n\n  /**\n   * Accessor function to retrieve the data rendered for each cell. If this\n   * property is not set, the data cells will render the value found in the data's property matching\n   * the column's name. For example, if the column is named `id`, then the rendered value will be\n   * value defined by the data's `id` property.\n   */\n  @Input() dataAccessor: (data: T, name: string) => string;\n\n  /** Alignment of the cell values. */\n  @Input() justify: 'start' | 'end' | 'center' = 'start';\n\n  /** @docs-private */\n  @ViewChild(CdkColumnDef, {static: true}) columnDef: CdkColumnDef;\n\n  /**\n   * The column cell is provided to the column during `ngOnInit` with a static query.\n   * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n   * column definition was provided in the same view as the table, which is not the case with this\n   * component.\n   * @docs-private\n   */\n  @ViewChild(CdkCellDef, {static: true}) cell: CdkCellDef;\n\n  /**\n   * The column headerCell is provided to the column during `ngOnInit` with a static query.\n   * Normally, this will be retrieved by the column using `ContentChild`, but that assumes the\n   * column definition was provided in the same view as the table, which is not the case with this\n   * component.\n   * @docs-private\n   */\n  @ViewChild(CdkHeaderCellDef, {static: true}) headerCell: CdkHeaderCellDef;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    this._options = this._options || {};\n  }\n\n  ngOnInit() {\n    this._syncColumnDefName();\n\n    if (this.headerText === undefined) {\n      this.headerText = this._createDefaultHeaderText();\n    }\n\n    if (!this.dataAccessor) {\n      this.dataAccessor =\n        this._options.defaultDataAccessor || ((data: T, name: string) => (data as any)[name]);\n    }\n\n    if (this._table) {\n      // Provide the cell and headerCell directly to the table with the static `ViewChild` query,\n      // since the columnDef will not pick up its content by the time the table finishes checking\n      // its content and initializing the rows.\n      this.columnDef.cell = this.cell;\n      this.columnDef.headerCell = this.headerCell;\n      this._table.addColumnDef(this.columnDef);\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getTableTextColumnMissingParentTableError();\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._table) {\n      this._table.removeColumnDef(this.columnDef);\n    }\n  }\n\n  /**\n   * Creates a default header text. Use the options' header text transformation function if one\n   * has been provided. Otherwise simply capitalize the column name.\n   */\n  _createDefaultHeaderText() {\n    const name = this.name;\n\n    if (!name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getTableTextColumnMissingNameError();\n    }\n\n    if (this._options && this._options.defaultHeaderTextTransform) {\n      return this._options.defaultHeaderTextTransform(name);\n    }\n\n    return name[0].toUpperCase() + name.slice(1);\n  }\n\n  /** Synchronizes the column definition name with the text column name. */\n  private _syncColumnDefName() {\n    if (this.columnDef) {\n      this.columnDef.name = this.name;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {\n  HeaderRowOutlet,\n  DataRowOutlet,\n  CdkTable,\n  CdkRecycleRows,\n  FooterRowOutlet,\n  NoDataRowOutlet,\n} from './table';\nimport {\n  CdkCellOutlet,\n  CdkFooterRow,\n  CdkFooterRowDef,\n  CdkHeaderRow,\n  CdkHeaderRowDef,\n  CdkRow,\n  CdkRowDef,\n  CdkNoDataRow,\n} from './row';\nimport {\n  CdkColumnDef,\n  CdkHeaderCellDef,\n  CdkHeaderCell,\n  CdkCell,\n  CdkCellDef,\n  CdkFooterCellDef,\n  CdkFooterCell,\n} from './cell';\nimport {CdkTextColumn} from './text-column';\nimport {ScrollingModule} from '../scrolling';\n\nconst EXPORTED_DECLARATIONS = [\n  CdkTable,\n  CdkRowDef,\n  CdkCellDef,\n  CdkCellOutlet,\n  CdkHeaderCellDef,\n  CdkFooterCellDef,\n  CdkColumnDef,\n  CdkCell,\n  CdkRow,\n  CdkHeaderCell,\n  CdkFooterCell,\n  CdkHeaderRow,\n  CdkHeaderRowDef,\n  CdkFooterRow,\n  CdkFooterRowDef,\n  DataRowOutlet,\n  HeaderRowOutlet,\n  FooterRowOutlet,\n  CdkTextColumn,\n  CdkNoDataRow,\n  CdkRecycleRows,\n  NoDataRowOutlet,\n];\n\n@NgModule({\n  exports: EXPORTED_DECLARATIONS,\n  imports: [ScrollingModule, ...EXPORTED_DECLARATIONS],\n})\nexport class CdkTableModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BooleanInput, coerceBooleanProperty} from '../coercion';\n\n/** @docs-private */\nexport type Constructor<T> = new (...args: any[]) => T;\n\n/**\n * Interface for a mixin to provide a directive with a function that checks if the sticky input has\n * been changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n */\nexport interface CanStick {\n  /** Whether sticky positioning should be applied. */\n  sticky: boolean;\n\n  /** Whether the sticky value has changed since this was last called. */\n  hasStickyChanged(): boolean;\n\n  /** Resets the dirty check for cases where the sticky state has been used without checking. */\n  resetStickyChanged(): void;\n}\n\n/** @docs-private */\nexport type CanStickCtor = Constructor<CanStick>;\n\n/**\n * Mixin to provide a directive with a function that checks if the sticky input has been\n * changed since the last time the function was called. Essentially adds a dirty-check to the\n * sticky value.\n * @docs-private\n * @deprecated Implement the `CanStick` interface instead.\n * @breaking-change 19.0.0\n */\nexport function mixinHasStickyInput<T extends Constructor<{}>>(base: T): CanStickCtor & T {\n  return class extends base {\n    /** Whether sticky positioning should be applied. */\n    get sticky(): boolean {\n      return this._sticky;\n    }\n    set sticky(v: BooleanInput) {\n      const prevValue = this._sticky;\n      this._sticky = coerceBooleanProperty(v);\n      this._hasStickyChanged = prevValue !== this._sticky;\n    }\n    _sticky: boolean = false;\n\n    /** Whether the sticky input has changed since it was last checked. */\n    _hasStickyChanged: boolean = false;\n\n    /** Whether the sticky value has changed since this was last called. */\n    hasStickyChanged(): boolean {\n      const hasStickyChanged = this._hasStickyChanged;\n      this._hasStickyChanged = false;\n      return hasStickyChanged;\n    }\n\n    /** Resets the dirty check for cases where the sticky state has been used without checking. */\n    resetStickyChanged() {\n      this._hasStickyChanged = false;\n    }\n\n    constructor(...args: any[]) {\n      super(...args);\n    }\n  };\n}\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;;;;;;;;;AAUA;;;AAGG;MACU,SAAS,GAAG,IAAI,cAAc,CAAM,WAAW;AAc5D;MACa,mBAAmB,GAAG,IAAI,cAAc,CACnD,qBAAqB;;ACLvB;;;AAGG;MAIU,UAAU,CAAA;;AAErB,IAAA,QAAQ,GAAG,MAAM,CAAmB,WAAW,CAAC;AAGhD,IAAA,WAAA,GAAA;uGALW,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBAHtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACzB,iBAAA;;AASD;;;AAGG;MAIU,gBAAgB,CAAA;;AAE3B,IAAA,QAAQ,GAAG,MAAM,CAAmB,WAAW,CAAC;AAGhD,IAAA,WAAA,GAAA;uGALW,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAH5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC/B,iBAAA;;AASD;;;AAGG;MAIU,gBAAgB,CAAA;;AAE3B,IAAA,QAAQ,GAAG,MAAM,CAAmB,WAAW,CAAC;AAGhD,IAAA,WAAA,GAAA;uGALW,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAH5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oBAAoB;AAC/B,iBAAA;;AASD;;;AAGG;MAKU,YAAY,CAAA;IACvB,MAAM,GAAI,MAAM,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAErC,iBAAiB,GAAG,KAAK;;AAGjC,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,IAAY,EAAA;AACnB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;AAEhB,IAAA,KAAK;;AAGf,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAI,MAAM,CAAC,KAAc,EAAA;AACvB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE;AAC1B,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;;IAGzB,OAAO,GAAG,KAAK;AAEvB;;;;AAIG;AACH,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,KAAc,EAAA;AAC1B,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,EAAE;AAC7B,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;;IAGjC,UAAU,GAAY,KAAK;;AAGD,IAAA,IAAI;;AAGE,IAAA,UAAU;;AAGV,IAAA,UAAU;AAE1C;;;;AAIG;AACH,IAAA,oBAAoB;AAEpB;;;AAGG;AACH,IAAA,mBAAmB;AAGnB,IAAA,WAAA,GAAA;;IAGA,gBAAgB,GAAA;AACd,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;QAC/C,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,OAAO,gBAAgB;;;IAIzB,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;;AAGhC;;;;;;AAMG;IACO,yBAAyB,GAAA;QACjC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAA,WAAA,EAAc,IAAI,CAAC,oBAAoB,CAAE,CAAA,CAAC;;AAGxE;;;;;AAKG;AACO,IAAA,aAAa,CAAC,KAAa,EAAA;;;QAGnC,IAAI,KAAK,EAAE;AACT,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;YAClB,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;YAC/D,IAAI,CAAC,yBAAyB,EAAE;;;uGAzGzB,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,cAAA,EAAA,MAAA,CAAA,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAgBJ,gBAAgB,CAiBhB,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAAA,gBAAgB,gBAnCxB,CAAC,EAAC,OAAO,EAAE,4BAA4B,EAAE,WAAW,EAAE,YAAY,EAAC,CAAC,4DAgDjE,UAAU,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAGV,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAGhB,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FApDnB,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,gBAAgB;oBAC1B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,4BAA4B,EAAE,WAAW,EAAc,YAAA,EAAC,CAAC;AAChF,iBAAA;wDAQK,IAAI,EAAA,CAAA;sBADP,KAAK;uBAAC,cAAc;gBAWjB,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAkBhC,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAaV,IAAI,EAAA,CAAA;sBAA7B,YAAY;uBAAC,UAAU;gBAGQ,UAAU,EAAA,CAAA;sBAAzC,YAAY;uBAAC,gBAAgB;gBAGE,UAAU,EAAA,CAAA;sBAAzC,YAAY;uBAAC,gBAAgB;;AA0DhC;MACa,WAAW,CAAA;IACtB,WAAY,CAAA,SAAuB,EAAE,UAAsB,EAAA;AACzD,QAAA,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,mBAAmB,CAAC;;AAE3E;AAED;AAQM,MAAO,aAAc,SAAQ,WAAW,CAAA;AAG5C,IAAA,WAAA,GAAA;QACE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;;uGAJtC,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,cAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAPzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC1B,wBAAA,MAAM,EAAE,cAAc;AACvB,qBAAA;AACF,iBAAA;;AASD;AAOM,MAAO,aAAc,SAAQ,WAAW,CAAA;AAG5C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC;AACtC,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAErC,QAAA,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC;QAE5B,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,YAAY,EAAE;QAC7C,IAAI,IAAI,EAAE;YACR,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;;;uGAX5C,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBANzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC3B,qBAAA;AACF,iBAAA;;AAiBD;AAOM,MAAO,OAAQ,SAAQ,WAAW,CAAA;AAGtC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC;AACtC,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAErC,QAAA,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC;QAE5B,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,YAAY,EAAE;QAC7C,IAAI,IAAI,EAAE;YACR,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;;;uGAX5C,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAP,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAP,OAAO,EAAA,UAAA,EAAA,CAAA;kBANnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,wBAAwB;AAClC,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,UAAU;AACpB,qBAAA;AACF,iBAAA;;;ACtOD;;AAEG;MACU,SAAS,CAAA;IACpB,KAAK,GAAsB,EAAE;IAC7B,QAAQ,GAAsB,EAAE;AACjC;AAED;MACa,0BAA0B,GAAG,IAAI,cAAc,CAC1D,4BAA4B;AAG9B;;;;;;AAMG;MAEU,wBAAwB,CAAA;IAC3B,gBAAgB,GAAqB,IAAI;AACzC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAGhC,IAAA,WAAA,GAAA;AAEA;;AAEG;AACH,IAAA,QAAQ,CAAC,IAAmB,EAAA;QAC1B,IAAI,CAAC,uBAAuB,EAAE;QAE9B,IAAI,CAAC,gBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;AAGzC;;;AAGG;AACH,IAAA,WAAW,CAAC,IAAmB,EAAA;QAC7B,IAAI,CAAC,uBAAuB,EAAE;QAE9B,IAAI,CAAC,gBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;IAGpC,uBAAuB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB;;AAGF,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,SAAS,EAAE;AAEvC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;;;;;QAK7B,cAAc,CAAC,MAAK;AAClB,YAAA,OAAO,IAAI,CAAC,gBAAiB,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAiB,CAAC,QAAQ,CAAC,MAAM,EAAE;AACpF,gBAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAiB;;AAGvC,gBAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,SAAS,EAAE;AAEvC,gBAAA,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACjC,oBAAA,IAAI,EAAE;;AAGR,gBAAA,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACpC,oBAAA,IAAI,EAAE;;;AAIV,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;SAC7B,CAAC,CACH;;uGAxDQ,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAxB,wBAAwB,EAAA,CAAA;;2FAAxB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBADpC;;;ACDD;;;AAGG;AACI,MAAM,gBAAgB,GAAG;AAEhC;;;AAGG;MAEmB,UAAU,CAAA;AAC9B,IAAA,QAAQ,GAAG,MAAM,CAAmB,WAAW,CAAC;AACtC,IAAA,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;;AAG5C,IAAA,OAAO;;AAGG,IAAA,cAAc;AAGxB,IAAA,WAAA,GAAA;AAEA,IAAA,WAAW,CAAC,OAAsB,EAAA;;;AAGhC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,MAAM,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,YAAY,KAAK,EAAE;AAC7E,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE;AAC1D,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;;;AAIrC;;;AAGG;IACH,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;;;AAI/C,IAAA,mBAAmB,CAAC,MAAoB,EAAA;AACtC,QAAA,IAAI,IAAI,YAAY,eAAe,EAAE;AACnC,YAAA,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ;;AAEnC,QAAA,IAAI,IAAI,YAAY,eAAe,EAAE;AACnC,YAAA,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ;;aAC5B;AACL,YAAA,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ;;;uGAvCX,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBAD/B;;AA6CD;;;AAGG;AAKG,MAAO,eAAgB,SAAQ,UAAU,CAAA;IAC7C,MAAM,GAAI,MAAM,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAErC,iBAAiB,GAAG,KAAK;;AAGjC,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAI,MAAM,CAAC,KAAc,EAAA;AACvB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE;AAC1B,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;;IAGzB,OAAO,GAAG,KAAK;AAIvB,IAAA,WAAA,GAAA;QACE,KAAK,CAAC,MAAM,CAAmB,WAAW,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;;;;AAK9D,IAAA,WAAW,CAAC,OAAsB,EAAA;AACzC,QAAA,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;;;IAI5B,gBAAgB,GAAA;AACd,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;QAC/C,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,OAAO,gBAAgB;;;IAIzB,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;;uGAvCrB,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,oJAMyB,gBAAgB,CAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FANxD,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;oBAC7B,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,iBAAiB,EAAC,CAAC;AACtD,iBAAA;wDAQK,MAAM,EAAA,CAAA;sBADT,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,gBAAgB,EAAC;;AAqCtE;;;AAGG;AAKG,MAAO,eAAgB,SAAQ,UAAU,CAAA;IAC7C,MAAM,GAAI,MAAM,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAErC,iBAAiB,GAAG,KAAK;;AAGjC,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAI,MAAM,CAAC,KAAc,EAAA;AACvB,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE;AAC1B,YAAA,IAAI,CAAC,OAAO,GAAG,KAAK;AACpB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;;IAGzB,OAAO,GAAG,KAAK;AAIvB,IAAA,WAAA,GAAA;QACE,KAAK,CAAC,MAAM,CAAmB,WAAW,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;;;;AAK9D,IAAA,WAAW,CAAC,OAAsB,EAAA;AACzC,QAAA,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;;;IAI5B,gBAAgB,GAAA;AACd,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;QAC/C,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,OAAO,gBAAgB;;;IAIzB,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;;uGAvCrB,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,eAAe,oJAMyB,gBAAgB,CAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FANxD,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;oBAC7B,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,iBAAiB,EAAC,CAAC;AACtD,iBAAA;wDAQK,MAAM,EAAA,CAAA;sBADT,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,gBAAgB,EAAC;;AAqCtE;;;;AAIG;AAQG,MAAO,SAAa,SAAQ,UAAU,CAAA;IAC1C,MAAM,GAAI,MAAM,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE7C;;;;;AAKG;AACH,IAAA,IAAI;AAIJ,IAAA,WAAA,GAAA;;;QAGE,KAAK,CAAC,MAAM,CAAmB,WAAW,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;;uGAhB5D,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAT,SAAS,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,EAAA,IAAA,EAAA,CAAA,eAAA,EAAA,MAAA,CAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAT,SAAS,EAAA,UAAA,EAAA,CAAA;kBAPrB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACvB,oBAAA,MAAM,EAAE;AACN,wBAAA,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAC;AAC5C,wBAAA,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,EAAC;AACvC,qBAAA;AACF,iBAAA;;AA4ED;;;AAGG;MAIU,aAAa,CAAA;AACxB,IAAA,cAAc,GAAG,MAAM,CAAC,gBAAgB,CAAC;;AAGzC,IAAA,KAAK;;AAGL,IAAA,OAAO;AAEP;;;;;;AAMG;AACH,IAAA,OAAO,oBAAoB,GAAyB,IAAI;AAIxD,IAAA,WAAA,GAAA;AACE,QAAA,aAAa,CAAC,oBAAoB,GAAG,IAAI;;IAG3C,WAAW,GAAA;;;AAGT,QAAA,IAAI,aAAa,CAAC,oBAAoB,KAAK,IAAI,EAAE;AAC/C,YAAA,aAAa,CAAC,oBAAoB,GAAG,IAAI;;;uGA5BlC,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC5B,iBAAA;;AAkCD;MAca,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,kRA/CZ,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FA+Cb,YAAY,EAAA,UAAA,EAAA,CAAA;kBAbxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,gBAAgB;AACzB,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA;;;oBAGD,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,OAAO,EAAE,CAAC,aAAa,CAAC;AACzB,iBAAA;;AAGD;MAca,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,YAAY,kRA/DZ,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FA+Db,YAAY,EAAA,UAAA,EAAA,CAAA;kBAbxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,gBAAgB;AACzB,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA;;;oBAGD,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,OAAO,EAAE,CAAC,aAAa,CAAC;AACzB,iBAAA;;AAGD;MAca,MAAM,CAAA;uGAAN,MAAM,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAN,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAM,6PA/EN,aAAa,EAAA,QAAA,EAAA,iBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FA+Eb,MAAM,EAAA,UAAA,EAAA,CAAA;kBAblB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,SAAS;AAClB,wBAAA,MAAM,EAAE,KAAK;AACd,qBAAA;;;oBAGD,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,aAAa,EAAE,iBAAiB,CAAC,IAAI;oBACrC,OAAO,EAAE,CAAC,aAAa,CAAC;AACzB,iBAAA;;AAGD;MAIa,YAAY,CAAA;AACvB,IAAA,WAAW,GAAG,MAAM,CAAmB,WAAW,CAAC;IAEnD,iBAAiB,GAAG,iBAAiB;AAGrC,IAAA,WAAA,GAAA;uGANW,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2BAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAHxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2BAA2B;AACtC,iBAAA;;;ACpWD;;;AAGG;AAcH;;;AAGG;AACI,MAAM,iBAAiB,GAAsB,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO;AAErF;;;AAGG;MACU,YAAY,CAAA;AA2Bb,IAAA,kBAAA;AACA,IAAA,aAAA;AACD,IAAA,SAAA;AACC,IAAA,wBAAA;AACA,IAAA,UAAA;AACS,IAAA,6BAAA;AACA,IAAA,iBAAA;AACA,IAAA,cAAA;AAjCX,IAAA,cAAc,GAAG,IAAI,OAAO,EAAgD;IAC5E,eAAe,GAAG,UAAU,EAAE;AACpC,UAAE,IAAI,UAAU,CAAC,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;UACzE,IAAI;IACA,mCAAmC,GAAgC,EAAE;IACrE,2BAA2B,GAAyC,IAAI;IACxE,iBAAiB,GAAa,EAAE;AACvB,IAAA,cAAc;IACvB,UAAU,GAAG,KAAK;AAE1B;;;;;;;;;;;;;;AAcG;AACH,IAAA,WAAA,CACU,kBAA2B,EAC3B,aAAqB,EACtB,SAAoB,EACnB,wBAAkD,EAClD,UAAa,GAAA,IAAI,EACR,6BAAgC,GAAA,IAAI,EACpC,iBAA6C,EAC7C,cAAyB,EAAA;QAPlC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB;QAClB,IAAa,CAAA,aAAA,GAAb,aAAa;QACd,IAAS,CAAA,SAAA,GAAT,SAAS;QACR,IAAwB,CAAA,wBAAA,GAAxB,wBAAwB;QACxB,IAAU,CAAA,UAAA,GAAV,UAAU;QACD,IAA6B,CAAA,6BAAA,GAA7B,6BAA6B;QAC7B,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAc,CAAA,cAAA,GAAd,cAAc;QAE/B,IAAI,CAAC,cAAc,GAAG;YACpB,KAAK,EAAE,CAAG,EAAA,aAAa,CAAkB,gBAAA,CAAA;YACzC,QAAQ,EAAE,CAAG,EAAA,aAAa,CAAqB,mBAAA,CAAA;YAC/C,MAAM,EAAE,CAAG,EAAA,aAAa,CAAmB,iBAAA,CAAA;YAC3C,OAAO,EAAE,CAAG,EAAA,aAAa,CAAoB,kBAAA,CAAA;SAC9C;;AAGH;;;;;AAKG;IACH,sBAAsB,CAAC,IAAmB,EAAE,gBAAmC,EAAA;AAC7E,QAAA,IAAI,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC3E,YAAA,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC;;QAG/C,MAAM,eAAe,GAAkB,EAAE;AACzC,QAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;;;YAGtB,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,YAAY,EAAE;gBACrC;;AAGF,YAAA,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,GAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAmB,CAAC;;;QAI3E,IAAI,CAAC,gBAAgB,CAAC;YACpB,KAAK,EAAE,MAAK;AACV,gBAAA,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE;AACrC,oBAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,gBAAgB,CAAC;;aAErD;AACF,SAAA,CAAC;;AAGJ;;;;;;;;;;;AAWG;AACH,IAAA,mBAAmB,CACjB,IAAmB,EACnB,iBAA4B,EAC5B,eAA0B,EAC1B,qBAAqB,GAAG,IAAI,EAC5B,MAAM,GAAG,IAAI,EAAA;;QAGb,IACE,CAAC,IAAI,CAAC,MAAM;YACZ,CAAC,IAAI,CAAC,UAAU;YAChB,EAAE,iBAAiB,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,EACjF;YACA,IAAI,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,EAAC,KAAK,EAAE,EAAE,EAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,EAAE,uBAAuB,CAAC,EAAC,KAAK,EAAE,EAAE,EAAC,CAAC;YAC5D;;;AAIF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;AACxB,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM;AAEzC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,KAAK,KAAK;QACtC,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,GAAG,MAAM;QACtC,MAAM,GAAG,GAAG,KAAK,GAAG,MAAM,GAAG,OAAO;QAEpC,MAAM,eAAe,GAAG,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC;QAC3D,MAAM,cAAc,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC;AAEpD,QAAA,IAAI,UAAoB;AACxB,QAAA,IAAI,cAAwB;AAC5B,QAAA,IAAI,YAAsB;QAE1B,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,8BAA8B,CAAC;AAClC,gBAAA,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;AACf,gBAAA,iBAAiB,EAAE,CAAC,GAAG,iBAAiB,CAAC;AACzC,gBAAA,eAAe,EAAE,CAAC,GAAG,eAAe,CAAC;AACtC,aAAA,CAAC;;QAGJ,IAAI,CAAC,gBAAgB,CAAC;YACpB,SAAS,EAAE,MAAK;gBACd,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,qBAAqB,CAAC;gBAEjE,cAAc,GAAG,IAAI,CAAC,8BAA8B,CAAC,UAAU,EAAE,iBAAiB,CAAC;gBACnF,YAAY,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,eAAe,CAAC;aAC9E;YACD,KAAK,EAAE,MAAK;AACV,gBAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;AACtB,oBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;wBACjC,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAgB;AAC3C,wBAAA,IAAI,iBAAiB,CAAC,CAAC,CAAC,EAAE;AACxB,4BAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,eAAe,CAAC;;AAG7E,wBAAA,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE;AACtB,4BAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,cAAc,CAAC;;;;AAK5E,gBAAA,IAAI,IAAI,CAAC,iBAAiB,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AACvD,oBAAA,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;AAC1C,wBAAA,KAAK,EACH,eAAe,KAAK,CAAC;AACnB,8BAAE;AACF,8BAAE;AACG,iCAAA,KAAK,CAAC,CAAC,EAAE,eAAe,GAAG,CAAC;iCAC5B,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM,iBAAiB,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;AAC1E,qBAAA,CAAC;AACF,oBAAA,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC;AAC7C,wBAAA,KAAK,EACH,cAAc,KAAK,CAAC;AAClB,8BAAE;AACF,8BAAE;iCACG,KAAK,CAAC,cAAc;iCACpB,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM,eAAe,CAAC,KAAK,GAAG,cAAc,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;AAC9E,iCAAA,OAAO,EAAE;AACnB,qBAAA,CAAC;;aAEL;AACF,SAAA,CAAC;;AAGJ;;;;;;;;;;AAUG;AACH,IAAA,SAAS,CAAC,WAA0B,EAAE,YAAuB,EAAE,QAA0B,EAAA;;AAEvF,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB;;;;;AAMF,QAAA,MAAM,IAAI,GAAG,QAAQ,KAAK,QAAQ,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,WAAW;AAChF,QAAA,MAAM,MAAM,GAAG,QAAQ,KAAK,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,GAAG,YAAY;;QAGpF,MAAM,aAAa,GAAa,EAAE;QAClC,MAAM,iBAAiB,GAA2B,EAAE;QACpD,MAAM,eAAe,GAAoB,EAAE;;;QAI3C,IAAI,CAAC,gBAAgB,CAAC;YACpB,SAAS,EAAE,MAAK;AACd,gBAAA,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,YAAY,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;AAC3E,oBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;wBACrB;;AAGF,oBAAA,aAAa,CAAC,QAAQ,CAAC,GAAG,YAAY;AACtC,oBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1B,oBAAA,eAAe,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;0BAC5B,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ;AAC1B,0BAAE,CAAC,GAAG,CAAC;oBAET,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAM;oBACpD,YAAY,IAAI,MAAM;AACtB,oBAAA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM;;aAEvC;YACD,KAAK,EAAE,MAAK;gBACV,MAAM,gBAAgB,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC;AAEjD,gBAAA,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;AACzD,oBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;wBACrB;;AAGF,oBAAA,MAAM,MAAM,GAAG,aAAa,CAAC,QAAQ,CAAC;AACtC,oBAAA,MAAM,kBAAkB,GAAG,QAAQ,KAAK,gBAAgB;oBACxD,KAAK,MAAM,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;wBAC/C,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,kBAAkB,CAAC;;;AAIvE,gBAAA,IAAI,QAAQ,KAAK,KAAK,EAAE;AACtB,oBAAA,IAAI,CAAC,iBAAiB,EAAE,uBAAuB,CAAC;AAC9C,wBAAA,KAAK,EAAE,iBAAiB;AACxB,wBAAA,OAAO,EAAE,aAAa;AACtB,wBAAA,QAAQ,EAAE,eAAe;AAC1B,qBAAA,CAAC;;qBACG;AACL,oBAAA,IAAI,CAAC,iBAAiB,EAAE,uBAAuB,CAAC;AAC9C,wBAAA,KAAK,EAAE,iBAAiB;AACxB,wBAAA,OAAO,EAAE,aAAa;AACtB,wBAAA,QAAQ,EAAE,eAAe;AAC1B,qBAAA,CAAC;;aAEL;AACF,SAAA,CAAC;;AAGJ;;;;;AAKG;IACH,2BAA2B,CAAC,YAAqB,EAAE,YAAuB,EAAA;AACxE,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B;;;QAIF,IAAI,CAAC,gBAAgB,CAAC;YACpB,KAAK,EAAE,MAAK;gBACV,MAAM,KAAK,GAAG,YAAY,CAAC,aAAa,CAAC,OAAO,CAAE;gBAElD,IAAI,KAAK,EAAE;AACT,oBAAA,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE;wBACtC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC;;yBACrC;wBACL,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC;;;aAGpD;AACF,SAAA,CAAC;;;IAIJ,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACpC,YAAA,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC;;AAGhD,QAAA,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE;AAClC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;AAGxB;;;;AAIG;IACH,kBAAkB,CAAC,OAAoB,EAAE,gBAAmC,EAAA;AAC1E,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACnD;;AAGF,QAAA,KAAK,MAAM,GAAG,IAAI,gBAAgB,EAAE;AAClC,YAAA,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;AACvB,YAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;;;;;;QAOpD,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CACzC,GAAG,IAAI,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAClE;QACD,IAAI,YAAY,EAAE;YAChB,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;;aACpD;;AAEL,YAAA,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;AACzB,YAAA,IAAI,IAAI,CAAC,6BAA6B,EAAE;AACtC,gBAAA,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE;;YAE7B,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;;;AAIhD;;;;AAIG;AACH,IAAA,eAAe,CACb,OAAoB,EACpB,GAAoB,EACpB,QAAgB,EAChB,eAAwB,EAAA;QAExB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;;QAEjD,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAA,EAAG,QAAQ,CAAA,EAAA,CAAI;QACpC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;AACzD,QAAA,IAAI,IAAI,CAAC,6BAA6B,EAAE;AACtC,YAAA,OAAO,CAAC,KAAK,CAAC,OAAO,IAAI,8CAA8C;;;AAI3E;;;;;;;;;;AAUG;AACH,IAAA,oBAAoB,CAAC,OAAoB,EAAA;AACvC,QAAA,MAAM,gBAAgB,GAAG;AACvB,YAAA,GAAG,EAAE,GAAG;AACR,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,IAAI,EAAE,CAAC;AACP,YAAA,KAAK,EAAE,CAAC;SACT;QAED,IAAI,MAAM,GAAG,CAAC;;;;AAId,QAAA,KAAK,MAAM,GAAG,IAAI,iBAAkE,EAAE;AACpF,YAAA,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACtB,gBAAA,MAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC;;;QAInC,OAAO,MAAM,GAAG,CAAA,EAAG,MAAM,CAAA,CAAE,GAAG,EAAE;;;AAIlC,IAAA,cAAc,CAAC,GAAgB,EAAE,qBAAqB,GAAG,IAAI,EAAA;QAC3D,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAC3D,OAAO,IAAI,CAAC,iBAAiB;;QAG/B,MAAM,UAAU,GAAa,EAAE;AAC/B,QAAA,MAAM,aAAa,GAAG,GAAG,CAAC,QAAQ;AAClC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,YAAA,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAgB;AAC5C,YAAA,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;;AAGxD,QAAA,IAAI,CAAC,iBAAiB,GAAG,UAAU;AACnC,QAAA,OAAO,UAAU;;AAGnB;;;;AAIG;IACH,8BAA8B,CAAC,MAAgB,EAAE,YAAuB,EAAA;QACtE,MAAM,SAAS,GAAa,EAAE;QAC9B,IAAI,YAAY,GAAG,CAAC;AAEpB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;AACnB,gBAAA,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY;AAC3B,gBAAA,YAAY,IAAI,MAAM,CAAC,CAAC,CAAC;;;AAI7B,QAAA,OAAO,SAAS;;AAGlB;;;;AAIG;IACH,4BAA4B,CAAC,MAAgB,EAAE,YAAuB,EAAA;QACpE,MAAM,SAAS,GAAa,EAAE;QAC9B,IAAI,YAAY,GAAG,CAAC;AAEpB,QAAA,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;AACnB,gBAAA,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY;AAC3B,gBAAA,YAAY,IAAI,MAAM,CAAC,CAAC,CAAC;;;AAI7B,QAAA,OAAO,SAAS;;AAGlB;;;AAGG;AACK,IAAA,oBAAoB,CAAC,OAAoB,EAAA;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;QACnD,IAAI,UAAU,EAAE;AACd,YAAA,OAAO,UAAU;;AAGnB,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,qBAAqB,EAAE;AAClD,QAAA,MAAM,IAAI,GAAG,EAAC,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM,EAAC;AAEjE,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACzB,YAAA,OAAO,IAAI;;QAGb,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;AACtC,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,EAAC,GAAG,EAAE,YAAY,EAAC,CAAC;AAC1D,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACK,IAAA,8BAA8B,CAAC,MAAiC,EAAA;AACtE,QAAA,IAAI,CAAC,kCAAkC,CAAC,MAAM,CAAC,IAAI,CAAC;;AAGpD,QAAA,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;AACrC,YAAA,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,MAAM,CAAC;;;;AAKjD,IAAA,kCAAkC,CAAC,IAAmB,EAAA;AAC5D,QAAA,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC;AAC7B,QAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,mCAAmC,EAAE;YAC7D,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;QAE5D,IAAI,CAAC,mCAAmC,GAAG,IAAI,CAAC,mCAAmC,CAAC,MAAM,CACxF,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAC/B;;;AAIK,IAAA,kBAAkB,CAAC,OAA8B,EAAA;QACvD,IAAI,iBAAiB,GAAG,KAAK;AAC7B,QAAA,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;AAC3B,YAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,EAAE;AACpC,kBAAE;oBACE,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,UAAU;oBACxC,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS;AACzC;AACH,kBAAE;AACE,oBAAA,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK;AAC9B,oBAAA,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,MAAM;iBACjC;AAEL,YAAA,IACE,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,MAAqB,CAAC,EAAE,KAAK;AAC9E,gBAAA,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EACpB;gBACA,iBAAiB,GAAG,IAAI;;YAG1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,MAAqB,EAAE,QAAQ,CAAC;;QAGhE,IAAI,iBAAiB,IAAI,IAAI,CAAC,mCAAmC,CAAC,MAAM,EAAE;AACxE,YAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACpC,gBAAA,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC;;AAGhD,YAAA,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC,MAAK;AACjD,gBAAA,IAAI,IAAI,CAAC,UAAU,EAAE;oBACnB;;AAGF,gBAAA,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,mCAAmC,EAAE;AAC7D,oBAAA,IAAI,CAAC,mBAAmB,CACtB,MAAM,CAAC,IAAI,EACX,MAAM,CAAC,iBAAiB,EACxB,MAAM,CAAC,eAAe,EACtB,IAAI,EACJ,KAAK,CACN;;AAEH,gBAAA,IAAI,CAAC,mCAAmC,GAAG,EAAE;AAC7C,gBAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI;aACxC,EAAE,CAAC,CAAC;;;AAIT;;;AAGG;AACK,IAAA,gBAAgB,CAAC,IAAiD,EAAA;AACxE,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,eAAe,CAAC,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAC,CAAC;;aACjD;AACL,YAAA,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,MAAK;AAC1C,gBAAA,IAAI,CAAC,SAAS,IAAI;gBAClB,IAAI,CAAC,KAAK,EAAE;AACd,aAAC,CAAC;;;AAGP;AAED,SAAS,MAAM,CAAC,OAAgB,EAAA;IAC9B,OAAO,CAAC,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,IAClE,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAClC;AACH;;AC/jBA;;;;AAIG;AACG,SAAU,0BAA0B,CAAC,EAAU,EAAA;AACnD,IAAA,OAAO,KAAK,CAAC,CAAA,+BAAA,EAAkC,EAAE,CAAA,EAAA,CAAI,CAAC;AACxD;AAEA;;;AAGG;AACG,SAAU,gCAAgC,CAAC,IAAY,EAAA;AAC3D,IAAA,OAAO,KAAK,CAAC,CAAA,4CAAA,EAA+C,IAAI,CAAA,EAAA,CAAI,CAAC;AACvE;AAEA;;;AAGG;SACa,mCAAmC,GAAA;AACjD,IAAA,OAAO,KAAK,CAAC,CAAsE,oEAAA,CAAA,CAAC;AACtF;AAEA;;;AAGG;AACG,SAAU,kCAAkC,CAAC,IAAS,EAAA;IAC1D,OAAO,KAAK,CACV,CAAkD,gDAAA,CAAA;QAChD,CAAsB,mBAAA,EAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,CAAE,CAC/C;AACH;AAEA;;;AAGG;SACa,2BAA2B,GAAA;IACzC,OAAO,KAAK,CACV,mDAAmD;AACjD,QAAA,oDAAoD,CACvD;AACH;AAEA;;;AAGG;SACa,8BAA8B,GAAA;AAC5C,IAAA,OAAO,KAAK,CAAC,CAAwE,sEAAA,CAAA,CAAC;AACxF;AAEA;;;AAGG;SACa,yCAAyC,GAAA;AACvD,IAAA,OAAO,KAAK,CAAC,CAA6D,2DAAA,CAAA,CAAC;AAC7E;AAEA;;;AAGG;SACa,kCAAkC,GAAA;AAChD,IAAA,OAAO,KAAK,CAAC,CAAqC,mCAAA,CAAA,CAAC;AACrD;;ACnEA;MACa,2BAA2B,GAAG,IAAI,cAAc,CAA4B,SAAS;;AC2ElG;;;AAGG;MAKU,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uDAAA,EAAA,SAAA,EAFd,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC,CAAC,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAE5E,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uDAAuD;oBACjE,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC,CAAC;AACxF,iBAAA;;AAWD;;;AAGG;MAIU,aAAa,CAAA;AACxB,IAAA,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACxC,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAI/B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,KAAK,GAAG,MAAM,CAAoB,SAAS,CAAC;AAClD,QAAA,KAAK,CAAC,UAAU,GAAG,IAAI;QACvB,KAAK,CAAC,eAAe,EAAE;;uGATd,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,aAAa;AACxB,iBAAA;;AAcD;;;AAGG;MAIU,eAAe,CAAA;AAC1B,IAAA,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACxC,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAI/B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,KAAK,GAAG,MAAM,CAAoB,SAAS,CAAC;AAClD,QAAA,KAAK,CAAC,gBAAgB,GAAG,IAAI;QAC7B,KAAK,CAAC,eAAe,EAAE;;uGATd,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAH3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC9B,iBAAA;;AAcD;;;AAGG;MAIU,eAAe,CAAA;AAC1B,IAAA,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACxC,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAI/B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,KAAK,GAAG,MAAM,CAAoB,SAAS,CAAC;AAClD,QAAA,KAAK,CAAC,gBAAgB,GAAG,IAAI;QAC7B,KAAK,CAAC,eAAe,EAAE;;uGATd,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAH3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC9B,iBAAA;;AAcD;;;;AAIG;MAIU,eAAe,CAAA;AAC1B,IAAA,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACxC,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAI/B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,KAAK,GAAG,MAAM,CAAoB,SAAS,CAAC;AAClD,QAAA,KAAK,CAAC,gBAAgB,GAAG,IAAI;QAC7B,KAAK,CAAC,eAAe,EAAE;;uGATd,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAH3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,mBAAmB;AAC9B,iBAAA;;AAcD;;;;AAIG;MACU,kBAAkB;AAC7B;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEF;;;;;AAKG;MAyBU,QAAQ,CAAA;AAGA,IAAA,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;AAClC,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC9C,IAAA,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC;IAChC,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC1D,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjB,IAAA,aAAa,GAC9B,MAAM,CAAgD,uBAAuB,CAAC;AAC7D,IAAA,wBAAwB,GAAG,MAAM,CAClD,0BAA0B,CAC3B;AACgB,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;AACpC,IAAA,0BAA0B,GAAG,MAAM,CACpD,2BAA2B,EAC3B,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAChC;AAEM,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;;AAG1B,IAAA,KAAK;;AAGE,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;AAGzC,IAAA,WAAW;;AAGX,IAAA,yBAAyB;AAEjC;;;;AAIG;AACK,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAAwB;AAE3D;;;AAGG;AACK,IAAA,QAAQ;AAEhB;;;;AAIG;AACK,IAAA,cAAc;AAEtB;;;;AAIG;AACK,IAAA,cAAc;;AAGd,IAAA,WAAW;;AAGX,IAAA,cAAc;AAEtB;;;;AAIG;AACK,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAAgB;AAEnD;;;;AAIG;AACK,IAAA,cAAc,GAAG,IAAI,GAAG,EAAgB;AAEhD;;;;AAIG;AACK,IAAA,oBAAoB,GAAG,IAAI,GAAG,EAAmB;AAEzD;;;;AAIG;AACK,IAAA,oBAAoB,GAAG,IAAI,GAAG,EAAmB;;AAGjD,IAAA,gBAAgB;AAExB;;;AAGG;IACK,oBAAoB,GAAG,IAAI;AAEnC;;;AAGG;IACK,oBAAoB,GAAG,IAAI;AAEnC;;;AAGG;IACK,4BAA4B,GAAG,IAAI;AAE3C;;;;AAIG;IACK,2BAA2B,GAAG,IAAI;AAE1C;;;;;;;;;;;;AAYG;AACK,IAAA,oBAAoB,GAAG,IAAI,GAAG,EAA4C;;AAGxE,IAAA,kBAAkB;AAE5B;;;AAGG;AACK,IAAA,aAAa;AAErB;;;AAGG;IACO,cAAc,GAAW,kBAAkB;AAErD;;;;AAIG;IACO,4BAA4B,GAAG,IAAI;;AAGnC,IAAA,SAAS;;IAGX,mBAAmB,GAAG,KAAK;;IAG3B,cAAc,GAAG,KAAK;;IAGtB,eAAe,GAAG,KAAK;;IAG/B,YAAY,GAAA;;AAEV,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;;;AAGxC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC;AACrE,YAAA,OAAO,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG,MAAM;;QAG/E,OAAO,IAAI,CAAC,iBAAiB;;IAEvB,iBAAiB,GAA8B,SAAS;AAEhE;;;;;AAKG;AACH,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,OAAO,CAAC,EAAsB,EAAA;AAChC,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,EAAE,IAAI,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;AAC7F,YAAA,OAAO,CAAC,IAAI,CAAC,CAAA,yCAAA,EAA4C,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAG,CAAA,CAAA,CAAC;;AAEjF,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;;AAEd,IAAA,UAAU;AAElB;;;;;;;;;;;;;;;;;;;AAmBG;AACH,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,UAAsC,EAAA;AACnD,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;AACnC,YAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;;;AAG9B,IAAA,WAAW;AAEnB;;;;;AAKG;AACH,IAAA,IACI,qBAAqB,GAAA;QACvB,OAAO,IAAI,CAAC,sBAAsB;;IAEpC,IAAI,qBAAqB,CAAC,KAAc,EAAA;AACtC,QAAA,IAAI,CAAC,sBAAsB,GAAG,KAAK;;;AAInC,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE;YAC3D,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,wBAAwB,EAAE;;;IAGnC,sBAAsB,GAAY,KAAK;AAEvC;;;AAGG;AACH,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAE1B,IAAI,WAAW,CAAC,KAAc,EAAA;AAC5B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK;;AAGzB,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI;AACvC,QAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI;;IAElC,YAAY,GAAY,KAAK;AAErC;;;AAGG;AAEM,IAAA,cAAc,GAAG,IAAI,YAAY,EAAQ;;;AAIlD;;;;;AAKG;IACM,UAAU,GAAG,IAAI,eAAe,CAA+B;AACtE,QAAA,KAAK,EAAE,CAAC;QACR,GAAG,EAAE,MAAM,CAAC,SAAS;AACtB,KAAA,CAAC;;AAGF,IAAA,UAAU;AACV,IAAA,gBAAgB;AAChB,IAAA,gBAAgB;AAChB,IAAA,gBAAgB;AAEhB;;;AAGG;AACiD,IAAA,kBAAkB;;AAGrB,IAAA,eAAe;;AAMhE,IAAA,qBAAqB;;AAMrB,IAAA,qBAAqB;;AAGO,IAAA,UAAU;AAE9B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAIpC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAErE,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC;;QAG9D,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS;AAC1C,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,KAAK,OAAO;;;;AAK7E,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAU,EAAE,OAAqB,KAAI;YACrF,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO;AAC/E,SAAC,CAAC;;IAGJ,QAAQ,GAAA;QACN,IAAI,CAAC,kBAAkB,EAAE;AAEzB,QAAA,IAAI,CAAC;AACF,aAAA,MAAM;AACN,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI;AACzC,SAAC,CAAC;;IAGN,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI;;IAG7B,qBAAqB,GAAA;;AAEnB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,IAAI,CAAC,OAAO,EAAE;;;IAIlB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE;AAE7B,QAAA;YACE,IAAI,CAAC,UAAU,EAAE,aAAa;YAC9B,IAAI,CAAC,gBAAgB,EAAE,aAAa;YACpC,IAAI,CAAC,gBAAgB,EAAE,aAAa;AACpC,YAAA,IAAI,CAAC,oBAAoB;AACzB,YAAA,IAAI,CAAC,iBAAiB;AACtB,YAAA,IAAI,CAAC,cAAc;AACnB,YAAA,IAAI,CAAC,oBAAoB;AACzB,YAAA,IAAI,CAAC,oBAAoB;AACzB,YAAA,IAAI,CAAC,iBAAiB;AACvB,SAAA,CAAC,OAAO,CAAC,CAAC,GAAwE,KAAI;YACrF,GAAG,EAAE,KAAK,EAAE;AACd,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE;AACxB,QAAA,IAAI,CAAC,cAAc,GAAG,EAAE;AACxB,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAE1B,QAAA,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC;;;AAIpC;;;;;;;;;AASG;IACH,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE;AAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QACvD,IAAI,CAAC,OAAO,EAAE;YACZ,IAAI,CAAC,gBAAgB,EAAE;AACvB,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;YAC1B;;AAEF,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AAEnD,QAAA,IAAI,CAAC,aAAa,CAAC,YAAY,CAC7B,OAAO,EACP,aAAa,EACb,CACE,MAA0C,EAC1C,sBAAqC,EACrC,YAA2B,KACxB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,EAAE,YAAa,CAAC,EAC1D,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAC1B,CAAC,MAA4D,KAAI;AAC/D,YAAA,IAAI,MAAM,CAAC,SAAS,KAAK,sBAAsB,CAAC,QAAQ,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1E,gBAAA,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC;;AAE9E,SAAC,CACF;;QAGD,IAAI,CAAC,sBAAsB,EAAE;;;AAI7B,QAAA,OAAO,CAAC,qBAAqB,CAAC,CAAC,MAA0C,KAAI;YAC3E,MAAM,OAAO,GAAkB,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,YAAa,CAAC;YACtE,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI;AAC9C,SAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,EAAE;AAEvB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,wBAAwB,EAAE;;;AAIjC,IAAA,YAAY,CAAC,SAAuB,EAAA;AAClC,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC;;;AAIvC,IAAA,eAAe,CAAC,SAAuB,EAAA;AACrC,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC;;;AAI1C,IAAA,SAAS,CAAC,MAAoB,EAAA;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;;;AAIjC,IAAA,YAAY,CAAC,MAAoB,EAAA;AAC/B,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC;;;AAIpC,IAAA,eAAe,CAAC,YAA6B,EAAA;AAC3C,QAAA,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;;AAIlC,IAAA,kBAAkB,CAAC,YAA6B,EAAA;AAC9C,QAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC;AAC9C,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;;AAIlC,IAAA,eAAe,CAAC,YAA6B,EAAA;AAC3C,QAAA,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;;AAIlC,IAAA,kBAAkB,CAAC,YAA6B,EAAA;AAC9C,QAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,CAAC;AAC9C,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;;;AAIlC,IAAA,YAAY,CAAC,SAA8B,EAAA;AACzC,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS;;AAGnC;;;;;;AAMG;IACH,2BAA2B,GAAA;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;;;AAK/D,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,MAAM,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC;YACjE,IAAI,KAAK,EAAE;AACT,gBAAA,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,EAAE,GAAG,MAAM;;;AAIzD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,EAAE,KAAK,CAAC;;AAG7D,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,kBAAkB,EAAE,CAAC;;AAG9D;;;;;;AAMG;IACH,2BAA2B,GAAA;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;;;AAK/D,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,MAAM,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC;YACjE,IAAI,KAAK,EAAE;AACT,gBAAA,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,EAAE,GAAG,MAAM;;;AAIzD,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;QACjE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC;AAChE,QAAA,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC;;AAG5F,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,kBAAkB,EAAE,CAAC;;AAG9D;;;;;;AAMG;IACH,wBAAwB,GAAA;QACtB,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;;;;AAM/D,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,4BAA4B,EAAE;;;YAGxF,IAAI,CAAC,aAAa,CAAC,sBAAsB,CACvC,CAAC,GAAG,UAAU,EAAE,GAAG,QAAQ,EAAE,GAAG,UAAU,CAAC,EAC3C,CAAC,MAAM,EAAE,OAAO,CAAC,CAClB;AACD,YAAA,IAAI,CAAC,4BAA4B,GAAG,KAAK;;;QAI3C,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,KAAI;AAClC,YAAA,IAAI,CAAC,sBAAsB,CAAC,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAClE,SAAC,CAAC;;AAGF,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAG;;YAE7B,MAAM,IAAI,GAAkB,EAAE;AAC9B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE;oBACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;;AAI1B,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,CAAC;AAC3C,SAAC,CAAC;;QAGF,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,KAAI;AAClC,YAAA,IAAI,CAAC,sBAAsB,CAAC,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAClE,SAAC,CAAC;;QAGF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,kBAAkB,EAAE,CAAC;;;IAItF,eAAe,GAAA;;;;;;QAMb,IACE,CAAC,IAAI,CAAC,cAAc;AACpB,YAAA,IAAI,CAAC,UAAU;AACf,YAAA,IAAI,CAAC,gBAAgB;AACrB,YAAA,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,gBAAgB,EACrB;AACA,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;;AAI1B,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,IAAI,CAAC,OAAO,EAAE;;;;;IAMZ,UAAU,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe;;;IAI5C,OAAO,GAAA;;QAEb,IAAI,CAAC,aAAa,EAAE;QACpB,IAAI,CAAC,gBAAgB,EAAE;;AAGvB,QAAA,IACE,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM;AAC3B,YAAA,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM;AAC3B,YAAA,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;aACpB,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;YACA,MAAM,2BAA2B,EAAE;;;AAIrC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,EAAE;QACnD,MAAM,cAAc,GAAG,cAAc,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB;;QAE/F,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,4BAA4B,IAAI,cAAc;AACvF,QAAA,IAAI,CAAC,2BAA2B,GAAG,cAAc;;AAGjD,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;;;AAInC,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,IAAI,CAAC,oBAAoB,GAAG,KAAK;;;;AAKnC,QAAA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YAClF,IAAI,CAAC,qBAAqB,EAAE;;AACvB,aAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE;;;YAG5C,IAAI,CAAC,wBAAwB,EAAE;;QAGjC,IAAI,CAAC,kBAAkB,EAAE;;AAG3B;;;;AAIG;IACK,iBAAiB,GAAA;QACvB,MAAM,UAAU,GAAmB,EAAE;;;AAIrC,QAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB;AACtD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,GAAG,EAAE;AAErC,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACf,YAAA,OAAO,UAAU;;;;AAKnB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACxB,YAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE7F,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACxC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,OAAO,EAAE,CAAC;;AAGpD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,gBAAA,IAAI,SAAS,GAAG,iBAAiB,CAAC,CAAC,CAAC;AAEpC,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAE;gBAC5D,IAAI,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC/B,oBAAA,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAE,CAAC,IAAI,CAAC,SAAS,CAAC;;qBACvC;oBACL,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,CAAC;;AAE1C,gBAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;;;AAI9B,QAAA,OAAO,UAAU;;AAGnB;;;;AAIG;AACK,IAAA,qBAAqB,CAC3B,IAAO,EACP,SAAiB,EACjB,KAA6C,EAAA;QAE7C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC;AAEjD,QAAA,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,IAAG;YAC1B,MAAM,gBAAgB,GAAG,KAAK,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAE,GAAG,EAAE;AAC7E,YAAA,IAAI,gBAAgB,CAAC,MAAM,EAAE;AAC3B,gBAAA,MAAM,OAAO,GAAG,gBAAgB,CAAC,KAAK,EAAG;AACzC,gBAAA,OAAO,CAAC,SAAS,GAAG,SAAS;AAC7B,gBAAA,OAAO,OAAO;;iBACT;AACL,gBAAA,OAAO,EAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAC;;AAEpC,SAAC,CAAC;;;IAII,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;AAE9B,QAAA,MAAM,UAAU,GAAG,gBAAgB,CACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,EACzC,IAAI,CAAC,iBAAiB,CACvB;AACD,QAAA,UAAU,CAAC,OAAO,CAAC,SAAS,IAAG;YAC7B,IACE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;iBACzC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,gBAAA,MAAM,gCAAgC,CAAC,SAAS,CAAC,IAAI,CAAC;;YAExD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC;AACvD,SAAC,CAAC;;;IAII,aAAa,GAAA;AACnB,QAAA,IAAI,CAAC,cAAc,GAAG,gBAAgB,CACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAC5C,IAAI,CAAC,oBAAoB,CAC1B;AACD,QAAA,IAAI,CAAC,cAAc,GAAG,gBAAgB,CACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAC5C,IAAI,CAAC,oBAAoB,CAC1B;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC;;AAG7F,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QAC7D,IACE,CAAC,IAAI,CAAC,qBAAqB;YAC3B,cAAc,CAAC,MAAM,GAAG,CAAC;aACxB,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;YACA,MAAM,mCAAmC,EAAE;;AAE7C,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC;;AAGzC;;;;AAIG;IACK,qBAAqB,GAAA;AAC3B,QAAA,MAAM,kBAAkB,GAAG,CAAC,GAAY,EAAE,GAAe,KAAI;;;YAG3D,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,EAAE;YACnC,OAAO,GAAG,IAAI,IAAI;AACpB,SAAC;;AAGD,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC;QAC1E,IAAI,kBAAkB,EAAE;YACtB,IAAI,CAAC,oBAAoB,EAAE;;;AAI7B,QAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC;QAClF,IAAI,oBAAoB,EAAE;YACxB,IAAI,CAAC,sBAAsB,EAAE;;AAG/B,QAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC;QAClF,IAAI,oBAAoB,EAAE;YACxB,IAAI,CAAC,sBAAsB,EAAE;;AAG/B,QAAA,OAAO,kBAAkB,IAAI,oBAAoB,IAAI,oBAAoB;;AAG3E;;;;AAIG;AACK,IAAA,iBAAiB,CAAC,UAAsC,EAAA;AAC9D,QAAA,IAAI,CAAC,KAAK,GAAG,EAAE;AAEf,QAAA,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC;;;AAIlC,QAAA,IAAI,IAAI,CAAC,yBAAyB,EAAE;AAClC,YAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE;AAC5C,YAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI;;QAGvC,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;;AAE3B,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE;;;AAIzC,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU;;;IAIvB,qBAAqB,GAAA;;AAE3B,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB;;AAGF,QAAA,IAAI,UAAgD;AAEpD,QAAA,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACjC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;;AACrC,aAAA,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACxC,YAAA,UAAU,GAAG,IAAI,CAAC,UAAU;;aACvB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACzC,YAAA,UAAU,GAAGA,EAAY,CAAC,IAAI,CAAC,UAAU,CAAC;;AAG5C,QAAA,IAAI,UAAU,KAAK,SAAS,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC/E,MAAM,8BAA8B,EAAE;;QAGxC,IAAI,CAAC,yBAAyB,GAAG;AAC9B,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,IAAI,IAAG;AAChB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,EAAE;YACvB,IAAI,CAAC,UAAU,EAAE;AACnB,SAAC,CAAC;;AAGN;;;AAGG;IACK,sBAAsB,GAAA;;QAE5B,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAClD,YAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE;;QAG7C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACvF,IAAI,CAAC,2BAA2B,EAAE;;AAGpC;;;AAGG;IACK,sBAAsB,GAAA;;QAE5B,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAClD,YAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,KAAK,EAAE;;QAG7C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACvF,IAAI,CAAC,2BAA2B,EAAE;;;IAI5B,sBAAsB,CAAC,IAAmB,EAAE,MAAkB,EAAA;AACpE,QAAA,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,IAAG;YACpE,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC;AACxD,YAAA,IAAI,CAAC,SAAS,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACjE,gBAAA,MAAM,0BAA0B,CAAC,UAAU,CAAC;;AAE9C,YAAA,OAAO,SAAU;AACnB,SAAC,CAAC;AACF,QAAA,MAAM,iBAAiB,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC;AACvE,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC;QACxE,IAAI,CAAC,aAAa,CAAC,mBAAmB,CACpC,IAAI,EACJ,iBAAiB,EACjB,eAAe,EACf,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,2BAA2B,CACvD;;;AAIH,IAAA,gBAAgB,CAAC,SAAoB,EAAA;QACnC,MAAM,YAAY,GAAkB,EAAE;AAEtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvD,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAA0B;YACvE,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;AAGzC,QAAA,OAAO,YAAY;;AAGrB;;;;;AAKG;IACH,WAAW,CAAC,IAAO,EAAE,SAAiB,EAAA;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE;YAC7B,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;QAG3B,IAAI,OAAO,GAAmB,EAAE;AAChC,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;;aACxE;AACL,YAAA,IAAI,MAAM,GACR,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc;YACzF,IAAI,MAAM,EAAE;AACV,gBAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;;;AAIxB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACtE,YAAA,MAAM,kCAAkC,CAAC,IAAI,CAAC;;AAGhD,QAAA,OAAO,OAAO;;IAGR,oBAAoB,CAC1B,SAAuB,EACvB,KAAa,EAAA;AAEb,QAAA,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM;QAC/B,MAAM,OAAO,GAAkB,EAAC,SAAS,EAAE,SAAS,CAAC,IAAI,EAAC;QAC1D,OAAO;YACL,WAAW,EAAE,MAAM,CAAC,QAAQ;YAC5B,OAAO;YACP,KAAK;SACN;;AAGH;;;;AAIG;IACK,UAAU,CAChB,MAAiB,EACjB,MAAkB,EAClB,KAAa,EACb,UAAyB,EAAE,EAAA;;AAG3B,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;AACrF,QAAA,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,OAAO,CAAC;AAChD,QAAA,OAAO,IAAI;;IAGL,0BAA0B,CAAC,MAAkB,EAAE,OAAsB,EAAA;QAC3E,KAAK,IAAI,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;AACvD,YAAA,IAAI,aAAa,CAAC,oBAAoB,EAAE;gBACtC,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,kBAAkB,CAAC,YAAY,EAAE,OAAO,CAAC;;;AAI/F,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAGxC;;;AAGG;IACK,sBAAsB,GAAA;AAC5B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AACnD,QAAA,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,WAAW,GAAG,KAAK,EAAE,WAAW,EAAE,EAAE;YAC1F,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAkB;AAC/D,YAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAwB;AAChD,YAAA,OAAO,CAAC,KAAK,GAAG,KAAK;AACrB,YAAA,OAAO,CAAC,KAAK,GAAG,WAAW,KAAK,CAAC;YACjC,OAAO,CAAC,IAAI,GAAG,WAAW,KAAK,KAAK,GAAG,CAAC;YACxC,OAAO,CAAC,IAAI,GAAG,WAAW,GAAG,CAAC,KAAK,CAAC;AACpC,YAAA,OAAO,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI;AAE3B,YAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC9B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,SAAS;AAC3D,gBAAA,OAAO,CAAC,WAAW,GAAG,WAAW;;iBAC5B;gBACL,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,SAAS;;;;;AAMrD,IAAA,iBAAiB,CAAC,MAAkB,EAAA;QAC1C,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC9B,YAAA,OAAO,EAAE;;QAEX,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,IAAG;YAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC;AAEnD,YAAA,IAAI,CAAC,MAAM,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAC9D,gBAAA,MAAM,0BAA0B,CAAC,QAAQ,CAAC;;AAG5C,YAAA,OAAO,MAAM,CAAC,mBAAmB,CAAC,MAAO,CAAC;AAC5C,SAAC,CAAC;;AAGJ;;;;AAIG;IACK,oBAAoB,GAAA;AAC1B,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE;QACrC,IAAI,CAAC,UAAU,EAAE;;AAGnB;;;;AAIG;IACK,kBAAkB,GAAA;AACxB,QAAA,MAAM,kBAAkB,GAAG,CACzB,GAAY,EACZ,CAAmD,KACjD;AACF,YAAA,OAAO,GAAG,IAAI,CAAC,CAAC,gBAAgB,EAAE;AACpC,SAAC;;;;QAMD,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,EAAE;YACzD,IAAI,CAAC,2BAA2B,EAAE;;QAGpC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,EAAE;YACzD,IAAI,CAAC,2BAA2B,EAAE;;AAGpC,QAAA,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,EAAE;AACjF,YAAA,IAAI,CAAC,4BAA4B,GAAG,IAAI;YACxC,IAAI,CAAC,wBAAwB,EAAE;;;AAInC;;;;AAIG;IACK,kBAAkB,GAAA;AACxB,QAAA,MAAM,SAAS,GAAc,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK;AAChE,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CACnC,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,cAAc,EACnB,SAAS,EACT,IAAI,CAAC,wBAAwB,EAC7B,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,IAAI,CAAC,4BAA4B,EACjC,IAAI,CAAC,0BAA0B,EAC/B,IAAI,CAAC,SAAS,CACf;AACD,QAAA,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAGA,EAAY,EAAa;AACtD,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,KAAK;YACpC,IAAI,CAAC,wBAAwB,EAAE;AACjC,SAAC,CAAC;;;AAIE,IAAA,WAAW,CAA2B,KAAmB,EAAA;AAC/D,QAAA,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC;;;IAI3D,gBAAgB,GAAA;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU;QAE1D,IAAI,CAAC,SAAS,EAAE;YACd;;QAGF,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC;AAE7D,QAAA,IAAI,UAAU,KAAK,IAAI,CAAC,mBAAmB,EAAE;YAC3C;;AAGF,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa;QAErD,IAAI,UAAU,EAAE;YACd,MAAM,IAAI,GAAG,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC;YAChE,MAAM,QAAQ,GAA4B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;AAI3D,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;AACrF,gBAAA,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;gBACpC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,iBAAiB,CAAC;;;aAEhD;YACL,SAAS,CAAC,KAAK,EAAE;;AAGnB,QAAA,IAAI,CAAC,mBAAmB,GAAG,UAAU;AAErC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;uGAroC7B,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,EAgPA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,UAAA,EAAA,YAAA,EAAA,qBAAA,EAAA,CAAA,uBAAA,EAAA,uBAAA,EAAA,gBAAgB,CAoBhB,EAAA,WAAA,EAAA,CAAA,aAAA,EAAA,aAAA,EAAA,gBAAgB,CA7QxB,EAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,8BAAA,EAAA,aAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAC;AAC3C,YAAA,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC;AAC1E,YAAA,EAAC,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,wBAAwB,EAAC;;AAEzE,YAAA,EAAC,OAAO,EAAE,2BAA2B,EAAE,QAAQ,EAAE,IAAI,EAAC;AACvD,SAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAoUa,YAAY,EAlBT,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,SAAA,EAAA,YAAY,EAGZ,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,SAAA,EAAA,SAAS,2EAGT,eAAe,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,SAAA,EAMf,eAAe,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,kwBAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,+CAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAvdrB,eAAe,EApBf,QAAA,EAAA,mBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,aAAa,EA6Db,QAAA,EAAA,aAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,eAAe,8DArBf,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAwIf,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAxBpB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,6BAA6B,EAC7B,QAAA,EAAA,UAAU,EACV,QAAA,EAAA,kBAAkB,EAEtB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,WAAW;AACpB,wBAAA,gCAAgC,EAAE,aAAa;AAChD,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,mBAKpB,uBAAuB,CAAC,OAAO,EACrC,SAAA,EAAA;AACT,wBAAA,EAAC,OAAO,EAAE,SAAS,EAAE,WAAW,UAAU,EAAC;AAC3C,wBAAA,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,4BAA4B,EAAC;AAC1E,wBAAA,EAAC,OAAO,EAAE,0BAA0B,EAAE,QAAQ,EAAE,wBAAwB,EAAC;;AAEzE,wBAAA,EAAC,OAAO,EAAE,2BAA2B,EAAE,QAAQ,EAAE,IAAI,EAAC;qBACvD,EACQ,OAAA,EAAA,CAAC,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,CAAC,EAAA,MAAA,EAAA,CAAA,+CAAA,CAAA,EAAA;wDAkMvE,OAAO,EAAA,CAAA;sBADV;gBAiCG,UAAU,EAAA,CAAA;sBADb;gBAkBG,qBAAqB,EAAA,CAAA;sBADxB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAqBhC,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAkB3B,cAAc,EAAA,CAAA;sBADtB;gBA0BmD,kBAAkB,EAAA,CAAA;sBAArE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,YAAY,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAGD,eAAe,EAAA,CAAA;sBAA/D,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC;gBAM/C,qBAAqB,EAAA,CAAA;sBAHpB,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE;AAChC,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA;gBAOD,qBAAqB,EAAA,CAAA;sBAHpB,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE;AAChC,wBAAA,WAAW,EAAE,IAAI;AAClB,qBAAA;gBAI2B,UAAU,EAAA,CAAA;sBAArC,YAAY;uBAAC,YAAY;;AAw0B5B;AACA,SAAS,gBAAgB,CAAI,KAAU,EAAE,GAAW,EAAA;IAClD,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtC;AAEA;;;AAGG;AACH,SAAS,mBAAmB,CAAC,MAAiB,EAAE,OAAe,EAAA;AAC7D,IAAA,MAAM,gBAAgB,GAAG,OAAO,CAAC,WAAW,EAAE;IAC9C,IAAI,OAAO,GAAgB,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa;IAErE,OAAO,OAAO,EAAE;;AAEd,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,CAAC,GAAI,OAAuB,CAAC,QAAQ,GAAG,IAAI;AAClF,QAAA,IAAI,QAAQ,KAAK,gBAAgB,EAAE;AACjC,YAAA,OAAO,OAAsB;;AACxB,aAAA,IAAI,QAAQ,KAAK,OAAO,EAAE;;YAE/B;;AAEF,QAAA,OAAO,GAAG,OAAO,CAAC,UAAU;;AAG9B,IAAA,OAAO,IAAI;AACb;;ACx6CA;;;;;;;;AAQG;MAuBU,aAAa,CAAA;IAChB,MAAM,GAAG,MAAM,CAAc,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IACxD,QAAQ,GAAG,MAAM,CAAuB,mBAAmB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;;AAGvF,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,IAAY,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI;;;QAIjB,IAAI,CAAC,kBAAkB,EAAE;;AAE3B,IAAA,KAAK;AAEL;;;AAGG;AACM,IAAA,UAAU;AAEnB;;;;;AAKG;AACM,IAAA,YAAY;;IAGZ,OAAO,GAA+B,OAAO;;AAGb,IAAA,SAAS;AAElD;;;;;;AAMG;AACoC,IAAA,IAAI;AAE3C;;;;;;AAMG;AAC0C,IAAA,UAAU;AAIvD,IAAA,WAAA,GAAA;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE;;IAGrC,QAAQ,GAAA;QACN,IAAI,CAAC,kBAAkB,EAAE;AAEzB,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,wBAAwB,EAAE;;AAGnD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,IAAI,CAAC,YAAY;AACf,gBAAA,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,CAAC,IAAO,EAAE,IAAY,KAAM,IAAY,CAAC,IAAI,CAAC,CAAC;;AAGzF,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;;;;YAIf,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;YAC/B,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;YAC3C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;;AACnC,aAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACxD,MAAM,yCAAyC,EAAE;;;IAIrD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC;;;AAI/C;;;AAGG;IACH,wBAAwB,GAAA;AACtB,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;AAEtB,QAAA,IAAI,CAAC,IAAI,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC5D,MAAM,kCAAkC,EAAE;;QAG5C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE;YAC7D,OAAO,IAAI,CAAC,QAAQ,CAAC,0BAA0B,CAAC,IAAI,CAAC;;AAGvD,QAAA,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;;IAItC,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;;;uGAjHxB,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,4NAoCb,YAAY,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,MAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EASZ,UAAU,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,YAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EASV,gBAAgB,EA1EjB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA;;;;;;;;;GAST,EASS,QAAA,EAAA,IAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,YAAY,4GAAE,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAE,QAAA,EAAA,sCAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,UAAU,yDAAE,OAAO,EAAA,QAAA,EAAA,wBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEjE,aAAa,EAAA,UAAA,EAAA,CAAA;kBAtBzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE;;;;;;;;;AAST,EAAA,CAAA;oBACD,aAAa,EAAE,iBAAiB,CAAC,IAAI;;;;;;;oBAOrC,eAAe,EAAE,uBAAuB,CAAC,OAAO;oBAChD,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC;AAC9E,iBAAA;wDAOK,IAAI,EAAA,CAAA;sBADP;gBAiBQ,UAAU,EAAA,CAAA;sBAAlB;gBAQQ,YAAY,EAAA,CAAA;sBAApB;gBAGQ,OAAO,EAAA,CAAA;sBAAf;gBAGwC,SAAS,EAAA,CAAA;sBAAjD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,YAAY,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBASA,IAAI,EAAA,CAAA;sBAA1C,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,UAAU,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;gBASQ,UAAU,EAAA,CAAA;sBAAtD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC;;;ACxE7C,MAAM,qBAAqB,GAAG;IAC5B,QAAQ;IACR,SAAS;IACT,UAAU;IACV,aAAa;IACb,gBAAgB;IAChB,gBAAgB;IAChB,YAAY;IACZ,OAAO;IACP,MAAM;IACN,aAAa;IACb,aAAa;IACb,YAAY;IACZ,eAAe;IACf,YAAY;IACZ,eAAe;IACf,aAAa;IACb,eAAe;IACf,eAAe;IACf,aAAa;IACb,YAAY;IACZ,cAAc;IACd,eAAe;CAChB;MAMY,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAd,cAAc,EAAA,OAAA,EAAA,CAFf,eAAe,EA1BzB,QAAQ;YACR,SAAS;YACT,UAAU;YACV,aAAa;YACb,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,OAAO;YACP,MAAM;YACN,aAAa;YACb,aAAa;YACb,YAAY;YACZ,eAAe;YACf,YAAY;YACZ,eAAe;YACf,aAAa;YACb,eAAe;YACf,eAAe;YACf,aAAa;YACb,YAAY;YACZ,cAAc;AACd,YAAA,eAAe,aArBf,QAAQ;YACR,SAAS;YACT,UAAU;YACV,aAAa;YACb,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,OAAO;YACP,MAAM;YACN,aAAa;YACb,aAAa;YACb,YAAY;YACZ,eAAe;YACf,YAAY;YACZ,eAAe;YACf,aAAa;YACb,eAAe;YACf,eAAe;YACf,aAAa;YACb,YAAY;YACZ,cAAc;YACd,eAAe,CAAA,EAAA,CAAA;AAOJ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,YAFf,eAAe,CAAA,EAAA,CAAA;;2FAEd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,qBAAqB;AAC9B,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,GAAG,qBAAqB,CAAC;AACrD,iBAAA;;;AClCD;;;;;;;AAOG;AACG,SAAU,mBAAmB,CAA4B,IAAO,EAAA;IACpE,OAAO,cAAc,IAAI,CAAA;;AAEvB,QAAA,IAAI,MAAM,GAAA;YACR,OAAO,IAAI,CAAC,OAAO;;QAErB,IAAI,MAAM,CAAC,CAAe,EAAA;AACxB,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO;AAC9B,YAAA,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,CAAC,CAAC;YACvC,IAAI,CAAC,iBAAiB,GAAG,SAAS,KAAK,IAAI,CAAC,OAAO;;QAErD,OAAO,GAAY,KAAK;;QAGxB,iBAAiB,GAAY,KAAK;;QAGlC,gBAAgB,GAAA;AACd,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;AAC/C,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;AAC9B,YAAA,OAAO,gBAAgB;;;QAIzB,kBAAkB,GAAA;AAChB,YAAA,IAAI,CAAC,iBAAiB,GAAG,KAAK;;AAGhC,QAAA,WAAA,CAAY,GAAG,IAAW,EAAA;AACxB,YAAA,KAAK,CAAC,GAAG,IAAI,CAAC;;KAEjB;AACH;;;;"}