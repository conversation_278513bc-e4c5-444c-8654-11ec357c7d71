
import 'package:flutter/material.dart';



class AppHelperFunction{
  static Color? getColor(String value){

    if (value=='Green'){
      return Colors.green;
    } else if (value=='Green'){
      return Colors.green;
    }
    else if (value=='Pink'){
      return Colors.pink;
    }
    else if (value=='Red'){
      return Colors.red;
    }
    else if (value=='Blue'){
      return Colors.blue;
    }
    else if (value=='Grey'){
      return Colors.grey;
    }
    else if (value=='Purple'){
      return Colors.purple;
    }
    else if (value=='Black'){
      return Colors.black;
    }
    else if (value=='white'){
      return Colors.white;
    }
  }
  static double screenHeight(BuildContext context){
    return MediaQuery.of(context).size.height;
  }
  static double screenWidth(BuildContext context){
    return MediaQuery.of(context).size.width;
  }
}