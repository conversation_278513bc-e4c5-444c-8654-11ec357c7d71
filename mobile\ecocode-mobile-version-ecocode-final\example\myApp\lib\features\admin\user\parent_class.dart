class Parent {
  final int? idTuteur;
  final String? nomTuteur;
  final String? prenomTuteur;
  final String? numTelTuteur;
  final EtatTuteur? etatTuteur;
  final String? cinTuteur;

  Parent({
    this.idTuteur,
    this.nomTuteur,
    this.prenomTuteur,
    this.numTelTuteur,
    this.etatTuteur,
    this.cinTuteur,
  });

  factory Parent.fromJson(Map<String, dynamic> json) {
    return Parent(
      idTuteur: json['idTuteur'],
      nomTuteur: json['nomTuteur'],
      prenomTuteur: json['prenomTuteur'],
      numTelTuteur: json['numTelTuteur'],
      etatTuteur: _etatTuteurFromString(json['etatTuteur']),
      cinTuteur: json['cinTuteur'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'idTuteur': idTuteur,
      'nomTuteur': nomTuteur,
      'prenomTuteur': prenomTuteur,
      'numTelTuteur': numTelTuteur,
      'etatTuteur': _etatTuteurToString(etatTuteur),
      'cinTuteur': cinTuteur,
    };
  }
   static EtatTuteur? _etatTuteurFromString(String? etat) {
    switch (etat) {
      case 'Principale':
        return EtatTuteur.Principale;
      case 'Secondaire':
        return EtatTuteur.Secondaire;
      default:
        return null;
    }
  }
  static String? _etatTuteurToString(EtatTuteur? etat) {

    switch (etat) {
      case EtatTuteur.Principale:
        return 'Principale';
      case EtatTuteur.Secondaire:
        return 'Secondaire';
      default:
        return null;
    }
  }
}

enum EtatTuteur {
  Principale,
  Secondaire,
}


