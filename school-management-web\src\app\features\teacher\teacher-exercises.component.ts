import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-teacher-exercises',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-exercises">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>📝 Exercise Management</h1>
            <p>Create, assign, and track student exercises and assignments</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="showAddModal = true">
              <span class="icon">➕</span>
              Create Exercise
            </button>
            <button class="action-btn secondary" (click)="viewTemplates()">
              <span class="icon">📋</span>
              Templates
            </button>
          </div>
        </div>
      </header>

      <!-- Exercise Tabs -->
      <section class="exercise-tabs">
        <div class="tab-buttons">
          <button 
            class="tab-btn" 
            [class.active]="activeTab === tab"
            (click)="setActiveTab(tab)"
            *ngFor="let tab of tabs">
            {{tab}} ({{getExerciseCount(tab)}})
          </button>
        </div>
      </section>

      <!-- Exercises Grid -->
      <section class="exercises-section">
        <div class="exercises-grid">
          <div class="exercise-card" *ngFor="let exercise of getFilteredExercises()">
            <div class="exercise-header">
              <div class="exercise-type" [ngClass]="exercise.type">{{exercise.type}}</div>
              <div class="exercise-status" [ngClass]="exercise.status">{{exercise.status}}</div>
            </div>
            <div class="exercise-content">
              <h3>{{exercise.title}}</h3>
              <p>{{exercise.description}}</p>
              <div class="exercise-meta">
                <span class="meta-item">
                  <span class="meta-icon">📚</span>
                  {{exercise.subject}}
                </span>
                <span class="meta-item">
                  <span class="meta-icon">🏫</span>
                  {{exercise.class}}
                </span>
                <span class="meta-item">
                  <span class="meta-icon">⏱️</span>
                  {{exercise.duration}} min
                </span>
                <span class="meta-item">
                  <span class="meta-icon">📊</span>
                  {{exercise.difficulty}}
                </span>
              </div>
              <div class="exercise-stats">
                <div class="stat">
                  <span class="stat-value">{{exercise.submissions}}</span>
                  <span class="stat-label">Submissions</span>
                </div>
                <div class="stat">
                  <span class="stat-value">{{exercise.averageScore}}%</span>
                  <span class="stat-label">Avg Score</span>
                </div>
                <div class="stat">
                  <span class="stat-value">{{exercise.completionRate}}%</span>
                  <span class="stat-label">Completion</span>
                </div>
              </div>
              <div class="exercise-dates">
                <div class="date-item">
                  <span class="date-label">Created:</span>
                  <span class="date-value">{{exercise.createdDate | date:'shortDate'}}</span>
                </div>
                <div class="date-item" *ngIf="exercise.dueDate">
                  <span class="date-label">Due:</span>
                  <span class="date-value">{{exercise.dueDate | date:'shortDate'}}</span>
                </div>
              </div>
            </div>
            <div class="exercise-actions">
              <button class="exercise-btn primary" (click)="viewExercise(exercise)">
                <span class="icon">👁️</span>
                View
              </button>
              <button class="exercise-btn secondary" (click)="editExercise(exercise)">
                <span class="icon">✏️</span>
                Edit
              </button>
              <button class="exercise-btn" (click)="viewSubmissions(exercise)">
                <span class="icon">📊</span>
                Results
              </button>
              <button class="exercise-btn" (click)="duplicateExercise(exercise)">
                <span class="icon">📋</span>
                Copy
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Exercise Statistics -->
      <section class="stats-section">
        <h2>📊 Exercise Statistics</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-content">
              <div class="stat-number">{{totalExercises}}</div>
              <div class="stat-label">Total Exercises</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
              <div class="stat-number">{{activeExercises}}</div>
              <div class="stat-label">Active</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📋</div>
            <div class="stat-content">
              <div class="stat-number">{{totalSubmissions}}</div>
              <div class="stat-label">Submissions</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
              <div class="stat-number">{{averageScore}}%</div>
              <div class="stat-label">Avg Score</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Create Exercise Modal -->
      <div class="modal-overlay" *ngIf="showAddModal" (click)="showAddModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>➕ Create New Exercise</h2>
            <button class="close-btn" (click)="showAddModal = false">✕</button>
          </div>
          <form class="add-form">
            <div class="form-group">
              <label>Exercise Title</label>
              <input type="text" [(ngModel)]="newExercise.title" name="exerciseTitle" placeholder="Enter exercise title">
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Type</label>
                <select [(ngModel)]="newExercise.type" name="exerciseType">
                  <option value="">Select Type</option>
                  <option value="Quiz">Quiz</option>
                  <option value="Assignment">Assignment</option>
                  <option value="Homework">Homework</option>
                  <option value="Test">Test</option>
                  <option value="Project">Project</option>
                </select>
              </div>
              <div class="form-group">
                <label>Subject</label>
                <select [(ngModel)]="newExercise.subject" name="exerciseSubject">
                  <option value="">Select Subject</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Science">Science</option>
                  <option value="English">English</option>
                  <option value="History">History</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Class</label>
                <select [(ngModel)]="newExercise.class" name="exerciseClass">
                  <option value="">Select Class</option>
                  <option value="Grade 5A">Grade 5A</option>
                  <option value="Grade 5B">Grade 5B</option>
                  <option value="Grade 4A">Grade 4A</option>
                </select>
              </div>
              <div class="form-group">
                <label>Difficulty</label>
                <select [(ngModel)]="newExercise.difficulty" name="exerciseDifficulty">
                  <option value="">Select Difficulty</option>
                  <option value="Easy">Easy</option>
                  <option value="Medium">Medium</option>
                  <option value="Hard">Hard</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Duration (minutes)</label>
                <input type="number" [(ngModel)]="newExercise.duration" name="exerciseDuration" min="5" max="180" placeholder="30">
              </div>
              <div class="form-group">
                <label>Due Date</label>
                <input type="date" [(ngModel)]="newExercise.dueDate" name="exerciseDueDate">
              </div>
            </div>
            <div class="form-group">
              <label>Description</label>
              <textarea [(ngModel)]="newExercise.description" name="exerciseDescription" rows="4" placeholder="Enter exercise description and instructions"></textarea>
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showAddModal = false">Cancel</button>
              <button type="button" class="save-btn" (click)="createExercise()">
                <span class="icon">💾</span>
                Create Exercise
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .teacher-exercises {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .action-btn.secondary {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .exercise-tabs {
      margin-bottom: 30px;
    }

    .tab-buttons {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      padding: 0 4px;
    }

    .tab-btn {
      background: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      color: #64748b;
      transition: all 0.3s ease;
      white-space: nowrap;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .tab-btn.active {
      background: #667eea;
      color: white;
    }

    .exercises-section {
      margin-bottom: 40px;
    }

    .exercises-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
      gap: 24px;
    }

    .exercise-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }

    .exercise-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .exercise-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .exercise-type {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .exercise-type.Quiz { background: #e3f2fd; color: #1976d2; }
    .exercise-type.Assignment { background: #f3e5f5; color: #7b1fa2; }
    .exercise-type.Homework { background: #e8f5e8; color: #388e3c; }
    .exercise-type.Test { background: #fff3e0; color: #f57c00; }
    .exercise-type.Project { background: #fce4ec; color: #c2185b; }

    .exercise-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .exercise-status.active { background: #d4edda; color: #155724; }
    .exercise-status.draft { background: #fff3cd; color: #856404; }
    .exercise-status.completed { background: #cce5ff; color: #004085; }
    .exercise-status.archived { background: #f8d7da; color: #721c24; }

    .exercise-content h3 {
      margin: 0 0 8px 0;
      color: #2d3748;
      font-size: 18px;
    }

    .exercise-content p {
      margin: 0 0 16px 0;
      color: #718096;
      font-size: 14px;
      line-height: 1.5;
    }

    .exercise-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-bottom: 16px;
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #718096;
    }

    .meta-icon {
      font-size: 14px;
    }

    .exercise-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 12px;
      background: #f8fafc;
      border-radius: 8px;
    }

    .stat {
      text-align: center;
    }

    .stat-value {
      display: block;
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 2px;
    }

    .stat-label {
      font-size: 11px;
      color: #718096;
    }

    .exercise-dates {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      font-size: 12px;
    }

    .date-label {
      color: #718096;
      margin-right: 4px;
    }

    .date-value {
      color: #2d3748;
      font-weight: 500;
    }

    .exercise-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
    }

    .exercise-btn {
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      transition: all 0.3s ease;
    }

    .exercise-btn.primary {
      background: #667eea;
      color: white;
      border: none;
    }

    .exercise-btn.secondary {
      background: white;
      color: #667eea;
      border: 1px solid #667eea;
    }

    .exercise-btn:not(.primary):not(.secondary) {
      background: #f8fafc;
      color: #64748b;
      border: 1px solid #e2e8f0;
    }

    .exercise-btn:hover {
      transform: translateY(-1px);
    }

    .stats-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 40px;
    }

    .stats-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }

    .stat-card {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #718096;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 700px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .modal-header h2 {
      margin: 0;
      color: #2d3748;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #718096;
    }

    .add-form {
      padding: 24px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #2d3748;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .form-group textarea {
      resize: vertical;
      min-height: 100px;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }

    .cancel-btn {
      background: #6c757d;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
    }

    .save-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .exercises-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .exercise-actions {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class TeacherExercisesComponent {
  activeTab = 'All';
  showAddModal = false;
  showViewModal = false;
  showEditModal = false;
  showSubmissionsModal = false;
  showGradingModal = false;
  selectedExercise: any = null;
  editingExercise: any = null;
  gradingSubmission: any = null;
  gradingExercise: any = null;
  newGrade: number | null = null;
  gradeFeedback = '';

  tabs = ['All', 'Active', 'Draft', 'Completed', 'Archived'];

  newExercise = {
    title: '',
    type: '',
    subject: '',
    class: '',
    difficulty: '',
    duration: 30,
    dueDate: '',
    description: '',
    totalPoints: 100,
    instructions: '',
    allowLateSubmissions: true,
    showCorrectAnswers: false,
    randomizeQuestions: false
  };

  exercises = [
    {
      id: 1,
      title: 'Quadratic Equations Quiz',
      description: 'Test understanding of quadratic equations and their solutions.',
      type: 'Quiz',
      subject: 'Mathematics',
      class: 'Grade 5A',
      difficulty: 'Medium',
      duration: 45,
      status: 'active',
      submissionCount: 25,
      averageScore: 78,
      completionRate: 89,
      createdDate: new Date('2024-01-15'),
      dueDate: new Date('2024-01-25'),
      totalPoints: 100,
      questions: [
        { id: 1, question: 'Solve: x² + 5x + 6 = 0', type: 'multiple-choice', points: 10, options: ['x = -2, -3', 'x = 2, 3', 'x = -1, -6', 'x = 1, 6'], correctAnswer: 0 },
        { id: 2, question: 'What is the discriminant of x² - 4x + 4 = 0?', type: 'short-answer', points: 10, correctAnswer: '0' },
        { id: 3, question: 'Graph the parabola y = x² - 2x + 1', type: 'essay', points: 20 }
      ],
      submissions: [
        { studentId: 'S001', studentName: 'John Smith', score: 85, submittedDate: new Date('2024-01-20'), answers: [0, '0', 'The parabola opens upward with vertex at (1,0)'] },
        { studentId: 'S002', studentName: 'Emma Johnson', score: 95, submittedDate: new Date('2024-01-19'), answers: [0, '0', 'Detailed graph analysis with correct vertex and axis of symmetry'] }
      ],
      rubric: {
        excellent: '90-100: Demonstrates complete understanding',
        good: '80-89: Shows good grasp of concepts',
        satisfactory: '70-79: Basic understanding evident',
        needsImprovement: '0-69: Requires additional support'
      }
    },
    {
      id: 2,
      title: 'Science Lab Report',
      description: 'Document findings from the chemistry experiment on acid-base reactions.',
      type: 'Assignment',
      subject: 'Science',
      class: 'Grade 5B',
      difficulty: 'Hard',
      duration: 120,
      status: 'active',
      submissionCount: 18,
      averageScore: 85,
      completionRate: 72,
      createdDate: new Date('2024-01-10'),
      dueDate: new Date('2024-01-30'),
      totalPoints: 100,
      requirements: [
        'Hypothesis statement (10 points)',
        'Materials list (5 points)',
        'Procedure description (15 points)',
        'Data collection and analysis (40 points)',
        'Conclusion and reflection (20 points)',
        'Proper formatting and grammar (10 points)'
      ],
      submissions: [
        { studentId: 'S004', studentName: 'Sarah Davis', score: 88, submittedDate: new Date('2024-01-25'), status: 'graded' },
        { studentId: 'S005', studentName: 'Alex Wilson', score: 82, submittedDate: new Date('2024-01-28'), status: 'graded' }
      ]
    },
    {
      id: 3,
      title: 'Reading Comprehension: The Secret Garden',
      description: 'Analyze the given text and answer comprehension questions.',
      type: 'Homework',
      subject: 'English',
      class: 'Grade 4A',
      difficulty: 'Easy',
      duration: 30,
      status: 'completed',
      submissionCount: 24,
      averageScore: 92,
      completionRate: 100,
      createdDate: new Date('2024-01-05'),
      dueDate: new Date('2024-01-12'),
      totalPoints: 50,
      questions: [
        { id: 1, question: 'Who is the main character?', type: 'short-answer', points: 5 },
        { id: 2, question: 'Describe the setting of the story', type: 'essay', points: 15 },
        { id: 3, question: 'What is the theme of the story?', type: 'essay', points: 20 },
        { id: 4, question: 'List three character traits of Mary', type: 'list', points: 10 }
      ],
      feedback: 'Excellent work on character analysis. Most students showed good understanding of themes.'
    },
    {
      id: 4,
      title: 'Geometry Practice: Angles and Shapes',
      description: 'Practice problems on angles, triangles, and quadrilaterals.',
      type: 'Test',
      subject: 'Mathematics',
      class: 'Grade 5A',
      difficulty: 'Medium',
      duration: 60,
      status: 'draft',
      submissionCount: 0,
      averageScore: 0,
      completionRate: 0,
      createdDate: new Date('2024-01-20'),
      dueDate: null,
      totalPoints: 80,
      questions: [
        { id: 1, question: 'Calculate the area of a triangle with base 8cm and height 6cm', type: 'calculation', points: 10 },
        { id: 2, question: 'What is the sum of interior angles in a pentagon?', type: 'multiple-choice', points: 10, options: ['360°', '540°', '720°', '900°'], correctAnswer: 1 },
        { id: 3, question: 'Draw and label an isosceles triangle', type: 'drawing', points: 15 }
      ]
    }
  ];

  totalExercises = this.exercises.length;
  activeExercises = this.exercises.filter(e => e.status === 'active').length;
  totalSubmissions = this.exercises.reduce((sum, ex) => sum + (ex.submissionCount || 0), 0);
  averageScore = Math.round(this.exercises.reduce((sum, ex) => sum + ex.averageScore, 0) / this.exercises.length);

  constructor(private router: Router) {}

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  getExerciseCount(tab: string): number {
    if (tab === 'All') return this.exercises.length;
    return this.exercises.filter(e => e.status.toLowerCase() === tab.toLowerCase()).length;
  }

  getFilteredExercises() {
    if (this.activeTab === 'All') return this.exercises;
    return this.exercises.filter(e => e.status.toLowerCase() === this.activeTab.toLowerCase());
  }

  viewExercise(exercise: any): void {
    this.selectedExercise = exercise;
    this.showViewModal = true;
  }

  editExercise(exercise: any): void {
    this.editingExercise = { ...exercise };
    this.showEditModal = true;
  }

  saveExerciseEdit(): void {
    if (this.editingExercise) {
      const index = this.exercises.findIndex(e => e.id === this.editingExercise.id);
      if (index !== -1) {
        this.exercises[index] = { ...this.editingExercise };
        this.showEditModal = false;
        this.editingExercise = null;
      }
    }
  }

  viewSubmissions(exercise: any): void {
    this.selectedExercise = exercise;
    this.showSubmissionsModal = true;
  }

  gradeSubmission(submission: any, exercise: any): void {
    this.gradingSubmission = submission;
    this.gradingExercise = exercise;
    this.showGradingModal = true;
  }

  saveGrade(): void {
    if (this.gradingSubmission && this.newGrade !== null) {
      this.gradingSubmission.score = this.newGrade;
      this.gradingSubmission.status = 'graded';
      this.gradingSubmission.feedback = this.gradeFeedback;
      this.gradingSubmission.gradedDate = new Date();

      // Update exercise statistics
      if (this.gradingExercise) {
        const submissions = this.gradingExercise.submissions || [];
        const totalScore = submissions.reduce((sum: number, sub: any) => sum + (sub.score || 0), 0);
        this.gradingExercise.averageScore = Math.round(totalScore / submissions.length);
      }

      this.showGradingModal = false;
      this.resetGrading();
    }
  }

  resetGrading(): void {
    this.gradingSubmission = null;
    this.gradingExercise = null;
    this.newGrade = null;
    this.gradeFeedback = '';
  }

  duplicateExercise(exercise: any): void {
    const duplicate = {
      ...exercise,
      id: this.exercises.length + 1,
      title: exercise.title + ' (Copy)',
      status: 'draft',
      submissionCount: 0,
      averageScore: 0,
      completionRate: 0,
      createdDate: new Date(),
      dueDate: null,
      questions: exercise.questions ? [...exercise.questions] : [],
      requirements: exercise.requirements ? [...exercise.requirements] : []
    };
    this.exercises.push(duplicate);
  }

  publishExercise(exercise: any): void {
    if (confirm(`Are you sure you want to publish "${exercise.title}"? Students will be able to see and submit this exercise.`)) {
      exercise.status = 'active';
      exercise.publishedDate = new Date();
    }
  }

  archiveExercise(exercise: any): void {
    if (confirm(`Are you sure you want to archive "${exercise.title}"? This will hide it from students.`)) {
      exercise.status = 'archived';
      exercise.archivedDate = new Date();
    }
  }

  deleteExercise(exercise: any): void {
    if (confirm(`Are you sure you want to delete "${exercise.title}"? This action cannot be undone.`)) {
      this.exercises = this.exercises.filter(e => e.id !== exercise.id);
      this.updateStatistics();
    }
  }

  updateStatistics(): void {
    this.totalExercises = this.exercises.length;
    this.activeExercises = this.exercises.filter(e => e.status === 'active').length;
    this.totalSubmissions = this.exercises.reduce((sum, ex) => sum + (ex.submissionCount || 0), 0);
    this.averageScore = Math.round(this.exercises.reduce((sum, ex) => sum + ex.averageScore, 0) / this.exercises.length);
  }

  exportExerciseData(exercise: any): void {
    const csvData = this.generateExerciseCSV(exercise);
    this.downloadCSV(csvData, `${exercise.title}_submissions.csv`);
  }

  generateExerciseCSV(exercise: any): string {
    const headers = ['Student Name', 'Student ID', 'Score', 'Submitted Date', 'Status'];
    const rows = (exercise.submissions || []).map((submission: any) => [
      submission.studentName,
      submission.studentId,
      submission.score || 'Not graded',
      submission.submittedDate ? submission.submittedDate.toLocaleDateString() : 'Not submitted',
      submission.status || 'Pending'
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map((field: any) => `"${field}"`).join(','))
      .join('\n');

    return csvContent;
  }

  downloadCSV(csvContent: string, filename: string): void {
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  createExercise(): void {
    if (this.newExercise.title && this.newExercise.type && this.newExercise.subject) {
      const newExercise = {
        id: this.exercises.length + 1,
        ...this.newExercise,
        status: 'draft',
        submissionCount: 0,
        averageScore: 0,
        completionRate: 0,
        createdDate: new Date(),
        dueDate: this.newExercise.dueDate ? new Date(this.newExercise.dueDate) : null,
        questions: [],
        submissions: [],
        rubric: {
          excellent: '90-100: Demonstrates complete understanding',
          good: '80-89: Shows good grasp of concepts',
          satisfactory: '70-79: Basic understanding evident',
          needsImprovement: '0-69: Requires additional support'
        }
      };
      this.exercises.push(newExercise);
      this.showAddModal = false;
      this.resetNewExercise();
    }
  }

  resetNewExercise(): void {
    this.newExercise = {
      title: '',
      type: '',
      subject: '',
      class: '',
      difficulty: '',
      duration: 30,
      dueDate: '',
      description: '',
      totalPoints: 100,
      instructions: '',
      allowLateSubmissions: true,
      showCorrectAnswers: false,
      randomizeQuestions: false
    };
  }

  viewTemplates(): void {
    console.log('View exercise templates');
  }
}
