// localhost
// const String baseURL = 'http://localhost:8080/api';
// const String ipAddress = 'localhost';
// const String keycloakUri = 'http://localhost:9001/realms/Prestacode';
// const String tokenUrl =
//     'http://localhost:9001/realms/Prestacode/protocol/openid-connect/token';
// const String logoutUrl =
//     'http://localhost:9001/realms/Prestacode/protocol/openid-connect/logout';
// const String introspectUrl =
//     'http://localhost:9001/realms/Prestacode/protocol/openid-connect/token/introspect';
// const String forgetPasswordURL = 'http://localhost:4201';
// const String clientId = 'ishrakschool-back';
// const String clientSecret = 'O8Q4vxIHXzr5yltXCdavk9n7SiIIh5p8';
// const String facebookUrl = 'https://www.facebook.com/NovaSchool';
// const String nameFacebook = 'NovaSchool';
// const String instagramUrl = 'https://www.instagram.com/ichrak.NovaSchool/';
// const String nameInstagram = 'ichrak.NovaSchool';
// const String adresse1 = 'طريق القائد محمد كلم 2,5';
// const String adresse2 = 'طريق تنيور كلم 3';
// const String tel1 = '31440430';
// const String tel2 = '51274305';
// const String tel3 = '55845245';
// const String tel4 = '23060006';
// const String mail = '<EMAIL>';
// const String youtubeUrl = 'https://www.youtube.com';
// const String schoolLogo = "assets/logos/amana.png";
// const String dashURL = 'http://ichrak.ecocode.ovh/profile-eleve-parent';
// final String backendLoginUrl = "http://localhost:8080/api/users/login";
// final String backendLogoutUrl = "http://localhost:8080/api/users/logout";
// final bool confirmed = false;
// final bool confirmedParent = true;
// const List<String> niveaux = [
//   'Préparatoire',
//   '1ère-Année',
//   '2ème-Année',
//   '3ème-Année',
//   '4ème-Année',
//   '5ème-Année',
//   '6ème-Année',
// ];

/// Test
// const String baseURL = 'http://**************:2002/api';
// const String keycloakUri = 'http://**************:9003/realms/Eco-Code';
// const String tokenUrl =
//     'http://**************:9003/realms/Eco-Code/protocol/openid-connect/token';
// const String logoutUrl =
//     'http://**************:9003/realms/Eco-Code/protocol/openid-connect/logout';
// const String introspectUrl =
//     'http://**************:9003/realms/Eco-Code/protocol/openid-connect/token/introspect';
// const String clientId = 'ishrakschool-back';
// const String clientSecret = 'cttRhoDIkNiFzqJc9jh0w3OsZGKswQ6t';
// const String ipAddress = '**************';
// const String forgetPasswordURL = 'http://**************:2001';
// const String facebookUrl = 'https://www.facebook.com/NovaSchool';
// const String nameFacebook = 'NovaSchool';
// const String instagramUrl = 'https://www.instagram.com/ichrak.NovaSchool/';
// const String nameInstagram = 'ichrak.NovaSchool';
// const String adresse1 = 'طريق القائد محمد كلم 2,5';
// const String adresse2 = 'طريق تنيور كلم 3';
// const String tel1 = '31440430';
// const String tel2 = '51274305';
// const String tel3 = '55845245';
// const String tel4 = '23060006';
// const String mail = '<EMAIL>';
// const String youtubeUrl = 'http://**************:2001';
// const String schoolLogo ="assets/logos/demo.png";
// const String dashURL = 'http://**************:2001/profile-eleve-parent';
// final String backendLoginUrl = "http://**************:2002/api/users/login";
// final String backendLogoutUrl = "http://**************:2002/api/users/logout";
// final bool confirmed = false;
// final bool confirmedParent = true;
// const List<String> niveaux = [
//   'Préparatoire',
//   '1ère-Année',
//   '2ème-Année',
//   '3ème-Année',
//   '4ème-Année',
//   '5ème-Année',
//   '6ème-Année',
// ];

/// Demo
// const String baseURL = 'https://demo.ecocode.ovh/api';
// const String keycloakUri = 'http://**************:9004/realms/Eco-Code';
// const String tokenUrl =
//     'http://**************:9004/realms/Eco-Code/protocol/openid-connect/token';
// const String logoutUrl =
//     'http://**************:9004/realms/Eco-Code/protocol/openid-connect/logout';
// const String introspectUrl =
//     'https://demo.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/token/introspect';
// const String forgetPasswordURL = 'https://demo.ecocode.ovh';
// const String clientId = 'ishrakschool-back';
// const String clientSecret = 'H5aeWUy55G8POK2Lxhb0wQ1lLACOblej';
// const String ipAddress = '**************';
// const String facebookUrl = 'https://www.facebook.com/people/Luminous-Horizon/61561398011455/';
// const String nameFacebook = 'Eco-code';
// const String instagramUrl = 'https://www.facebook.com/people/Luminous-Horizon/61561398011455/';
// const String nameInstagram = 'Eco.Code';
// const String adresse1 = 'Immeuble Fourat, Sfax, Tunisia';
// const String adresse2 = '';
// const String tel1 = '20000000';
// const String tel2 = '50000000';
// const String tel3 = '44000000';
// const String tel4 = '90000000';
// const String mail = '<EMAIL>';
// const String youtubeUrl = 'https://www.youtube.com/channel/UCGy0KpZu_8IetwVpDMP_zQw';
// const String schoolLogo ="assets/logos/demo.png";
// const String dashURL = 'https://demo.ecocode.ovh/profile-eleve-parent';
// final String backendLoginUrl = "https://demo.ecocode.ovh/api/users/login";
// final String backendLogoutUrl = "https://demo.ecocode.ovh/api/users/logout";
// final bool confirmed = true;
// final bool confirmedParent = true; 
// const List<String> niveaux = [
//   'Préparatoire',
//   '1ère-Année',
//   '2ème-Année',
//   '3ème-Année',
//   '4ème-Année',
//   '5ème-Année',
//   '6ème-Année',
// ];

/// ichrak
// const String baseURL = 'http://************:2023/api';
// const String keycloakUri = 'http://************:9003/realms/Eco-Code';
// const String tokenUrl =
//     'http://************:9003/realms/Eco-Code/protocol/openid-connect/token';
// const String logoutUrl =
//     'http://************:9003/realms/Eco-Code/protocol/openid-connect/logout';
// const String introspectUrl =
//     'http://************:9003/realms/Eco-Code/protocol/openid-connect/token/introspect';
// const String forgetPasswordURL = 'http://ichrak.ecocode.ovh';
// const String clientId = 'ishrakschool-back';
// const String clientSecret = 'cttRhoDIkNiFzqJc9jh0w3OsZGKswQ6t';
// const String ipAddress = '************';
// const String facebookUrl = 'https://www.facebook.com/NovaSchool';
// const String nameFacebook = 'NovaSchool';
// const String instagramUrl = 'https://www.instagram.com/ichrak.NovaSchool/';
// const String nameInstagram = 'ichrak.NovaSchool';
// const String adresse1 = 'طريق القائد محمد كلم 2,5';
// const String adresse2 = 'طريق تنيور كلم 3';
// const String tel1 = '31440430';
// const String tel2 = '51274305';
// const String tel3 = '55845245';
// const String tel4 = '23060006';
// const String mail = '<EMAIL>';
// const String youtubeUrl = '';
// const String schoolLogo ="assets/logos/ichrak.png";
// const String name = "ichrak";
// const String dashURL = 'http://ichrak.ecocode.ovh/profile-eleve-parent';
// final String backendLoginUrl = "http://************:2023/api/users/login";
// final String backendLogoutUrl = "http://************:2023/api/users/logout";
// final bool confirmed = true;
// final bool confirmedParent = true;
// const List<String> niveaux = [
//   'Préparatoire',
//   '1ère-Année',
//   '2ème-Année',
//   '3ème-Année',
//   '4ème-Année',
//   '5ème-Année',
//   '6ème-Année',
// ];

/// Amana
// const String baseURL = 'https://amana.ecocode.ovh/api';
// const String keycloakUri = 'https://auth.amana.ecocode.ovh/realms/Eco-Code';
// const String tokenUrl =
//     'https://auth.amana.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/token';
// const String logoutUrl =
//     'https://auth.amana.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/logout';
// const String introspectUrl =
//     'https://auth.amana.ecocode.ovh/realms/Eco-Code/protocol/openid-connect/token/introspect';
// const String forgetPasswordURL = 'https://amana.ecocode.ovh';
// const String clientId = 'ishrakschool-back';
// const String clientSecret = 'cttRhoDIkNiFzqJc9jh0w3OsZGKswQ6t';
// const String ipAddress = '*************';
// const String facebookUrl = 'https://www.facebook.com/p/AMANA-School-100057215239972/';
// const String nameFacebook = 'AMANA-School';
// const String instagramUrl = '';
// const String nameInstagram = '';
// const String adresse1 = 'Derriere les stades Arena Gabès, Gabès, Tunisia';
// const String adresse2 = '';
// const String tel1 = '75290400';
// const String tel2 = '';
// const String tel3 = '';
// const String tel4 = '';
// const String mail = '<EMAIL>';
// const String youtubeUrl = '';
// const String schoolLogo ="assets/logos/amana.png";
// const String dashURL = 'http://amana.ecocode.ovh/profile-eleve-parent';
// final String backendLoginUrl = "https://amana.ecocode.ovh/api/users/login";
// final String backendLogoutUrl = "https://amana.ecocode.ovh/api/users/logout";
// final bool confirmed = false;
// final bool confirmedParent = true;
// const List<String> niveaux = [
//   'Préparatoire',
//   '1ère-Année',
//   '2ème-Année',
//   '3ème-Année',
//   '4ème-Année',
//   '5ème-Année',
//   '6ème-Année',
// ];

/// Horizon
// const String baseURL = 'http://**************:2023/api';
// const String keycloakUri = 'http://**************:9003/realms/Eco-Code';
// const String tokenUrl =
//     'http://**************:9003/realms/Eco-Code/protocol/openid-connect/token';
// const String logoutUrl =
//     'http://**************:9003/realms/Eco-Code/protocol/openid-connect/logout';
// const String introspectUrl =
//     'http://**************:9003/realms/Eco-Code/protocol/openid-connect/token/introspect';
// const String forgetPasswordURL = 'http://horizon.ecocode.ovh';
// const String clientId = 'ishrakschool-back';
// const String clientSecret = 'H5aeWUy55G8POK2Lxhb0wQ1lLACOblej';
// const String ipAddress = '**************';
// const String facebookUrl = 'https://www.facebook.com/people/Luminous-Horizon/61561398011455/';
// const String nameFacebook = 'Luminous-Horizon';
// const String instagramUrl = '';
// const String nameInstagram = '';
// const String adresse1 = 'Route Tbolbi km4 près de la mosquée Alhosna, Sfax, Tunisia';
// const String adresse2 = '';
// const String tel1 = '95650766';
// const String tel2 = '95606370'; 
// const String tel3 = '';
// const String tel4 = '';
// const String mail = '<EMAIL>';
// const String youtubeUrl = '';
// const String schoolLogo ="assets/logos/horizon.png";
// const String dashURL = 'http://horizon.ecocode.ovh/profile-eleve-parent';
// final String backendLoginUrl = "http://**************:2023/api/users/login";
// final String backendLogoutUrl = "http://**************:2023/api/users/logout";
// final bool confirmed = true;
// final bool confirmedParent = true;
// const List<String> niveaux = [
//   '7ème-Année',
//   '8ème-Année',
//   '9ème-Année',
// ];

/// Loujayn
// const String baseURL = 'http://*************:2023/api';
// const String keycloakUri = 'http://*************:9003/realms/Eco-Code';
// const String tokenUrl =
//     'http://*************:9003/realms/Eco-Code/protocol/openid-connect/token';
// const String logoutUrl =
//     'http://*************:9003/realms/Eco-Code/protocol/openid-connect/logout';
// const String introspectUrl =
//     'http://*************:9003/realms/Eco-Code/protocol/openid-connect/token/introspect';
// const String forgetPasswordURL = 'http://loujayn.ecocode.ovh';
// const String clientId = 'ishrakschool-back';
// const String clientSecret = 'H5aeWUy55G8POK2Lxhb0wQ1lLACOblej';
// const String ipAddress = '*************';
// const String facebookUrl = 'https://www.facebook.com/people/Loujayn-School/100084515956585/';
// const String nameFacebook = 'Loujayn-School';
// const String instagramUrl = '';
// const String nameInstagram = '';
// const String adresse1 = 'Ghannush, Tunisia';
// const String adresse2 = '';
// const String tel1 = '51800322';
// const String tel2 = '';
// const String tel3 = '';
// const String tel4 = '';
// const String mail = '<EMAIL>';
// const String youtubeUrl = 'https://www.youtube.com/channel/UCGy0KpZu_8IetwVpDMP_zQw';
// const String schoolLogo ="assets/logos/loujayn.png";
// const String dashURL = 'http://loujayn.ecocode.ovh/profile-eleve-parent';
// final String backendLoginUrl = "http://*************:2023/api/users/login";
// final String backendLogoutUrl = "http://*************:2023/api/users/logout";
// final bool confirmed = true;
// final bool confirmedParent = true;
// const List<String> niveaux = [
//   'Préparatoire',
//   '1ère-Année',
//   '2ème-Année',
//   '3ème-Année',
//   '4ème-Année',
//   '5ème-Année',
//   '6ème-Année',
// ];

/// Nova
// const String baseURL = 'http://***************:2023/api';
// const String keycloakUri = 'http://***************:9003/realms/Eco-Code';
// const String tokenUrl =
//     'http://***************:9003/realms/Eco-Code/protocol/openid-connect/token';
// const String logoutUrl =
//     'http://***************:9003/realms/Eco-Code/protocol/openid-connect/logout';
// const String introspectUrl =
//     'http://***************:9003/realms/Eco-Code/protocol/openid-connect/token/introspect';
// const String forgetPasswordURL = 'http://nova.ecocode.ovh';
// const String clientId = 'ishrakschool-back';
// const String clientSecret = 'H5aeWUy55G8POK2Lxhb0wQ1lLACOblej';
// const String ipAddress = '***************';
// const String facebookUrl = 'https://www.facebook.com/Ecole.NovaSchool/';
// const String nameFacebook = 'Ecole.NovaSchool';
// const String instagramUrl = 'https://www.instagram.com/explore/locations/1014724412/nova-school/';
// const String nameInstagram = 'nova-school';
// const String adresse1 = 'Route Gremda Km 5,5 , Sfax, Tunisia';
// const String adresse2 = '';
// const String tel1 = '21595852';
// const String tel2 = '';
// const String tel3 = '';
// const String tel4 = '';
// const String mail = '';
// const String youtubeUrl = '';
// const String schoolLogo ="assets/logos/nova.png";
// const String dashURL = 'http://nova.ecocode.ovh/profile-eleve-parent';
// final String backendLoginUrl = "http://***************:2023/api/users/login";
// final String backendLogoutUrl = "http://***************:2023/api/users/logout";
// final bool confirmed = false;
// final bool confirmedParent = true;
// const List<String> niveaux = [
//   'Préparatoire',
//   '1ère-Année',
//   '2ème-Année',
//   '3ème-Année',
//   '4ème-Année',
//   '5ème-Année',
//   '6ème-Année',
// ];

// dont modify this api ichrak
const String grantType = 'password';
