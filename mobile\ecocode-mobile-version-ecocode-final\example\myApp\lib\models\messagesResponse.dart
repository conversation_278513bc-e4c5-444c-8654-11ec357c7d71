import 'package:NovaSchool/models/messageResponse.dart';

class MessagesResponse {
  final List<MessageResponse> content;
  final int count;
  final int countNoRead;
  final int totalCount;

  MessagesResponse({
    required this.content,
    required this.count,
    required this.countNoRead,
    required this.totalCount,
  });

  factory MessagesResponse.fromJson(Map<String, dynamic> json) {
    var list = json['content'] as List;
    List<MessageResponse> contentList = list.map((i) => MessageResponse.fromJson(i)).toList();

    return MessagesResponse(
      content: contentList,
      count: json['count'],
      countNoRead: json['countNoRead'],
      totalCount: json['totalCount'],
    );
  }
}
