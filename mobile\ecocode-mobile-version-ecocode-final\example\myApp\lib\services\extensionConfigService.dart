import 'dart:convert';
import 'package:NovaSchool/models/extensionConfig.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
class ExtensionConfigService {
  final String baseUrl= dotenv.get('baseURL');

  // ExtensionConfigService(this.baseUrl);

  Future<List<ExtensionConfig>> getAll() async {
    final response = await http.get(Uri.parse('$baseUrl/extension-config'));
    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((e) => ExtensionConfig.fromJson(e)).toList();
    } else {
      throw Exception('Failed to fetch extension configs');
    }
  }

  Future<ExtensionConfig> getById(int id) async {
    final response =
        await http.get(Uri.parse('$baseUrl/extension-config/$id'));
        print('$baseUrl/api/extension-config/$id');
    if (response.statusCode == 200) {
      return ExtensionConfig.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to fetch extension config with ID $id');
    }
  }

}