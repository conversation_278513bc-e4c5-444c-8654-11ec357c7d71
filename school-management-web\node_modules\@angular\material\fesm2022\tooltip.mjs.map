{"version": 3, "file": "tooltip.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/tooltip/tooltip-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n * @deprecated No longer being used, to be removed.\n * @breaking-change 21.0.0\n */\nexport const matTooltipAnimations: {\n  readonly tooltipState: any;\n} = {\n  // Represents:\n  // trigger('state', [\n  //   state('initial, void, hidden', style({opacity: 0, transform: 'scale(0.8)'})),\n  //   state('visible', style({transform: 'scale(1)'})),\n  //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n  //   transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),\n  // ])\n\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: {\n    type: 7,\n    name: 'state',\n    definitions: [\n      {\n        type: 0,\n        name: 'initial, void, hidden',\n        styles: {type: 6, styles: {opacity: 0, transform: 'scale(0.8)'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'visible',\n        styles: {type: 6, styles: {transform: 'scale(1)'}, offset: null},\n      },\n      {\n        type: 1,\n        expr: '* => visible',\n        animation: {type: 4, styles: null, timings: '150ms cubic-bezier(0, 0, 0.2, 1)'},\n        options: null,\n      },\n      {\n        type: 1,\n        expr: '* => hidden',\n        animation: {type: 4, styles: null, timings: '75ms cubic-bezier(0.4, 0, 1, 1)'},\n        options: null,\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAQA;;;;;AAKG;AACU,MAAA,oBAAoB,GAE7B;;;;;;;;;AAUF,IAAA,YAAY,EAAE;AACZ,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,OAAO;AACb,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,uBAAuB;gBAC7B,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC/E,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,SAAS;AACf,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,UAAU,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACjE,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,cAAc;AACpB,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,kCAAkC,EAAC;AAC/E,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,aAAa;AACnB,gBAAA,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,iCAAiC,EAAC;AAC9E,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}