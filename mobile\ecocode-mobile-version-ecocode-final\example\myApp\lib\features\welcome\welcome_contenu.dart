// import 'dart:io';

// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
// import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
// import 'package:logger/logger.dart';
// import '../../commun/teacher_app/app_TnavBar.dart';
// import '../admin/home/<USER>';
// import '../../utils/constants/colors.dart';
// import '../../utils/constants/images.dart';
// import '../../utils/constants/text.dart';
// import '../login/login_screen.dart';
// import 'package:openid_client/openid_client.dart';
// import '../../openid_io.dart'
//     if (dart.library.html) '../../openid_browser.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:jose/jose.dart';

// final log = Logger();
// const keycloakUri = 'http://....:9001/realms/Prestacode'; //keycloakUri from base url
// const scopes = ['openid', 'profile', 'email'];
// const adminRole = 'admin';
// const parentRole = 'parent';
// const enseignantRole = 'enseignant';
// const agentRole = 'agent';
// Credential? credential;
// late final Client client;

// Future<Client> getClient() async {
//   var uri = Uri.parse(keycloakUri);
//   if (!kIsWeb && Platform.isAndroid) uri = uri.replace(host: '********');
//   var clientId = 'ishrakschool-back';  //base Url

//   var issuer = await Issuer.discover(uri);
//   return Client(issuer, clientId);
// }

// class WelcomeContenu extends StatelessWidget {
//   const WelcomeContenu({
//     Key? key,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           /// Logo de l'école
//           const Image(
//             image: AssetImage(AppImages.schoolLogo),
//             width: 280,
//           ),
//           Padding(
//             padding: const EdgeInsets.all(40.0),
//             child: SingleChildScrollView(
//               physics: const AlwaysScrollableScrollPhysics(),
//               child: GestureDetector(
//                 // onTap: () {
//                 //   /// Ouvre une boîte de dialogue pour la connexion
//                 //   showGeneralDialog(
//                 //     barrierDismissible: true, /// Permet de fermer la boîte de dialogue en cliquant en dehors d'elle
//                 //     barrierLabel: 'Commencer',
//                 //     context: context,
//                 //     pageBuilder: (context, _, __) => Center(
//                 //       child: LoginScreen(), /// Affiche l'écran de connexion
//                 //     ),
//                 //   );
//                 // },
//                 onTap: () async {
//                   // Initialize the client if not already initialized
//                   client = await getClient();
//                   log.i('client: ${client}');
//                   try {
//                     log.i('client: ');
//                     // Authenticate and get credential
//                     var credential = await authenticate(client, scopes: scopes);

//                     // Log the access token
//                     log.i('Access Token: ${credential.accessToken}');

//                     // Extract roles from token
//                     var roles = extractRolesFromToken(
//                         credential.idToken.toCompactSerialization());

//                     // Store token and roles in local storage
//                     await storeTokenAndRoles(
//                         credential.idToken.toCompactSerialization(), roles);

//                     // Navigate based on roles
//                     navigateBasedOnRoles(context, roles);
//                   } catch (e, stackTrace) {
//                     log.e('Error during authentication: $e');
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       SnackBar(
//                         content: Text('Error during authentication: $e'),
//                         duration: const Duration(seconds: 5),
//                       ),
//                     );
//                   }
//                 },

//                 /// Bouton de connexion
//                 /// Login Button
//                 child: Container(
//                   height: 50,
//                   decoration: const BoxDecoration(
//                     color: Colors.blue,
//                     borderRadius: BorderRadius.all(Radius.circular(30)),
//                   ),
//                   child: const Center(
//                     child: Text(
//                       'Login',
//                       style: TextStyle(
//                         fontSize: 18,
//                         fontWeight: FontWeight.w500,
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   List<String> extractRolesFromToken(String accessToken) {
//     log.i('Access Token: $accessToken'); // Log the access token string

//     final jwt = JsonWebToken.unverified(accessToken);
//     final claims = jwt.claims.toJson();

//     log.i('Claims: $claims'); // Log the entire claims including resource_access

//     final resourceAccess = claims['resource_access'] as Map<String, dynamic>?;

//     final roles = <String>[];
//     if (resourceAccess != null) {
//       final clientRoles =
//           resourceAccess['ishrakschool-back']?['roles'] as List<dynamic>?;
//       if (clientRoles != null) {
//         roles.addAll(clientRoles.map((role) => role.toString()));
//       }
//     }

//     return roles;
//   }

//   Future<void> storeTokenAndRoles(String token, List<String> roles) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     await prefs.setString('token', token);
//     await prefs.setStringList('roles', roles);
//   }

//   void navigateBasedOnRoles(BuildContext context, List<String> roles) {
//     if (roles.contains(adminRole)) {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => AHomeScreen()),
//       );
//     } else if (roles.contains(enseignantRole)) {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => THomeScreen()),
//       );
//     }else if (roles.contains(parentRole)) {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => ParentAppNavBar()),
//       );
//     }
//      else {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => AHomeScreen()),
//       );
//     }
//   }
// }

import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/login/login_services.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/images.dart';
import '../../utils/constants/text.dart';
import '../login/login_screen.dart';
import 'package:provider/provider.dart';

class WelcomeContenu extends StatelessWidget {
  const WelcomeContenu({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AuthService _loginService = AuthService();
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          /// Logo de l'école
          Image(
            image: AssetImage(dotenv.get('schoolLogo')),
            width: 280,
          ),
          // Image.network(
          //   '$schoolLogo',
          //   width: 280,
          //   // errorBuilder: (context, error, stackTrace) {
          //   //   return const Text('Failed to load logo');
          //   // },
          //   loadingBuilder: (context, child, loadingProgress) {
          //     if (loadingProgress == null) return child;
          //     return const CircularProgressIndicator();
          //   },
          // ),
          Padding(
            padding: const EdgeInsets.all(40.0),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: GestureDetector(
                onTap: () async {
                  // Check if the user is authenticated
                  if (await _loginService.isAuthenticated() == false) {
                    // Show login dialog if the user is not authenticated
                    showGeneralDialog(
                      barrierDismissible:
                          true, // Allow closing the dialog by clicking outside
                      barrierLabel: 'Commencer',
                      context: context,
                      pageBuilder: (context, _, __) => Center(
                        child:
                            LoginScreen(), // Show login screen if not authenticated
                      ),
                    );
                  } else {
                    // If authenticated, redirect based on the role
                    if (await _loginService.isAdmin() == true) {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(builder: (context) => AdminAppNavBar()),
                      );
                    } else if (await _loginService.isParent() == true) {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                            builder: (context) => ParentAppNavBar()),
                      );
                    } else if (await _loginService.isEnseiganat() == true) {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(builder: (context) => THomeScreen()),
                      );
                    } else {
                      _showErrorDialog(
                          context, 'Rôle utilisateur inconnu ou non autorisé.');
                    }
                  }
                },

                /// Bouton de connexion
                child: Container(
                  height: 50,
                  decoration: const BoxDecoration(

                    gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
                    //color: Color(0xFF4099FF),
                    borderRadius: BorderRadius.all(Radius.circular(30)),
                  ),
                  child: const Center(
                    child: Text(
                      AppText.WelcomeButtonText,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFFFFFFFF),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

void _showErrorDialog(BuildContext context, String message) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text("Erreur"),
        content: Text(message),
        actions: <Widget>[
          TextButton(
            child: Text("OK"),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    },
  );
}
