import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';

class EleveResponseDTO {
  final List<EleveInfoPersoDTO> profilEleveDto;
  final String imageEleve;

  EleveResponseDTO({
    required this.profilEleveDto,
    required this.imageEleve,
  });

  factory EleveResponseDTO.fromJson(Map<String, dynamic> json) {
    return EleveResponseDTO(
      profilEleveDto: (json['profilEleveDto'] as List<dynamic>)
          .map((eleveJson) => EleveInfoPersoDTO.fromJson(eleveJson))
          .toList(),
      imageEleve: json['imageEleve'] ?? '',
    );
  }
}
