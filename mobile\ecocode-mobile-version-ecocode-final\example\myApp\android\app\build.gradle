plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.example.NovaSchool"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion
    ndkVersion "25.1.8937393"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true 
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    buildFeatures {
        buildConfig true // Ensure this line is correct
    }

    defaultConfig {
        applicationId "com.example.NovaSchool"
        minSdkVersion flutter.minSdkVersion
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        manifestPlaceholders = [
            'appAuthRedirectScheme': 'com.example.NovaSchool',
        ]

        // Set app_name dynamically using resValue based on the APP_NAME from env files
        resValue "string", "app_name", project.hasProperty("APP_NAME") ? project.APP_NAME : "NovaSchool"
    }

    flavorDimensions "default"

    productFlavors {
        local {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"LocalSchool\""
            applicationId "com.example.LocalSchool"
        }
        staging {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"TestSchool\""
            applicationId "com.example.StagingSchool"
        }
        nova {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"NovaSchool\""
            applicationId "com.example.NovaSchool"
            resValue "string", "app_name", "NovaSchool"
        }
        ichrak {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"IchrakSchool\""
            applicationId "com.example.IchrakSchool"
            resValue "string", "app_name", "IchrakSchool"
            resValue "mipmap", "ic_launcher", "@mipmap/ic_launcher_ichrak"
        }
        horizon {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"CollegeHorizon\""
            applicationId "com.example.CollegeHorizon"
            resValue "string", "app_name", "CollegeHorizon"
        }
        demo {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"DemoSchool\""
            applicationId "com.example.DemoSchool"
            resValue "string", "app_name", "DemoSchool"
        }
        amana {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"AmanaSchool\""
            applicationId "com.example.AmanaSchool"
            resValue "string", "app_name", "AmanaSchool"
        }
        loujayn {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"LoujaynSchool\""
            applicationId "com.example.LoujaynSchool"
            resValue "string", "app_name", "LoujaynSchool"
        }
        jjr {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"JJRSchool\""
            applicationId "com.example.JJRSchool"
            resValue "string", "app_name", "JJRSchool"
        }
        tacapes {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"TacapesLelMaarifa\""
            applicationId "com.example.TacapesLelMaarifa"
            resValue "string", "app_name", "TacapesLelMaarifa"
        }
        excellence {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"ExcellencePrivee\""
            applicationId "com.example.ExcellencePrivee"
            resValue "string", "app_name", "ExcellencePrivee"
        }
        essedik {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"EssiddikPrivee\""
            applicationId "com.example.EssiddikPrivee"
            resValue "string", "app_name", "EssiddikPrivee"
        }
        pinacle {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"PinaclePrivee\""
            applicationId "com.example.PinaclePrivee"
            resValue "string", "app_name", "PinaclePrivee"
        }
        eppm {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"EPPM\""
            applicationId "com.example.EPPM"
            resValue "string", "app_name", "EPPM"
        }
        amine {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"AmineSchool\""
            applicationId "com.example.AmineSchool"
            resValue "string", "app_name", "AmineSchool"
        }
        anouar {
            dimension "default"
            buildConfigField "String", "APP_NAME", "\"AnouarEP\""
            applicationId "com.example.AnouarEP"
            resValue "string", "app_name", "AnouarEP"
        }
       
    }

    buildTypes {
        release {
            signingConfig signingConfigs.debug
        }
    }

    // Ensure that the correct output file names are used for the APKs
    applicationVariants.all { variant ->
        variant.outputs.all {
            // Use the flavor-specific app_name
            outputFileName = "${variant.buildType.name}-${variant.productFlavors[0].name}.apk"
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
    implementation 'androidx.multidex:multidex:2.0.1'
}

