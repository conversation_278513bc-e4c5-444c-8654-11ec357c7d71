import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

import '../../../../../utils/constants/size.dart';
import '../../../../../utils/device/devices_utility.dart';

class AppBarConfig extends StatelessWidget implements PreferredSizeWidget{
  const AppBarConfig ({
    Key? key,
    this.title,
    this.showBackArrow = false,
    this.leadingIcon,
    this.actions,
    this.leadingPressed,

  }) : super(key: key);

  final Widget? title;
  final bool? showBackArrow;
  final IconData? leadingIcon;
  final List<Widget>? actions;
  final VoidCallback? leadingPressed;




  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: AppSize.md,left: 5,top: 0),
      child: AppBar(
          automaticallyImplyLeading: false,
          leading: showBackArrow!
              ? IconButton(onPressed: () => Get.back(), icon: const Icon(Iconsax.arrow_left))
              : leadingIcon!= null ? IconButton(onPressed: leadingPressed, icon: Icon(leadingIcon)) : null,
          title: title,
          actions: actions,

      ),
    );
  }


  @override
  //Size get preferredSize => const Size.fromHeight(500); // ou ajuste selon le contenu réel

Size get preferredSize => Size.fromHeight(DevicesUtility.getAppBarHeight());
}