import 'package:flutter/material.dart';
import 'package:NovaSchool/utils/constants/colors.dart';

///  Méthode pour créer un Fomulaire
Widget buildRowWithIconAndText(IconData icon, String text1, String text2) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(15),
    ),
    child: Padding(
      padding: const EdgeInsets.only(bottom: 25, left: 10, top: 25),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.2),
              shape: BoxShape.circle,

               // gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),

            ),
            padding: EdgeInsets.all(10),
            child: Icon(icon, size: 40, color: Colors.lightBlue),//(0xFF4099FF)),
          ),
          Sized<PERSON>ox(width: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                text1,
                style: TextStyle(
                  fontSize: 19,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF4099FF),
                ),
              ),
              Text(
                text2,
                style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                    fontSize: 17),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}


/// Méthode pour créer une ligne de séparation
Widget buildSeparator() {
  return Padding(
    padding: const EdgeInsets.only(left: 10,right: 10),
    child: Container(
      height: 1,
      color: Colors.grey.withOpacity(0.5),
    ),
  );
}