import 'dart:convert';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/tokenInterceptor.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:NovaSchool/models/trimesterDataDto.dart';
import 'package:http_interceptor/http_interceptor.dart';
class CarnetService {
  late final String baseCarnetUrl;
  final httpClient =
      InterceptedClient.build(interceptors: [TokenInterceptor()]);
  final auth = AuthService();
  late final String baseURL;

  CarnetService() {
    baseURL = dotenv.get('baseURL');
    baseCarnetUrl = "$baseURL/note";
  }

  Future<TrimesterDataDto> getElevesCarnet() async {
    final eleveId = await auth.getEleveId();
    final url = Uri.parse('$baseCarnetUrl/carnet?idEleve=$eleveId');
    print('url $url');

    try {
      final response = await httpClient.get(url);
      print(response.statusCode);
      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        print('jsonResponse $jsonResponse');
        return TrimesterDataDto.fromJson(jsonResponse);
      } else {
        throw Exception('Failed to load data');

      }
    } catch (e) {
      print('error $e');
      throw Exception('Error fetching data: $e');
      
    }
  }
}
