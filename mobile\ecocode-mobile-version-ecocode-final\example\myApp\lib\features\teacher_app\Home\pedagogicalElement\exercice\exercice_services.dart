import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../../commun/base_url.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';


class ExerciceService {
  // String baseURL = dotenv.get('baseURL');
  // static String ExerciceUrl = '$baseURL/exercices';
  late final String baseURL;
  late final String ExerciceUrl;


  ExerciceService(){
    baseURL = dotenv.get('baseURL');
    ExerciceUrl = '$baseURL/exercices';
  }

  Future<List<Map<String, dynamic>>> getExercicesByEnseignantId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');
    int? userId = prefs.getInt('userId');

    final String apiUrl = '$ExerciceUrl/enseignant';

    try {
      final response = await http.get(
        Uri.parse('$apiUrl?enseignantId=$userId'),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        List<dynamic> data = json.decode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Failed to load exercices with status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to load exercices: $e');
    }
  }

  // Ajouter exercice avec l'id de l'enseignant
  Future<void> createExercice(Map<String, dynamic> exerciceData) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');
    int? userId = prefs.getInt('userId');

    final String apiUrl = '$ExerciceUrl/creerExercice?enseignantId=$userId';

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(exerciceData),
      );

      if (response.statusCode == 200) {
        // L'exercice a été créé avec succès
      } else {
        throw Exception('Failed to create exercice with status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to create exercice: $e');
    }
  }

  // Méthode pour uploader un fichier sur Cloudinary et obtenir l'URL
  Future<String> uploadFile(Uint8List fileBytes, String fileName) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');

    final String apiUrl = '$ExerciceUrl/uploadFile';
    final request = http.MultipartRequest('POST', Uri.parse(apiUrl))
      ..headers['Authorization'] = 'Bearer $token'
      ..files.add(http.MultipartFile.fromBytes('file', fileBytes, filename: fileName));

    final response = await request.send();
    if (response.statusCode == 200) {
      final responseBody = await response.stream.bytesToString();
      final jsonResponse = json.decode(responseBody);
      return jsonResponse['fileUrl']; // URL du fichier uploadé
    } else {
      throw Exception('Failed to upload file with status code: ${response.statusCode}');
    }
  }

  // Modifier exercice avec l'id de l'exercice
  Future<void> updateExercice(int id, Map<String, dynamic> exerciceData) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');

    final String apiUrl = '$ExerciceUrl/modifierExercice/$id';

    try {
      final response = await http.put(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(exerciceData),
      );

      if (response.statusCode == 200) {
        // L'exercice a été modifié avec succès
      } else {
        throw Exception('Failed to update exercice with status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to update exercice: $e');
    }
  }

  // Supprimer exercice avec l'id de l'enseignant
  Future<void> deleteExercice(int id) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');

    final String apiUrl = '$ExerciceUrl/supprimerExercice/$id';

    try {
      final response = await http.delete(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 204) {
        // L'exercice a été supprimé avec succès
      } else {
        throw Exception('Failed to delete exercice with status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to delete exercice: $e');
    }
  }
}
