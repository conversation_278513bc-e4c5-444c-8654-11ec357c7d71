{"project_info": {"project_number": "817685344848", "project_id": "ecocode-51a9c", "storage_bucket": "ecocode-51a9c.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:817685344848:android:d97ac178e59cc4bc82d755", "android_client_info": {"package_name": "com.example.AmanaSchool"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:88b7b1085e5a0b9982d755", "android_client_info": {"package_name": "com.example.CollegeHorizon"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:10f17fae60515d9782d755", "android_client_info": {"package_name": "com.example.DemoSchool"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:7acc332b0fba879c82d755", "android_client_info": {"package_name": "com.example.EssiddikPrivee"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:44e1577703c1849082d755", "android_client_info": {"package_name": "com.example.ExcellencePrivee"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:6d1ec56f3bd11c7282d755", "android_client_info": {"package_name": "com.example.IchrakSchool"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:4591d7b609a263a682d755", "android_client_info": {"package_name": "com.example.JJRSchool"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:11442e94f6c210ca82d755", "android_client_info": {"package_name": "com.example.LoujaynSchool"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:7a9d41be5d48503782d755", "android_client_info": {"package_name": "com.example.NovaSchool"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:da62c781ba6a7b4a82d755", "android_client_info": {"package_name": "com.example.PinaclePrivee"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:28ca1511ffd08e2682d755", "android_client_info": {"package_name": "com.example.StagingSchool"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:d01b3393a9bce92f82d755", "android_client_info": {"package_name": "com.example.TacapesLelMaarifa"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:817685344848:android:adc12f26b3bc39bb82d755", "android_client_info": {"package_name": "com.example.flutter_notification"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyB9LXzPrFdmW59MW49bH40lg_y6L-v-gtQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}