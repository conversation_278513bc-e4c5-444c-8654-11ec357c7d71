class Exercice {
  final int id;
  final String matiere;
  final String details;
  final String date;
  final String? fichierUrl;
  final String niveau;
  final List<String> classe;
  final bool toutesLesClasses;
  final int enseignantId;

  Exercice({
    required this.id,
    required this.matiere,
    required this.details,
    required this.date,
    this.fichierUrl,
    required this.niveau,
    required this.classe,
    required this.toutesLesClasses,
    required this.enseignantId,
  });

  factory Exercice.fromJson(Map<String, dynamic> json) {
    return Exercice(
      id: json['id'] ?? 0,
      matiere: json['matiere'] ?? '',
      details: json['details'] ?? '',
      date: json['date'] ?? '',
      fichierUrl: json['fichierUrl'],
      niveau: json['niveau'] ?? '',
      classe: List<String>.from(json['classe'] ?? []),
      toutesLesClasses: json['toutesLesClasses'] ?? false,
      enseignantId: json['enseignantId'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'matiere': matiere,
      'details': details,
      'date': date,
      'fichierUrl': fichierUrl,
      'niveau': niveau,
      'classe': classe,
      'toutesLesClasses': toutesLesClasses,
      'enseignantId': enseignantId,
    };
  }
}
