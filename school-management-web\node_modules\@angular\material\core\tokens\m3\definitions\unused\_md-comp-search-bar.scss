//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'avatar-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'avatar-size': if($exclude-hardcoded-values, null, 30px),
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level3'),
    'container-height': if($exclude-hardcoded-values, null, 56px),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'hover-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'hover-supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'input-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'input-text-font': map.get($deps, 'md-sys-typescale', 'body-large-font'),
    'input-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-large-line-height'),
    'input-text-size': map.get($deps, 'md-sys-typescale', 'body-large-size'),
    'input-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.search-bar.input-text.tracking cannot be represented in the "font" property
    // shorthand. Consider using the discrete properties instead.
    'input-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-large-weight')
          map.get($deps, 'md-sys-typescale', 'body-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-large-font')
      ),
    'input-text-weight': map.get($deps, 'md-sys-typescale', 'body-large-weight'),
    'leading-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'pressed-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'pressed-supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'supporting-text-font':
      map.get($deps, 'md-sys-typescale', 'body-large-font'),
    'supporting-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-large-line-height'),
    'supporting-text-size':
      map.get($deps, 'md-sys-typescale', 'body-large-size'),
    'supporting-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.search-bar.supporting-text.tracking cannot be represented in the "font"
    // property shorthand. Consider using the discrete properties instead.
    'supporting-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'body-large-weight')
          map.get($deps, 'md-sys-typescale', 'body-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-large-font')
      ),
    'supporting-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-large-weight'),
    'trailing-icon-color': map.get($deps, 'md-sys-color', 'on-surface-variant')
  );
}
