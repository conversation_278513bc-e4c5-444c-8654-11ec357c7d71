import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../utils/constants/colors.dart';

class WelcomeLanguage extends StatelessWidget {

  /// Définition du dégradé linéaire pour le titre
  final Shader _linearGradient = const LinearGradient(
    colors: [Color(0xFF4099FF), Color(0xFF4099FF)],
    begin: Alignment.centerLeft,
    end: Alignment.bottomRight,
  ).createShader(const Rect.fromLTWH(0.0, 0.0, 350.0, 80.0));

  WelcomeLanguage({Key? key,}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// Position de l'icône de sélection de la langue
    return Positioned(
      top: 20,
      right: 20,
      child: IconButton(
        icon: Icon(Icons.language, color: Color(0xFF4099FF)),
        onPressed: () {
          /// Ouvre une boîte de dialogue pour sélectionner la langue
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Container(
                  /// size de boîte de dialogue
                  width: 300,
                  height: 200,
                  decoration: BoxDecoration(
                    /// Décoration de la boîte de dialogue
                    color: Colors.white.withOpacity(0.94),
                    borderRadius: BorderRadius.all(Radius.circular(20)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: Offset(0, 5),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.grey,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      /// Titre de la boîte de dialogue
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: Text(
                            'Choisir la langue',
                            textAlign: TextAlign.center,
                            style: GoogleFonts.dmSerifDisplay(
                              fontSize: 27,
                              fontWeight: FontWeight.w500,
                              foreground: Paint()..shader = _linearGradient,
                            ),
                          ),
                        ),
                      ),
                      /// les options a sélecter
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        highlightColor: Color(0xFF4099FF),
                        splashColor: Color(0xFF4099FF).withOpacity(0.3),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Center(
                            child: Text(
                              'Français',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 20,
                              ),
                            ),
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        highlightColor: Color(0xFF4099FF),
                        splashColor: Color(0xFF4099FF).withOpacity(0.9),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Center(
                            child: Text(
                              'العربية',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 20,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
