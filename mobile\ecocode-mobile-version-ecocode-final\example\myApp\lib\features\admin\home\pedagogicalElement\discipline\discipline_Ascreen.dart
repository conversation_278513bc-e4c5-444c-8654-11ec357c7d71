import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/admin/A_appDrawer.dart';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/admin/home/<USER>/discipline/discipline_Aajout.dart';
import 'package:NovaSchool/features/admin/profil/profil_Ascreen.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/models/disciplineEntity.dart';
import 'package:NovaSchool/models/disciplineEntityResponse.dart';
import 'package:NovaSchool/models/messageResponse.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/discipline_service.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
class ADisciplineScreen extends StatefulWidget {
  @override
  _ADisciplineScreenState createState() => _ADisciplineScreenState();
}

class _ADisciplineScreenState extends State<ADisciplineScreen> {
  List<DisciplineEntityResponse> disciplines = [];
  bool _isLoading = true;
  int _itemsPerPage = 0;
  int _currentPageIndex = 0;
  final int _pageSize = 5;
  int _totalPages = 0;
  ScrollController _scrollController = ScrollController();
  final AuthService auth = AuthService();
  final DisciplineServices disciplineService = DisciplineServices();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateItemsPerPage();
    });
    _fetchDisciplines();
  }

  Future<void> _fetchDisciplines({int page = 0}) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final disciplineList = await disciplineService.getAllDisciplines(
        page: page,
        size: _pageSize,
      );

      if (mounted) {
        setState(() {
          disciplines = disciplineList.content;
          _totalPages = disciplineList.totalPages;
          _currentPageIndex = page;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching disciplines: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      // Display an error dialog or SnackBar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading disciplines. Please try again.')),
      );
    }
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true;
      });
      _fetchDisciplines(page: newIndex);
    }
  }

  Future<Map<String, String>> _getUserInfo() async {
    return await AuthService().getSenderDetails();
  }

  @override
  Widget build(BuildContext context) {
    List<DisciplineEntityResponse> currentPageDisciplines = disciplines;

    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AdminDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            iconTheme: IconThemeData(
              color: Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            title: GestureDetector(
              onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                      builder: (context) =>
                          AdminAppNavBar()), // remplace THomeScreen par ton écran d'accueil
                );
              },
            /*title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<ANavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => AdminAppNavBar());
              },*/
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),
                ],
              ),
            ),
            /*Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/logos/ecocode.png',
                  height: 150,
                  color: Colors.white,
                ),
                SizedBox(width: 10),

              ],
            ),*/
            actions: [ IconButton(
              icon: Icon(Icons.logout, color: Colors.white),
              //title: Text('Déconnexion'),
              onPressed: () async {
                await AuthService.logoutKeycloak();
              },
            ),
             /* IconButton(
                icon: Icon(
                  Icons.person_outline,
                  color: Colors.white,
                  size: 30,
                ),
                onPressed: () async {
                  Map<String, String> userInfo = await _getUserInfo();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AProfilScreen(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                        email: userInfo['email'] ?? '',
                        profile: userInfo['profile'] ?? '',
                      ),
                    ),
                  );
                },
              ),*/
              /*IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  onPressed: () {},
                ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: Column(
          children: [
            Container(
              child: Column(
                children: [
                  SecondHeadBar1(title: 'Disciplines',icon: Icons.priority_high_outlined,),

                  /*PrimaryHeaderContainer(
                    child: Container(
                      height: 100,
                      child: SecondHeadBar(
                        title: 'Discipline',
                        titleColor: Colors.white,
                        iconColor: Colors.white,
                      ),
                    ),
                  ),*/
                  Padding(
                    padding: EdgeInsets.all(15.0),
                    child: Center(
                      child: Container(
                        padding: EdgeInsets.all(10.0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15.0),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15.0),
                         /* child: Image.asset(
                            AppImages.discipline,
                            width: 200,
                            height: 100,
                            fit: BoxFit.contain,
                          ),*/
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Discipline List
            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                      height: 510,
                      child: disciplines.isEmpty
                          ? Center(
                              child: Text(
                                "Aucune discipline trouvée.",
                                style:
                                    TextStyle(fontSize: 18, color: Colors.grey),
                              ),
                            )
                          : RefreshIndicator(
                              onRefresh: _fetchDisciplines,
                              child: ListView(
                                padding: EdgeInsets.all(10.0),
                                children: currentPageDisciplines
                                    .map((discipline) => Card(
                                          elevation: 4,
                                          margin: EdgeInsets.symmetric(
                                              vertical: 8.0),
                                          color: Colors.white,
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12.0),
                                          ),
                                          child: ExpansionTile(
                                            tilePadding: EdgeInsets.symmetric(
                                                horizontal: 16.0,
                                                vertical: 8.0),
                                            title: Text(
                                              discipline.typeDiscipline,
                                              style: TextStyle(
                                                fontSize: 18.0,
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFF000010),
                                              ),
                                            ),

                                            subtitle: Column(

                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(height: 5),
                                                Text(
                                                  "Nom: ${discipline.nomEleve} ${discipline.prenomEleve}",
                                                  style: TextStyle(
                                                    color: Colors.grey[800],
                                                  ),
                                                ),

                                                Text(
                                                  "Classe: ${discipline.niveauClasse} ${discipline.nomClasse}",
                                                  style: TextStyle(
                                                    color: Colors.grey[800],
                                                  ),
                                                ),
                                                Text(
                                                  "Date: ${discipline.dateDiscipline.toLocal().toString().split(' ')[0]}",
                                                  style: TextStyle(
                                                    color: Colors.grey[800],
                                                  ),
                                                ),
                                              ],
                                            ),
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 16.0,
                                                        vertical: 8.0),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    // Motif at the top of the expanded section
                                                    Text(
                                                      "Motif: ${discipline.motifDiscipline}",
                                                      style: TextStyle(
                                                        fontSize: 16.0,
                                                        color: Colors.grey[800],
                                                      ),
                                                    ),//SizedBox(width: 100),
                                                    // Any additional content can go below if needed
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ))
                                    .toList(),
                              ),
                            ),
                    ),
            ),
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: Icon(Icons.arrow_back_ios,
                        color: _currentPageIndex > 0
                            ? Color(0xFF4099FF)
                            : Colors.grey,
                        size: 20),
                    onPressed: _currentPageIndex > 0
                        ? () {
                            _fetchDisciplines(
                                page: _currentPageIndex -
                                    1); // Fetch previous page
                          }
                        : null,
                  ),
                  Text(
                    '${_currentPageIndex + 1} / $_totalPages',
                    style: TextStyle(color: Colors.grey, fontSize: 15),
                  ),
                  IconButton(
                    icon: Icon(Icons.arrow_forward_ios,
                        color: _currentPageIndex < _totalPages - 1
                            ? Color(0xFF4099FF)
                            : Colors.grey,
                        size: 20),
                    onPressed: _currentPageIndex < _totalPages - 1
                        ? () {
                            _fetchDisciplines(
                                page: _currentPageIndex + 1); // Fetch next page
                          }
                        : null,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Color(0xFF4099FF),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => AjoutDiscipline(
                      onDisciplinesAdded: () {
                        _fetchDisciplines();
                      },
                    )),
          );
        },
        child: Icon(
          Icons.add,
          color: AppColors.light,
        ),
      ),
    );
  }
}
