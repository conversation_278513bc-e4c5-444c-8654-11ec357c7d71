class IClasseProjection {
  final String nomClasse;
  final String niveauClasse;
  final int idClasse;

  IClasseProjection({required this.nomClasse, required this.idClasse, required this.niveauClasse});

  factory IClasseProjection.fromJson(Map<String, dynamic> json) {
    return IClasseProjection(
      nomClasse: json['nomClasse'] as String,
      niveauClasse: json['niveauClasse'] as String,
      idClasse: json['idClasse'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nomClasse': nomClasse,
      'niveauClasse': niveauClasse,
      'idClasse': idClasse,
    };
  }
}