import 'dart:async';
import 'dart:convert';
import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/models/messageResponse.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/observation/observation_details.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/message_services.dart';
import '../../../../../utils/constants/images.dart';
import 'package:get/get.dart';

class PObservationScreen extends StatefulWidget {
  final Function(String) onMessageRead;
  const PObservationScreen({Key? key, required this.onMessageRead})
      : super(key: key);

  @override
  _PObservationScreenState createState() => _PObservationScreenState();
}

class _PObservationScreenState extends State<PObservationScreen> {
  List<MessageResponse> observations = [];
  bool _isLoading = true;
  int _itemsPerPage = 0;
  int _currentPageIndex = 0;
  final int _pageSize = 5;
  int _totalPages = 0;
  ScrollController _scrollController = ScrollController();
  MessageServices msg = MessageServices();
  AuthService auth = AuthService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateItemsPerPage();
    });
    _fetchObservations();
  }

  Future<void> _fetchObservations({int page = 0}) async {
    setState(() {
      _isLoading = true;
    });
    List<MessageResponse> fetchedObservations = await msg.fetchMessagesForParent(
      type: "Observation",
      page: page,
      pageSize: _pageSize,
      isReceiver: true,
    );

    if (mounted) {
      setState(() {
        observations = fetchedObservations;
        _totalPages = (fetchedObservations.length / _pageSize).ceil();
        _isLoading = false;
      });
    }
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true; // Set loading to true before fetching new page
      });
      _fetchObservations(page: newIndex);
    }
  }

  Future<void> _readMessage(MessageResponse observation) async {
    await msg.markMessageAsRead(observation);
    setState(() {
      observation.read = true;
    });
    widget.onMessageRead('Observation');
  }

  Future<void> _handleMessageTap(BuildContext context, MessageResponse observations, String messageType) async {
    if (!observations.read) {
      await _readMessage(observations);
    }

    await msg.navigateToMessageDetail(context, observations, messageType);
  }

  @override
  Widget build(BuildContext context) {
    List<MessageResponse> currentPageObservations = observations.isEmpty
        ? []
        : observations
            .skip(_currentPageIndex * _pageSize)
            .take(_pageSize)
            .toList();

    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<PNavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => ParentAppNavBar());
              },
              child: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: Column(
          children: [
            // Conteneur haut avec bouton retour + image sur la même ligne
            Padding(
              padding: EdgeInsets.all(15.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Tooltip(
                    message: 'Retour',
                    child: IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Icons.arrow_back_ios, color: Colors.blueGrey),
                    ),
                  ),
                  SizedBox(width: 35), // Espacement entre l'icône et l'image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(15.0),
                    child: Image.asset(
                      AppImages.observation,
                      width: 200,
                      height: 100,
                      fit: BoxFit.contain,
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                      height: 510,
                      child: observations.isEmpty
                          ? Center(
                              child: Text("Aucune observation trouvée.",
                                  style: TextStyle(
                                      fontSize: 18, color: Colors.grey)))
                          : RefreshIndicator(
                              onRefresh: () => _fetchObservations(),
                              child: ListView.builder(
                                shrinkWrap: true,
                                padding: EdgeInsets.zero,
                                controller: _scrollController,
                                itemCount: currentPageObservations.length +
                                    1, // Add 1 for the pagination button
                                itemBuilder: (context, index) {
                                  if (index < currentPageObservations.length) {
                                    MessageResponse observations =
                                        currentPageObservations[index];

                                    return GestureDetector(
                                      onTap: () async {
                                       await _handleMessageTap(context, observations, 'Observation');
                                      },
                                      child: Column(
                                        children: [
                                          SizedBox(height: 8.0),
                                          Container(
                                            padding: EdgeInsets.all(14.0),
                                            margin: EdgeInsets.only(
                                                bottom: 10.0,
                                                left: 10.0,
                                                right: 10.0),
                                            decoration: BoxDecoration(
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.5),
                                                  spreadRadius: 0,
                                                  blurRadius: 5,
                                                  offset: Offset(0, 1),
                                                ),
                                              ],
                                              color: observations.read
                                                  ? Colors.white
                                                  : Color(0xFFE1F5FE),
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        observations
                                                            .messageEntity
                                                            .objet,
                                                        style: TextStyle(
                                                          fontSize: 18.0,
                                                          color:
                                                              Color(0xFF4099FF),
                                                          fontWeight:
                                                              observations.read
                                                                  ? FontWeight
                                                                      .normal
                                                                  : FontWeight
                                                                      .bold,
                                                        ),
                                                      ),
                                                    ),
                                                    Tooltip(
                                                      message: 'Details',
                                                      child: Icon(Icons.info),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 3.0),
                                                Text(
                                                  "Date: ${observations.messageEntity.dateEnvoie.toLocal().add(Duration(hours: 1)).toString().split(' ')[0]}",
                                                  style: TextStyle(
                                                      color: Colors.grey),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    return Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          IconButton(
                                            icon: Icon(Icons.arrow_back_ios,
                                                color: _currentPageIndex > 0
                                                    ? Color(0xFF4099FF)
                                                    : Colors.grey,
                                                size: 20),
                                            onPressed: _currentPageIndex > 0
                                                ? () {
                                                    _updatePageIndex(
                                                        _currentPageIndex - 1);
                                                  }
                                                : null,
                                          ),
                                          Text(
                                            '${_currentPageIndex + 1} / $_totalPages',
                                            style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: 15),
                                          ),
                                          IconButton(
                                            icon: Icon(Icons.arrow_forward_ios,
                                                color: _currentPageIndex <
                                                        _totalPages - 1
                                                    ? Color(0xFF4099FF)
                                                    : Colors.grey,
                                                size: 20),
                                            onPressed: _currentPageIndex <
                                                    _totalPages - 1
                                                ? () {
                                                    _updatePageIndex(
                                                        _currentPageIndex + 1);
                                                  }
                                                : null,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                },
                              ),
                            ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
