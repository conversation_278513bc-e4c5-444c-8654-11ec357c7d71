import 'package:flutter/material.dart';



class EcranPrincipal extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Rihem'),
        backgroundColor: Colors.blue,
        actions: <Widget>[
          IconButton(
            icon: Icon(Icons.message),
            onPressed: () {
              // Actions quand on appuie sur l'icône de message
            },
          ),
          IconButton(
            icon: Icon(Icons.account_circle),
            onPressed: () {
              // Actions quand on appuie sur l'icône de compte
            },
          ),
        ],
      ),
      body: ListView(
        children: ListTile.divideTiles(
          context: context,
          tiles: [
            ListTile(
              leading: Icon(Icons.announcement),
              title: Text('Annonces'),
              trailing: Icon(Icons.chevron_right),
              onTap: () {
                // Actions quand on appuie sur 'Annonces'
              },
            ),
            ListTile(
              leading: Icon(Icons.book),
              title: Text('Leçons'),
              trailing: Icon(Icons.chevron_right),
              onTap: () {
                // Actions quand on appuie sur 'Leçons'
              },
            ),
            ListTile(
              leading: Icon(Icons.topic),
              title: Text('Sujets'),
              trailing: Icon(Icons.chevron_right),
              onTap: () {
                // Actions quand on appuie sur 'Sujets'
              },
            ),
            ListTile(
              leading: Icon(Icons.beach_access),
              title: Text('Vacances'),
              trailing: Icon(Icons.chevron_right),
              onTap: () {
                // Actions quand on appuie sur 'Vacances'
              },
            ),
            ListTile(
              leading: Icon(Icons.school),
              title: Text('Examens'),
              trailing: Icon(Icons.chevron_right),
              onTap: () {
                // Actions quand on appuie sur 'Examens'
              },
            ),

          ],
        ).toList(),
      ),

    );
  }
}