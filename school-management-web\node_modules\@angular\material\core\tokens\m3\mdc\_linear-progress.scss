@use 'sass:map';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, linear-progress);

/// Generates the tokens for MDC linear-progress
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of tokens for the MDC linear-progress
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $mdc-tokens: token-definition.get-mdc-tokens(
      'linear-progress-indicator', $systems, $exclude-hardcoded);
  $variant-tokens: (
    primary: (), // Default, no overrides needed
    secondary: (
      active-indicator-color: map.get($systems, md-sys-color, secondary),
      track-color: map.get($systems, md-sys-color, secondary-container),
    ),
    tertiary: (
      active-indicator-color: map.get($systems, md-sys-color, tertiary),
      track-color: map.get($systems, md-sys-color, tertiary-container),
    ),
    error: (
      active-indicator-color: map.get($systems, md-sys-color, error),
      track-color: map.get($systems, md-sys-color, error-container),
    ),
  );

  @return token-definition.namespace-tokens(
      $prefix, ($mdc-tokens, $variant-tokens), $token-slots);
}
