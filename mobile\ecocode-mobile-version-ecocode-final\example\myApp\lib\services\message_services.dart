import 'dart:async';
import 'dart:convert';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/cours/cours_details.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/discipline/discipline_details.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/exercice/exercice_details.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/observation/observation_details.dart';
import 'package:NovaSchool/models/getNumberOfNotRead.dart';
import 'package:NovaSchool/models/messageResponse.dart';
import 'package:NovaSchool/models/messageResponseDTO.dart';
import 'package:NovaSchool/services/tokenInterceptor.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/models/fileMessage.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:http_parser/http_parser.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class MessageServices {
  // String baseURL = dotenv.get('baseURL');
  // final String baseUrl = "$baseURL/messages";
  late final String baseURL;
  late final String baseUrl;
  final auth = AuthService();
  final httpClient =
      InterceptedClient.build(interceptors: [TokenInterceptor()]);

  MessageServices() {
    baseURL = dotenv.get('baseURL');
    baseUrl = "$baseURL/messages";
  }

  Future<void> createMessage(
    BuildContext context,
    Map<String, dynamic> addMessageDTO,
    List<Map<String, dynamic>> listofFiles,
  ) async {
    // String baseURL = dotenv.get('baseURL');
    // final String baseUrl = "$baseURL/messages";

    final url = Uri.parse('$baseUrl/add');
    final token = await auth.getToken();

    // Create a multipart request
    var request = http.MultipartRequest('POST', url);
    request.headers.addAll({
      'Content-Type': 'multipart/form-data',
      'Authorization': 'Bearer $token',
    });

    // Add the JSON part
    final jsonPayload = json.encode(addMessageDTO);
    request.fields['addMessageDTO'] = jsonPayload;

    // Add files if they exist
    if (listofFiles.isNotEmpty) {
      for (var file in listofFiles) {
        if (file['bytes'] == null || file['bytes']!.isEmpty) {
          print('File bytes are null for file: ${file['name']}');
          continue;
        }

        var multipartFile = http.MultipartFile.fromBytes(
          'listofFile',
          file['bytes']!,
          filename: file['name'],
          contentType: MediaType('application', 'octet-stream'),
        );

        request.files.add(multipartFile);
      }
    }

    // Log request headers and fields
    print('Request Headers: ${request.headers}');
    print('Request Fields: ${request.fields}');
    print(
        'Request Files: ${request.files.map((file) => file.filename).toList()}');

    // Send the request
    var response = await request.send();

    if (response.statusCode == 200) {
      print('Message created successfully');
    } else {
      var responseBody = await response.stream.bytesToString();
      print('Failed to create message: ${response.statusCode} - $responseBody');
    }
  }

  Future<List<MessageResponse>> getMessages({
    String? type,
    String? senderId,
    String? userIdReceiver,
    int? eleveId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 0,
    int size = 10,
  }) async {
    final queryParameters = {
      'type': type,
      'senderId': senderId,
      'userIdReceiver': userIdReceiver,
      'eleveId': eleveId?.toString(),
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'page': page.toString(),
      'size': size.toString(),
    }..removeWhere((key, value) => value == null);

    final uri =
        Uri.parse('$baseUrl/user').replace(queryParameters: queryParameters);

    final response = await httpClient.get(
      uri,
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    if (response.statusCode == 200) {
      List<dynamic> jsonData = json.decode(response.body);
      return jsonData.map((e) => MessageResponse.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load messages');
    }
  }

  Future<List<MessageResponse>> getMessagesForAdmin({
    String? type,
    String? senderId,
    String? userIdReceiver,
    int? eleveId,
    DateTime? startDate,
    DateTime? endDate,
    String? role,
    String? envoyerRecus,
    int page = 0,
    int size = 10,
  }) async {
    final queryParameters = {
      'type': type,
      'senderId': senderId,
      'userIdReceiver': userIdReceiver,
      'eleveId': eleveId?.toString(),
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'role': role?.toString(),
      'envoyerRecus': envoyerRecus?.toString(),
      'page': page.toString(),
      'size': size.toString(),
    }..removeWhere((key, value) => value == null);

    final uri =
        Uri.parse('$baseUrl/admin').replace(queryParameters: queryParameters);

    final response = await httpClient.get(
      uri,
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    if (response.statusCode == 200) {
      List<dynamic> jsonData = json.decode(response.body);
      return jsonData.map((e) => MessageResponse.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load messages');
    }
  }

  Future<List<MessageResponse>> getMessagesForParent({
    String? type,
    String? senderId,
    String? userIdReceiver,
    int? eleveId,
    DateTime? startDate,
    DateTime? endDate,
    int page = 0,
    int size = 10,
  }) async {
    final queryParameters = {
      'type': type,
      'senderId': senderId,
      'userIdReceiver': userIdReceiver,
      'eleveId': eleveId?.toString(),
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'page': page.toString(),
      'size': size.toString(),
    }..removeWhere((key, value) => value == null);

    final uri =
        Uri.parse('$baseUrl/parent').replace(queryParameters: queryParameters);

    final response = await httpClient.get(
      uri,
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    if (response.statusCode == 200) {
      List<dynamic> jsonData = json.decode(response.body);
      return jsonData.map((e) => MessageResponse.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load messages');
    }
  }

  Future<List<MessageResponse>> fetchMessagesForParent({
    required String type,
    required int page,
    required int pageSize,
    bool isSender = false,
    bool isReceiver = false,
  }) async {
    try {
      final userId = await auth.getUserId();
      final eleveId = isReceiver ? await auth.getEleveId() : null;
      List<MessageResponse> fetchedMessages = [];

      if (isSender) {
        fetchedMessages = await getMessagesForParent(
          type: type,
          senderId: userId,
          page: page,
          size: pageSize,
        );
      } else if (isReceiver) {
        fetchedMessages = await getMessagesForParent(
          type: type,
          eleveId: eleveId,
          userIdReceiver: userId,
          page: page,
          size: pageSize,
        );
      }

      fetchedMessages.sort((a, b) =>
          b.messageEntity.dateEnvoie.compareTo(a.messageEntity.dateEnvoie));
      for (var message in fetchedMessages) {
        print('Message ID: ${message.messageEntity.idMessage}');
        print('objet: ${message.messageEntity.objet}');
        print('body: ${message.messageEntity.body}');
        print('Date: ${message.messageEntity.dateEnvoie}');
      }
      return fetchedMessages;
    } catch (e) {
      print('Failed to fetch $type messages: $e');
      return [];
    }
  }

  Future<List<MessageResponse>> fetchMessages({
    required String type,
    required int page,
    required int pageSize,
    bool isSender = false,
    bool isReceiver = false,
  }) async {
    try {
      final userId = await auth.getUserId();
      final eleveId = isReceiver ? await auth.getEleveId() : null;
      List<MessageResponse> fetchedMessages = [];

      if (isSender) {
        // Fetch messages where the user is the sender.
        fetchedMessages = await getMessages(
          type: type,
          senderId: userId,
          page: page,
          size: pageSize,
        );
      } else if (isReceiver) {
        // Fetch messages where the user is the receiver.
        fetchedMessages = await getMessages(
          type: type,
          eleveId: eleveId,
          userIdReceiver: userId,
          page: page,
          size: pageSize,
        );
      }

      fetchedMessages.sort((a, b) =>
          b.messageEntity.dateEnvoie.compareTo(a.messageEntity.dateEnvoie));
      return fetchedMessages;
    } catch (e) {
      print('Failed to fetch $type messages: $e');
      return [];
    }
  }

  Future<List<MessageResponse>> fetchMessagesForAdmin({
    required String type,
    required int page,
    required int pageSize,
    bool isSender = false,
    bool isReceiver = false,
  }) async {
    try {
      final userId = await auth.getUserId();
      final eleveId = isReceiver ? await auth.getEleveId() : null;
      List<MessageResponse> fetchedMessages = [];

      if (isSender) {
        // Fetch messages where the user is the sender.
        fetchedMessages = await getMessagesForAdmin(
          type: type,
          senderId: null,
          role: 'Admin',
          envoyerRecus: 'envoyer',
          page: page,
          size: pageSize,
        );
      } else if (isReceiver) {
        // Fetch messages where the user is the receiver.
        fetchedMessages = await getMessagesForAdmin(
          type: type,
          eleveId: null,
          userIdReceiver: null,
          role: 'Admin',
          envoyerRecus: 'recus',
          page: page,
          size: pageSize,
        );
      }

      fetchedMessages.sort((a, b) =>
          b.messageEntity.dateEnvoie.compareTo(a.messageEntity.dateEnvoie));
      return fetchedMessages;
    } catch (e) {
      print('Failed to fetch $type messages: $e');
      return [];
    }
  }

  Future<GetNumberOfNotRead> getUnreadCount({
    required String idParentKeycloak,
    required int idEleve,
  }) async {
    try {
      final response = await httpClient.get(
        Uri.parse(
            '$baseURL/messages/not-read/count?idParentKeycloak=$idParentKeycloak&idEleve=$idEleve'),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return GetNumberOfNotRead.fromJson(jsonResponse);
      } else {
        throw Exception('Failed to load unread counts');
      }
    } catch (e) {
      print('Error fetching unread counts: $e');
      throw Exception('Failed to load unread counts');
    }
  }

  Future<List<FileMessage>> getFilesByMessageIdOrRepondreAIdMessage(
      int idmsg) async {
    final url = Uri.parse('$baseUrl/$idmsg/filesMessage');
    print("Making GET request to: $url");

    final response = await httpClient.get(
      url,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    print("Response status: ${response.statusCode}");
    print("Response body: ${response.body}");

    if (response.statusCode == 200) {
      List<dynamic> jsonData = json.decode(response.body);
      print("Parsed response: $jsonData");
      return jsonData.map((e) => FileMessage.fromJson(e)).toList();
    } else {
      print("Failed to fetch files. Status: ${response.statusCode}");
      throw Exception('Failed to load files');
    }
  }

  Future<void> readMessage(Map<String, dynamic> readMessageDTO) async {
    final url = Uri.parse('$baseUrl/read/message');
    try {
      // Create the request
      final response = await httpClient.put(
        url,
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: json.encode(readMessageDTO),
      );

      // Handle response
      if (response.statusCode == 200) {
        print('Message read successfully');
      } else if (response.statusCode == 404) {
        print('Message not found');
      } else {
        print('Failed to read message: ${response.statusCode}');
        print('Response body: ${response.body}');
      }
    } catch (e) {
      print('An error occurred while reading the message: $e');
    }
  }

  Future<void> markMessageAsRead(MessageResponse message) async {
    try {
      final userId = await auth.getUserId();
      final eleveId = await auth.getEleveId();
      final prefs = await SharedPreferences.getInstance();
      final receiverUsername = prefs.get('firstname');
      final receiverUserSurname = prefs.get('lastname');
      final name = prefs.get('name');

      final readMessageDTO = {
        'messageEntity': {
          'idMessage': message.messageEntity.idMessage,
        },
        'idReceiverUser': userId,
        'receiverUsername': receiverUsername ?? '',
        'receiverUserSurname': name ?? '',
        'receiverUserCIN': '',
        'receiverUserRole': '',
      };

      // Call the readMessage API
      await readMessage(readMessageDTO);
    } catch (e) {
      print('Failed to mark message as read: $e');
    }
  }

  Future<void> addMessage(
      BuildContext context,
      Map<String, dynamic> addCoursDTO,
      List<Map<String, dynamic>> selectedFiles) async {
    await createMessage(context, addCoursDTO, selectedFiles);
  }

  Future<void> changeConfirmation(int idMsg) async {
    final url = Uri.parse('$baseUrl/changerConfirmation/$idMsg');
    try {
      final response = await httpClient.put(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        print('Message confirmed successfully.');
      } else if (response.statusCode == 404) {
        print('Message not found.');
      } else {
        print('Failed to confirm message: ${response.statusCode}');
      }
    } catch (e) {
      print('Error changing confirmation: $e');
    }
  }

  Future<void> confirmAndExecute(BuildContext context, String message,
      Future<void> Function() action) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Confirmation'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Annuler'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('Confirmer'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        await action();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Validation avec succès'),
          ),
        );
      } catch (e) {
        print('Failed to execute action: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Échec de Validation'),
          ),
        );
      }
    }
  }

  Future<void> deleteMessage(int idMsg) async {
    final url = Uri.parse('$baseUrl/SupprimerMsg/$idMsg');
    final token = await auth.getToken();
    try {
      final response = await http.put(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        // Confirmation changed successfully
        print('Message deleted successfully.');
      } else if (response.statusCode == 404) {
        // Handle message not found
        print('Message not found.');
      } else {
        // Handle other status codes
        print('Failed to deleted message: ${response.statusCode}');
      }
    } catch (e) {
      // Handle exceptions (e.g., network issues)
      print('Error deleting message: $e');
    }
  }

  Future<void> deleteMessageAndExecute(BuildContext context, String message,
      Future<void> Function() action) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Confirmation'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Annuler'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text('Confirmer'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        await action();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Suppression validée avec succès'),
          ),
        );
      } catch (e) {
        print('Failed to execute action: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Échec de Suppression'),
          ),
        );
      }
    }
  }

  Future<List<MessageresponseDTO>> getMessagesByIdOrSender(
      {required int idMessage, String? idSender}) async {
    final url = Uri.parse(
        '$baseUrl/$idMessage/messageDetails${idSender != null ? '?idSender=$idSender' : ''}');

    final response = await httpClient.get(
      url,
    );

    if (response.statusCode == 200) {
      List<dynamic> responseData = jsonDecode(response.body);
      return responseData
          .map((json) => MessageresponseDTO.fromJson(json))
          .toList();
    } else {
      throw Exception('Failed to load message details');
    }
  }

  Future<void> navigateToMessageDetail(
      BuildContext context, MessageResponse message, String messageType) async {
    MessageServices messageService = MessageServices();
    final senderId = await auth.getUserId();

    List<MessageresponseDTO> detailedMessages =
        await messageService.getMessagesByIdOrSender(
      idMessage: message.messageEntity.idMessage,
      idSender: senderId,
    );

    if (detailedMessages.isNotEmpty) {
      print('Detailed messages fetched:');
      for (var detail in detailedMessages) {
        print('Message ID: ${detail.idMessage}');
        print('objet: ${detail.objet}');
        print('body: ${detail.body}');
        print('Date Sent: ${detail.dateEnvoie}');
      }
      Widget detailPage;

      switch (messageType) {
        case "Cours":
          detailPage = CoursDetailPage(coursList: detailedMessages);
          break;
        case "Exercices":
          detailPage = ExerciceDetailPage(exercicesList: detailedMessages);
          break;
        case "Observation":
          detailPage = ObservationDetails(observationsList: detailedMessages);
          break;
        case "Discipline":
          detailPage = DisciplineDetails(disciplineList: detailedMessages);
          break;
        default:
          print('No details found for this message type');
          return;
      }

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => detailPage,
        ),
      );
    } else {
      print('No details found for this message');
    }
  }
}
