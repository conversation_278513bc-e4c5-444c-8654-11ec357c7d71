/*import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SecondHeadBar1 extends StatelessWidget {

  final String title;
  final IconData icon;

  const SecondHeadBar1({
    Key? key,
    required this.title,
    required this.icon ,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        boxShadow: [
          BoxShadow(
            color: Colors.white70.withOpacity(0.15),
            blurRadius: 12,
            offset: Offset(0, 4),
          ),
        ],
      ),

    child: Row(
        children: [
          Tooltip(
            message: 'Retour',
            child: IconButton(
              icon: Icon(Icons.arrow_back_ios, color: Colors.blueGrey),
              onPressed: () => Get.back(),
              //padding: EdgeInsets.zero, // Réduit l'espace autour de l'icône
              constraints: BoxConstraints(), // Supprime les contraintes par défaut
            ),
          ),
          Icon(icon, color: Colors.blue, size: 28),
          SizedBox(width: 8),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize:25,

              fontWeight: FontWeight.w500,
              color: Colors.blue.shade800,
            ),
          ),
        ],
      ),
    );
  }
}
*/
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SecondHeadBar1 extends StatelessWidget {
  final String title;
  final IconData icon;

  const SecondHeadBar1({
    Key? key,
    required this.title,
    required this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft, // Aligner le contenu à droite
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min, // Prendre le moins d'espace possible
        mainAxisAlignment: MainAxisAlignment.end, // Aligner à droite dans le Row
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Tooltip(
            message: 'Retour',
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.blueGrey),
              onPressed: () => Get.back(),
              padding: EdgeInsets.zero, // Supprime les marges internes
              constraints: const BoxConstraints(), // Supprime les contraintes par défaut
            ),
          ),
          const SizedBox(width: 4), // Espacement minimal entre icône retour et icône carnet
          Icon(icon, color: Colors.blue, size: 24),
          const SizedBox(width: 4), // Espacement minimal entre icône carnet et titre
          Text(
            title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: Colors.blue.shade800,
            ),
          ),
        ],
      ),
    );
  }
}
