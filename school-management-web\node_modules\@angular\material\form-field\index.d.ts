export { a as MatFormFieldModule, M as <PERSON><PERSON><PERSON><PERSON> } from '../module.d-1ZCYe5BH.js';
export { F as FloatLabelType, e as MAT_ERROR, j as MAT_FORM_FIELD, k as MAT_FORM_FIELD_DEFAULT_OPTIONS, f as MAT_PREFIX, g as MAT_SUFFIX, b as <PERSON><PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>ield, h as MatForm<PERSON>ieldAppearance, i as MatFormFieldDefaultOptions, a as MatHint, c as MatPrefix, d as MatSuffix, S as SubscriptSizing } from '../form-field.d-CMA_QQ0R.js';
export { M as MatFormFieldControl } from '../form-field-control.d-QxD-9xJ3.js';
import '@angular/core';
import '../common-module.d-C8xzHJDr.js';
import '@angular/cdk/bidi';
import '@angular/cdk/observers';
import '@angular/cdk/coercion';
import '@angular/forms';
import '../palette.d-BSSFKjO6.js';
import 'rxjs';

/** @docs-private */
declare function getMatFormFieldPlaceholderConflictError(): Error;
/** @docs-private */
declare function getMatFormFieldDuplicatedHintError(align: string): Error;
/** @docs-private */
declare function getMatFormFieldMissingControlError(): Error;

/**
 * Animations used by the MatFormField.
 * @docs-private
 * @deprecated No longer used, will be removed.
 * @breaking-change 21.0.0
 */
declare const matFormFieldAnimations: {
    readonly transitionMessages: any;
};

export { getMatFormFieldDuplicatedHintError, getMatFormFieldMissingControlError, getMatFormFieldPlaceholderConflictError, matFormFieldAnimations };
