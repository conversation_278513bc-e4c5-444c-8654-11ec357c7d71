@use 'sass:meta';
@use 'sass:color';
@use '../../../theming/inspection';
@use '../../token-definition';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, snackbar);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // Sets the snack bar border radius.
    container-shape: 4px,
    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    // Removed to match the previous appearance.
    supporting-text-tracking: null,
    // Excluded because they target the wrong DOM node. See the
    // comments on the elevation of `.mat-mdc-snack-bar-container`.
    container-elevation: null,
    container-shadow-color: null,
    action-focus-label-text-color: null,
    action-focus-state-layer-color: null,
    action-focus-state-layer-opacity: null,
    action-hover-label-text-color: null,
    action-hover-state-layer-color: null,
    action-hover-state-layer-opacity: null,
    action-label-text-color: null,
    action-label-text-font: null,
    action-label-text-size: null,
    action-label-text-tracking: null,
    action-label-text-weight: null,
    action-pressed-label-text-color: null,
    action-pressed-state-layer-color: null,
    action-pressed-state-layer-opacity: null,
    icon-color: null,
    icon-focus-icon-color: null,
    icon-focus-state-layer-color: null,
    icon-focus-state-layer-opacity: null,
    icon-hover-icon-color: null,
    icon-hover-state-layer-color: null,
    icon-hover-state-layer-opacity: null,
    icon-pressed-icon-color: null,
    icon-pressed-state-layer-color: null,
    icon-pressed-state-layer-opacity: null,
    icon-size: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $surface: inspection.get-theme-color($theme, background, card);

  @return (
    container-color: if(meta.type-of($surface) == color,
      color.mix(if($is-dark, #fff, #000), $surface, 80%), $surface),
    supporting-text-color: if(meta.type-of($surface) == color, rgba($surface, 0.87), $surface)
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    supporting-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    supporting-text-line-height: inspection.get-theme-typography($theme, body-2, line-height),
    supporting-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    supporting-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
