import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:NovaSchool/features/teacher_app/Profil/enseignant_class.dart';
import 'package:shared_preferences/shared_preferences.dart';
//import '../../../commun/base_url.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnseignantService {
  Future<List<Enseignant>> getAllEnseignants() async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/enseignant/all';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String token = prefs.getString('token') ?? '';
    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 200) {
        List<dynamic> responseData = json.decode(response.body);
        List<Enseignant> enseignants =
            responseData.map((data) => Enseignant.fromJson(data)).toList();
        return enseignants;
      } else {
        throw Exception(
            'Failed to get all enseignants: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get all enseignants: $e');
    }
  }

  Future<void> addEnseignant(Enseignant enseignant) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/enseignant/add';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String token = prefs.getString('token') ?? '';

    try {
      var request = http.MultipartRequest('POST', Uri.parse(apiUrl));
      request.headers['Authorization'] = 'Bearer $token';

      // Add EnseignantEntity as a JSON string part
      request.fields['EnseignantEntity'] = jsonEncode(enseignant.toJson());

      // Send the request
      var response = await request.send();

      if (response.statusCode == 200) {
        print('Enseignant added successfully.');
      } else {
        throw Exception('Failed to add enseignant: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to add enseignant: $e');
    }
  }
}
