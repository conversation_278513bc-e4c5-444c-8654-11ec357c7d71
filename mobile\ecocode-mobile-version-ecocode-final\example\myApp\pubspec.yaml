name: NovaSchool
description: "A new Flutter project."

publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=2.12.0 <3.0.0'

#-----------Packages-----------#
dependencies:
  flutter:
    sdk: flutter

  # Utility Packages
  intl: ^0.19.0
  logger: ^2.0.2+1
  url_launcher: ^6.2.5
  smooth_page_indicator: ^1.1.0
  google_nav_bar: ^5.0.6
  bottom_bar: ^2.0.3
  curved_navigation_bar: ^1.0.3
  google_fonts: ^4.0.4
  shared_preferences: ^2.2.2
  localstorage: ^4.0.0
  file_picker: ^8.0.0+1
  keycloak_flutter: ^0.0.21
  dart_jsonwebtoken: ^2.14.0
  jwt_decode: ^0.3.1
  http: 0.13.6
  dialog_flowtter: ^0.3.3
  multi_select_flutter: ^4.1.3
  flutter_appauth: 6.0.6
  provider: ^6.1.2
  openid_client:
    path: ../..


  # Icons
  iconsax_flutter: ^1.0.0
  cupertino_icons: ^1.0.6




  # State Management
  get: ^4.6.6
  get_storage: ^2.1.1
  mime: ^1.0.5
  universal_io: ^2.2.2
  universal_html: ^2.2.4
  permission_handler: ^11.3.1
  encrypt: ^5.0.3
  path_provider: ^2.1.4 
  device_info_plus: ^10.1.2
  open_file: ^3.5.6
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3
  flutter_local_notifications: ^15.1.1
  http_interceptor: ^1.0.2
  flutter_dotenv: ^5.2.1
  photo_view: ^0.15.0
  package_info_plus: ^8.1.1

 

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  # Local Assets
  assets:
    - assets/icons/
    - assets/images/
    - assets/logos/
    - assets/images/onBoardingImages/
    - assets/images/HomeImages/
    - json/
    - assets/dialog_flow_auth.json
    - assets/icons/admin_icons/
    - .env.local
    - .env.test
    - .env.demo
    - .env.ichrak
    - .env.horizon
    - .env.amana
    - .env.nova
    - .env.loujayn
    - .env.jjr
    - .env.essedik
    - .env.pinacle
    - .env.excellence
    - .env.tacapes
    - .env.eppm
    - .env.anouar
    - .env.amine

  # Local Fonts
  fonts:
    - family: Coolvetica
      fonts:
        - asset: assets/fonts/coolvetica_rg_it.otf
          style: italic
          weight: 400
        - asset: assets/fonts/coolvetica_rg.otf
          weight: 400
    - family: Nisebuschgardens
      fonts:
        - asset: assets/fonts/NiseBuschGardens.ttf
    - family: Cookie-Regular
      fonts:
        - asset: assets/fonts/Cookie-Regular.ttf
    - family: DMSerifDisplay
      fonts:
        - asset: assets/fonts/DMSerifDisplay-Italic.ttf


