import 'dart:convert';

import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/models/historiquePaiementEleve.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/tokenInterceptor.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class PaiementService {
  // String baseURL = dotenv.get('baseURL');
  // final String baseUrl = "$baseURL/historiquepaiementEleve";
  late final String baseURL;
  late final String baseUrl;
  final auth = AuthService();
  final httpClient =
      InterceptedClient.build(interceptors: [TokenInterceptor()]);


  PaiementService() {
    baseURL = dotenv.get('baseURL');
    baseUrl = "$baseURL/historiquepaiementEleve";
  }

  Future<Map<String, dynamic>?> getPaiementByIdElevePage(
      {required int eleveId,
      int page = 0,
      int size = 10,
      // String sortBy = "datePaiementEleve",
      // String sortDirection = "DESC"
      }) async {

//  String baseURL = dotenv.get('baseURL');
//   final String baseUrl = "$baseURL/historiquepaiementEleve";
//   final auth = AuthService();
//   final httpClient =
//       InterceptedClient.build(interceptors: [TokenInterceptor()]);

// String baseURL = dotenv.get('baseURL');
//     final String baseUrl = "$baseURL/historiquepaiementEleve";

    final url = Uri.parse(
        '$baseUrl/eleve/$eleveId/page?page=$page&size=$size');

    try {
      final response = await httpClient.get(url);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);

        List<dynamic> content = jsonData['content'];
        int totalPages = jsonData['totalPages'];
        int totalElements = jsonData['totalElements'];
        List<HistoriquePaiementEleveDTO> paiements = content
            .map((articleJson) => HistoriquePaiementEleveDTO.fromJson(articleJson))
            .toList();

        return {
          'paiements': paiements,
          'totalPages': totalPages,
          'totalElements': totalElements
        };
      } else {
        print('Failed to load paiements. Status code: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error occurred while fetching paiements: $e');
      return null;
    }
  }
}
