class EleveInfoPersoDTO {
  final int idEleve;
  final String nomEleve;
  final String prenomEleve; 
  final String nomArabeEleve; 
  final String prenomArabeEleve; 
  final DateTime? dateNaissanceEleve;
  final String lieuEleve; 
  final String sexeEleve; 
  final String adresseEleve; 
  final String classScolaireEleve; 
  final String paysEleve; 
  final String matriculeEleve; 
  final String identifiantUnique; 
  final String niveauClasse;
  final String nomClasse; 

  EleveInfoPersoDTO({
    required this.idEleve,
    required this.nomEleve,
    required this.prenomEleve,
    required this.nomArabeEleve,
    required this.prenomArabeEleve,
    this.dateNaissanceEleve,
    required this.lieuEleve,
    required this.sexeEleve,
    required this.adresseEleve,
    required this.classScolaireEleve,
    required this.paysEleve,
    required this.matriculeEleve,
    required this.identifiantUnique,
    required this.niveauClasse,
    required this.nomClasse,
  });

  factory EleveInfoPersoDTO.fromJson(Map<String, dynamic> json) {
    final dateNaissanceString = json['dateNaissanceEleve'] as String?;
    return EleveInfoPersoDTO(
      idEleve: json['idEleve'] ?? 0,
      nomEleve: json['nomEleve'] ?? '',
      prenomEleve: json['prenomEleve'] ?? '',
      nomArabeEleve: json['nomArabeEleve'] ?? '',
      prenomArabeEleve: json['prenomArabeEleve'] ?? '',
      dateNaissanceEleve: dateNaissanceString != null && dateNaissanceString.isNotEmpty
          ? DateTime.tryParse(dateNaissanceString)
          : null, // Null si vide ou non valide
      lieuEleve: json['lieuEleve'] ?? '',
      sexeEleve: json['sexeEleve'] ?? '',
      adresseEleve: json['adresseEleve'] ?? '',
      classScolaireEleve: json['classScolaireEleve'] ?? '',
      paysEleve: json['paysEleve'] ?? '',
      matriculeEleve: json['matriculeEleve'] ?? '',
      identifiantUnique: json['identifiantUnique'] ?? '',
      niveauClasse: json['niveauClasse'] ?? '',
      nomClasse: json['nomClasse'] ?? '',
    );
  }
}
