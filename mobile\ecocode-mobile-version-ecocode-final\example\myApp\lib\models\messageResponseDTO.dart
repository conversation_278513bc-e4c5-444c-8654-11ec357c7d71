class MessageresponseDTO {
  final int idMessage;
  final String? senderId; // Make these fields nullable
  final String? senderUsername;
  final String? senderUserSurname;
  final String? type;
  final String? objet;
  final String? body;
  final bool readStatus;
  final bool confirmed;
  final bool existFile;
  final int numberOfFile;
  final DateTime dateEnvoie;
  final DateTime? dateCorrectionExercice;
  final MessageresponseDTO? repondreA;
  final bool archived;

  MessageresponseDTO({
    required this.idMessage,
    this.senderId, // Now nullable
    this.senderUsername,
    this.senderUserSurname,
    this.type,
    this.objet,
    this.body,
    required this.readStatus,
    required this.confirmed,
    required this.existFile,
    required this.numberOfFile,
    required this.dateEnvoie,
    this.dateCorrectionExercice,
    this.repondreA,
    required this.archived,
  });

  factory MessageresponseDTO.fromJson(Map<String, dynamic> json) {
    return MessageresponseDTO(
      idMessage: json['idMessage'],
      senderId: json['senderId'] ?? '', // Provide a fallback value
      senderUsername: json['senderUsername'] ?? '',
      senderUserSurname: json['senderUserSurname'] ?? '',
      type: json['type'] ?? '',
      objet: json['objet'] ?? '',
      body: json['body'] ?? '',
      readStatus: json['readStatus'] ?? false,
      confirmed: json['confirmed'] ?? false,
      existFile: json['existFile'] ?? false,
      numberOfFile: json['numberOfFile'] is int
          ? json['numberOfFile']
          : (json['numberOfFile'] as num).toInt(),
      dateEnvoie: DateTime.parse(json['dateEnvoie']),
      dateCorrectionExercice: json['dateCorrectionExercice'] != null
          ? DateTime.parse(json['dateCorrectionExercice'])
          : null,
      repondreA: json['repondreA'] != null
          ? MessageresponseDTO.fromJson(json['repondreA'])
          : null,
      archived: json['archived'] ?? false,
    );
  }
}
