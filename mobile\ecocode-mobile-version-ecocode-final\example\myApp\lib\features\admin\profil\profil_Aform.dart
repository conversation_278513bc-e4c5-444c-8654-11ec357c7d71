// AProfilForm.dart

import 'package:flutter/material.dart';
import '../../../../utils/constants/colors.dart';
import '../../parent_app/profil/student_form.dart';
import 'admin_class.dart'; // Importez la classe Admin

class AProfilForm extends StatelessWidget {
  final String firstName;
  final String lastName;
  final String email;
  final String profile;

  const AProfilForm({
    Key? key,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.profile,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15 , vertical: 5),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: AppColors.light,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                spreadRadius: 0,
                blurRadius: 5,
                offset: Offset(0, 1),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                buildRowWithIconAndText(Icons.person, "Nom", firstName),
                buildSeparator(),
                buildRowWithIconAndText(Icons.mail_outline, "Email", email ),
                buildSeparator(),
                buildRowWithIconAndText(Icons.person, "Profile", profile),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
