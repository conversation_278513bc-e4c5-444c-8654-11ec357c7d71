class ObservationData {
  final String title;
  final String description;
  final String date;
  final String fichier;
  bool showDetails;

  ObservationData({
    required this.title,
    required this.description,
    required this.date,
    required this.fichier,
    this.showDetails = false,
  });

  factory ObservationData.fromJson(Map<String, dynamic> json) {
    return ObservationData(
      title: json['title'],
      description: json['description'],
      date: json['date'],
      fichier: json['fichier'],
    );
  }
}