import 'package:flutter/material.dart';
import 'package:dialog_flowtter/dialog_flowtter.dart';

class ChatbotScreen extends StatefulWidget {
  const ChatbotScreen({Key? key}) : super(key: key);

  @override
  _ChatbotScreenState createState() => _ChatbotScreenState();
}

class _ChatbotScreenState extends State<ChatbotScreen> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController _textController = TextEditingController();
  List<Map<String, dynamic>> messages = [];

  late DialogFlowtter dialogFlowtter;

  @override
  void initState() {
    super.initState();
    initializeDialogFlowtter();
  }

  void initializeDialogFlowtter() async {
    try {
      dialogFlowtter = await DialogFlowtter.fromFile(path: "assets/dialog_flow_auth.json");
    } catch (e) {
      print("Error initializing DialogFlowtter: $e");
      // Handle the error here
    }
  }

  @override
  Widget build(BuildContext context) {
    final EdgeInsets padding = MediaQuery.of(context).viewInsets;

    return AnimatedPadding(
      padding: padding,
      duration: const Duration(milliseconds: 300),
      curve: Curves.decelerate,
      child: Container(
        height: 600,
        margin: const EdgeInsets.symmetric(horizontal: 14),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.94),
          borderRadius: const BorderRadius.all(Radius.circular(40)),
        ),
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: Colors.transparent,
          body: Column(
            children: [
              Expanded(
                child: ListView.builder(
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final String text = _getMessageText(message);
                    final bool isUserMessage = message['isUserMessage'] ?? false;
                    return ListTile(
                      title: Text(
                        text,
                        textAlign: isUserMessage ? TextAlign.right : TextAlign.left,
                      ),
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(15.0),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _textController,
                        decoration: InputDecoration(
                          hintText: 'Enter your message...',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.send),
                      onPressed: () {
                        sendMessage(_textController.text);
                        _textController.clear();
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getMessageText(Map<String, dynamic> message) {
    if (message['message'] is Message) {
      final messageText = (message['message'] as Message).text?.text;
      return messageText != null && messageText.isNotEmpty ? messageText[0] : '';
    } else {
      return message['message']?.toString() ?? '';
    }
  }

  void sendMessage(String text) async {
    if (text.isEmpty) {
      print('Message is empty');
      return;
    }

    setState(() {
      addMessage(text, true);
    });

    try {
      DetectIntentResponse response = await dialogFlowtter.detectIntent(
        queryInput: QueryInput(
          text: TextInput(
            text: text,
            languageCode: 'fr', // Set the language to French
          ),
        ),
      );

      if (response.message != null) {
        setState(() {
          addMessage(response.message!, false);
        });
      }
    } catch (e) {
      print("Error sending message: $e");
      // Handle the error here
    }
  }

  void addMessage(dynamic message, [bool isUserMessage = false]) {
    setState(() {
      messages.add({'message': message, 'isUserMessage': isUserMessage});
    });
  }
}
