//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'docked-container-color': map.get($deps, 'md-sys-color', 'surface'),
    'docked-container-shape':
      map.get($deps, 'md-sys-shape', 'corner-extra-large-top'),
    'docked-container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'docked-drag-handle-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'docked-drag-handle-height': if($exclude-hardcoded-values, null, 4px),
    'docked-drag-handle-opacity': if($exclude-hardcoded-values, null, 0.4),
    'docked-drag-handle-width': if($exclude-hardcoded-values, null, 32px),
    'docked-minimized-container-shape':
      map.get($deps, 'md-sys-shape', 'corner-none'),
    'docked-modal-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'docked-standard-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1')
  );
}
