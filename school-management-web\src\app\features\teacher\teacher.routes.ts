import { Routes } from '@angular/router';

export const teacherRoutes: Routes = [
  {
    path: '',
    loadComponent: () => import('./layout/teacher-layout.component').then(m => m.TeacherLayoutComponent),
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./dashboard/teacher-dashboard.component').then(m => m.TeacherDashboardComponent)
      },
      {
        path: 'classes',
        loadComponent: () => import('./classes/teacher-classes.component').then(m => m.TeacherClassesComponent)
      },
      {
        path: 'courses',
        loadComponent: () => import('./courses/teacher-courses.component').then(m => m.TeacherCoursesComponent)
      },
      {
        path: 'exercises',
        loadComponent: () => import('./exercises/teacher-exercises.component').then(m => m.TeacherExercisesComponent)
      },
      {
        path: 'grades',
        loadComponent: () => import('./grades/teacher-grades.component').then(m => m.TeacherGradesComponent)
      },
      {
        path: 'attendance',
        loadComponent: () => import('./attendance/teacher-attendance.component').then(m => m.TeacherAttendanceComponent)
      },
      {
        path: 'observations',
        loadComponent: () => import('./observations/teacher-observations.component').then(m => m.TeacherObservationsComponent)
      },
      {
        path: 'schedule',
        loadComponent: () => import('./schedule/teacher-schedule.component').then(m => m.TeacherScheduleComponent)
      },
      {
        path: 'messages',
        loadComponent: () => import('./messages/teacher-messages.component').then(m => m.TeacherMessagesComponent)
      },
      {
        path: 'profile',
        loadComponent: () => import('./profile/teacher-profile.component').then(m => m.TeacherProfileComponent)
      }
    ]
  }
];
