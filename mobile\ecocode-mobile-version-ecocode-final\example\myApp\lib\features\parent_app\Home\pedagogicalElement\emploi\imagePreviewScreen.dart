import 'dart:convert';
import 'dart:typed_data';

import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class ImagePreviewScreen extends StatelessWidget {
  final Uint8List imageBytes;
  ImagePreviewScreen({required this.imageBytes});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AppBackground(
        child: SingleChildScrollView(
          child: Column(
            children: [
              PrimaryHeaderContainer(
                child: Container(
                  height: 100,
                  child: SecondHeadBar(
                    title: 'Emploi',
                    titleColor: Colors.white,
                    iconColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Title in Arabic above the emploi section
               Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Center(
                child: Text(
                  "جدول الأوقات", // Arabic title for "Emploi du temps"
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Container(
                  height: 300,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 2,
                        blurRadius: 5,
                        offset: Offset(0, 3),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: imageBytes.isNotEmpty
                        ? PhotoView(
                            imageProvider: MemoryImage(imageBytes),
                            backgroundDecoration:
                                BoxDecoration(color: Colors.white),
                            minScale: PhotoViewComputedScale.contained,
                            maxScale: PhotoViewComputedScale.covered * 2,
                          )
                        : Center(
                            child: Text(
                              "Emploi n'est pas disponible pour le moment",
                              style: TextStyle(
                                fontSize: 20,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
