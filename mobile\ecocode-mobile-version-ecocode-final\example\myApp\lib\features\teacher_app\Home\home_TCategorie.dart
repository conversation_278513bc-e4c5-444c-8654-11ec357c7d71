import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class THomeCategories extends StatelessWidget {
  final VoidCallback onPressed;
  final String? iconimage;
  final IconData? iconData;
  final String title;
  final int unreadCount;

  const THomeCategories({
    Key? key,
    required this.onPressed,
    this.iconimage,
    this.iconData,
    required this.title,
    this.unreadCount = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        margin: const EdgeInsets.only(bottom: 30),
        width: MediaQuery.of(context).size.width / 2.5,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.5),
              spreadRadius: 0,
              blurRadius: 5,
              offset: const Offset(2, 4),
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                iconimage != null
                    ? Image.asset(iconimage!, height: 60, width: 60)
                    : Icon(iconData, size: 55, color: const Color(0xFF0993D7)),
                const SizedBox(height: 8),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF1B2F38),
                  ),
                ),
              ],
            ),
            if (unreadCount > 0)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(5),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    '$unreadCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
/*import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class THomeCategories extends StatelessWidget {
  const THomeCategories({
    Key? key,
     this.iconimage,
    this.iconData,
    required this.title,
    required this.onPressed,
  }) : super(key: key);

  final String? iconimage;
  final IconData? iconData;
  final String title;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 10),
      child: GestureDetector(
        onTap: onPressed,
        child: Container(
          height: 100.0,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                spreadRadius: 0,
                blurRadius: 5,
                offset: Offset(0, 1),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(15.0),
            child: Row(
              children: [
                iconimage != null
                    ? Image(image: AssetImage(iconimage!), height: 45, width: 45)
                    : Icon(iconData, size: 45, color: Color(0xFF0993D7)),//Color(0xFF0ECDF1)),//Colors.lightBlue),
                /*Color(0xFF0679AB) bleu foncé
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.dmSerifDisplay(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1B2F38),
                  ),
                )*/
                /*Image(
                  image: AssetImage(iconimage),
                  width: 50.0,
                  height: 50.0,
                ),*/
                //SizedBox(width: 50.0),
                Expanded(
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.poppins(//dmSerifDisplay(
                      fontSize: 24,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.blueGrey,
                  size: 25,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
*/