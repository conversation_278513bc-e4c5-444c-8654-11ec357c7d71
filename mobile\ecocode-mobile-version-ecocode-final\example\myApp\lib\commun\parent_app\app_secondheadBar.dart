import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../utils/constants/colors.dart';
import '../../utils/constants/size.dart';

class SecondHeadBar extends StatelessWidget {
 const SecondHeadBar({
    Key? key,
    required this.title,
    this.titleColor =
        AppColors.dark, // New parameter for title color with a default value
    this.iconColor = Colors
        .black, // New parameter for icon color with a default value of white
  }) : super(key: key);

  final String title;
  final Color titleColor; // Variable for title color
  final Color iconColor; // Variable for icon color

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: AppSize.md, top: AppSize.xl),
      child: Column(
        children: [
          Row(
            children: [
              Tooltip(
                message: 'Retour',
                child: IconButton(
                  onPressed: () => Get.back(),
                  icon: Icon(Icons.arrow_back,
                      color: iconColor), // Set the icon color to white
                ),
              ),
              Text(
                title,
                style: GoogleFonts.dmSerifDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.w400,
                  color: titleColor, // Use the new titleColor parameter
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
