import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:NovaSchool/utils/constants/text.dart';

class AHomeWelcome extends StatelessWidget {
  final String firstName;
  final String lastName;

  const AHomeWelcome({
    Key? key,
    required this.firstName,
    required this.lastName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20.0, top: 20.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 90,
            height: 90,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: AssetImage(AppImages.Admin),
                fit: BoxFit.cover,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.5),
                  spreadRadius: 2,
                  blurRadius: 6,
                  offset: Offset(0, 3), // changes position of shadow
                ),
              ],
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  AppText.Hometitle,
                  style: GoogleFonts.dmSerifDisplay(
                    fontSize: 23,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 5),
                Text(
                  "$firstName",
                  style: GoogleFonts.dmSerifDisplay(
                    fontSize: 25,
                    fontStyle: FontStyle.italic, // 🔥 Ajout de l'italique
                    fontWeight: FontWeight.w800,
                    color: Colors.orange.shade200,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }





// final String firstName;
//   final String lastName;
//   final int configId = 1;
//   final ExtensionConfigService service = ExtensionConfigService();
//   final FileDownloadService fileDownloadService = FileDownloadService();

//   AHomeWelcome({
//     Key? key,
//     required this.firstName,
//     required this.lastName,
//   }) : super(key: key);

//   void _showSnackbar(BuildContext context, String message, Color color,
//       {String? actionLabel, VoidCallback? onPressed}) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         backgroundColor: color,
//         action: actionLabel != null
//             ? SnackBarAction(label: actionLabel, onPressed: onPressed ?? () {})
//             : null,
//       ),
//     );
//   }

//   Future<void> _downloadFile(BuildContext context, String url) async {
//     try {
//       fileDownloadService.showLoadingDialog(
//           context, 'Téléchargement en cours...');
//       // Use url_launcher to open the appPath in the browser for downloading
//       if (await canLaunch(url)) {
//         await launch(url);
//         _showSnackbar(
//           context,
//           'Téléchargement démarré. Vérifiez votre dossier Téléchargements.',
//           Colors.green,
//         );
//       } else {
//         _showSnackbar(
//           context,
//           'Impossible de lancer le téléchargement.',
//           Colors.red,
//         );
//       }
//     } catch (e) {
//       _showSnackbar(
//         context,
//         'Une erreur s\'est produite pendant le téléchargement.',
//         Colors.red,
//       );
//     } finally {
//       Navigator.of(context).pop(); // Close the loading dialog
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder<ExtensionConfig>(
//       future: service.getById(configId),
//       builder: (context, snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return Center(child: CircularProgressIndicator());
//         } else if (snapshot.hasError) {
//           return Center(child: Text('Erreur: ${snapshot.error}'));
//         } else if (!snapshot.hasData) {
//           return Center(child: Text('Configuration introuvable.'));
//         }

//         ExtensionConfig? config = snapshot.data;
//         return Padding(
//           padding: const EdgeInsets.only(left: 20.0, top: 35.0),
//           child: Row(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Container(
//                 width: 90,
//                 height: 90,
//                 decoration: BoxDecoration(
//                   shape: BoxShape.circle,
//                   image: DecorationImage(
//                     image: AssetImage(AppImages.Admin),
//                     fit: BoxFit.cover,
//                   ),
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.white.withOpacity(0.5),
//                       spreadRadius: 0,
//                       blurRadius: 4,
//                       offset: Offset(0, 1),
//                     ),
//                   ],
//                 ),
//               ),
//               SizedBox(width: 15),
//               Expanded(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Text(
//                       AppText.Hometitle,
//                       style: GoogleFonts.dmSerifDisplay(
//                         fontSize: 20,
//                         fontWeight: FontWeight.w500,
//                         color: Colors.white,
//                       ),
//                     ),
//                     SizedBox(height: 5),
//                     Text(
//                       firstName,
//                       style: GoogleFonts.dmSerifDisplay(
//                         fontSize: 18,
//                         fontWeight: FontWeight.w500,
//                         color: Colors.white,
//                       ),
//                     ),
//                     SizedBox(height: 20),
//                     if (config != null &&
//                         config.message != null &&
//                         config.message.isNotEmpty)
//                       GestureDetector(
//                         onTap: () => _downloadFile(context, config.path),
//                         child: Row(
//                           children: [
//                             if (config != null &&
//                                 config.path != null &&
//                                 config.path.isNotEmpty)
//                               Icon(
//                                 Icons.download,
//                                 color: Colors.white70,
//                                 size: 20,
//                               ),
//                             SizedBox(width: 8),
//                             Flexible(
//                               child: Text(
//                                 config.message,
//                                 style: GoogleFonts.dmSerifDisplay(
//                                   fontSize: 16,
//                                   fontWeight: FontWeight.w400,
//                                   color: Colors.white70,
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }
}

