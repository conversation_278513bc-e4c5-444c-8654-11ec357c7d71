import 'dart:convert';
import 'dart:typed_data';

class FileMessage {
  final int idMessage;
  final int idFile;
  final String fileName;
  final Uint8List file;

  FileMessage({
    required this.idMessage,
    required this.idFile,
    required this.fileName,
    required this.file,
  });

  factory FileMessage.fromJson(Map<String, dynamic> json) {
    return FileMessage(
      idMessage: json['idMessage'],
      idFile: json['idFile'],
      fileName: json['fileName'],
      file: base64Decode(json['file']), 
    );
  }
}
