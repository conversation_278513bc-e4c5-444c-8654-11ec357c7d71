@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, minimal-pseudo-checkbox);

/// Generates custom tokens for the minimal variant of mat-pseudo-checkbox.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the minimal variant of mat-pseudo-checkbox
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: ((
    selected-checkmark-color: map.get($systems, md-sys-color, primary),
    disabled-selected-checkmark-color: sass-utils.safe-color-change(
      map.get($systems, md-sys-color, on-surface), $alpha: 0.38),
  ), (
    // Color variants:
    primary: (), // Default, no overrides needed.
    secondary: (
      selected-checkmark-color: map.get($systems, md-sys-color, secondary),
    ),
    tertiary: (
      selected-checkmark-color: map.get($systems, md-sys-color, tertiary),
    ),
    error: (
      selected-checkmark-color: map.get($systems, md-sys-color, error),
    )
  ));

  @return token-definition.namespace-tokens($prefix, $tokens, $token-slots);
}
