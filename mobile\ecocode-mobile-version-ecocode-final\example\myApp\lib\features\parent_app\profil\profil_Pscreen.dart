import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/features/parent_app/Home/home_PHeadBar.dart';
import 'package:NovaSchool/models/eleveResponseDTO.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/features/parent_app/profil/profil_Pform.dart';
import 'package:NovaSchool/features/parent_app/profil/profile_Pimage.dart';
import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
import 'package:NovaSchool/services/eleve_services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../commun/app_background.dart';
import '../../../../commun/parent_app/app_PheadBar.dart';

class PProfilScreen extends StatefulWidget {
  final String nomClasse;
  final int eleveId;

  const PProfilScreen({
    Key? key,
    required this.eleveId,
    required this.nomClasse,
  }) : super(key: key);

  @override
  _PProfilScreenState createState() => _PProfilScreenState();
}

class _PProfilScreenState extends State<PProfilScreen> {
  String? nomClasse;
  late ValueNotifier<ChildProfile?> notifier;
  late PNavigationController _navigationController;

  @override
  void initState() {
    super.initState();
    _navigationController = Get.find<PNavigationController>();
    notifier = Get.find<ValueNotifier<ChildProfile?>>();
    notifier.addListener(_onProfileChanged);
    nomClasse = widget.nomClasse; 
    _loadNomClasse();
  }

  void _onProfileChanged() {
    if (mounted) {
      final selectedProfile = notifier.value;
      print(
          'Selected Profile in PProfilScreen: ${selectedProfile?.firstName} ${selectedProfile?.name}');

      _loadNomClasse();
    }
  }

  Future<void> _loadNomClasse() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? savedNomClasse = prefs.getString('nomClasse');
    if (savedNomClasse != null) {
      setState(() {
        nomClasse = savedNomClasse; // Update the local nomClasse variable
        _navigationController.nomClasse.value = savedNomClasse; // Update navigation controller
      });
    }
  }

  @override
  void dispose() {
    notifier.removeListener(_onProfileChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final EleveServices eleveService = Get.find<EleveServices>();

    return  Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: Image.asset(
              'assets/logos/ecocode.png', // Remplace avec ton chemin
              height: 150, // Ajuste la taille
              color: Colors
                  .white, // Applique du blanc au logo (si PNG avec transparence)
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),

          body: AppBackground(
        child: Column(
          children: [
            /*PrimaryHeaderContainer(
              child: Container(
                height: 200,
                child: PHeadHomeScreen(),
              ),
            ),*/
            Expanded(
              child: Obx(
                () {
                  final profile = _navigationController.selectedProfile.value;
                  if (profile == null) {
                    return Center(child: Text('No profile selected'));
                  }

                  return FutureBuilder<EleveResponseDTO>(
                    future: eleveService.getEleveById(profile.id),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Center(child: CircularProgressIndicator());
                      } else if (snapshot.hasError) {
                        return Center(child: Text('Error: ${snapshot.error}'));
                      } else if (!snapshot.hasData) {
                        return Center(child: Text('No data available'));
                      }

                      final eleveResponse = snapshot.data!;
                      final profileData = eleveResponse.profilEleveDto.first;
                      final imageEleve = eleveResponse.imageEleve;

                      return SingleChildScrollView(
                        child: Column(
                          children: [
                        Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16.0),),
                      SizedBox(height: 20),
                            PProfileImage(imageData: imageEleve, profile: profileData),
                            SizedBox(height: 10,),
                            PProfilForm(
                                profile: profileData,
                                nomClasse:
                                    nomClasse ??
                                        ''),
                          ],
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
