import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
//import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
class PEmploiScreen extends StatelessWidget {
  const PEmploiScreen({Key? key}) : super(key: key);

  Future<void> _launchEmploiURL(BuildContext context) async {
    String emploi = dotenv.get('dashURL');
    if (await canLaunch(emploi)) {
      await launch(emploi);
    } else {
      _showErrorDialog(
          context, 'Impossible d\'ouvrir la page d\'emploi du temps.');
    }
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Erreur'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<PNavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => ParentAppNavBar());
              },
              child: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header
            /*  PrimaryHeaderContainer(
                child: Container(
                  height: 100,
                  child: SecondHeadBar(
                    title: 'Emploi',
                    titleColor: Colors.white,
                    iconColor: Colors.white,
                  ),
                ),
              ),*/
              //const SizedBox(height: 20),

              Container(
                margin: EdgeInsets.symmetric(
                  horizontal:
                      screenWidth * 0.05, 
                  vertical: 10,
                ),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    elevation: 5,
                    padding: EdgeInsets.all(20),
                    backgroundColor: Colors.blue, 
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  onPressed: () => _launchEmploiURL(context),
                  child: Row(
                    children: [
                      Icon(Icons.web, color: Colors.white, size: 30),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          'Cliquez ici pour accéder à l\'emploi du temps',
                          style: TextStyle(fontSize: 16, color: Colors.white),
                          softWrap: true,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white,
                      ),
                      padding: const EdgeInsets.all(8.0), 
                      child: Icon(Icons.arrow_forward, color: Colors.blue, size: 20), 
                    ),
                    ],
                  ),
                ),
              ),

              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal:
                      screenWidth * 0.05, 
                  vertical: 10,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    Text(
                      'Page en cours de développement',
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 10),
                    Text(
                      'Cette page est actuellement en cours de développement. '
                      'L\'emploi sera disponible ici très bientôt. Merci pour votre patience.',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
