@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, expansion);

/// Generates custom tokens for the mat-expansion.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-expansion
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: sass-utils.merge-all(
    token-definition.generate-typography-tokens($systems, header-text, title-medium),
    token-definition.generate-typography-tokens($systems, container-text, body-large),
    (
      container-shape: token-definition.hardcode(12px, $exclude-hardcoded),
      legacy-header-indicator-display: token-definition.hardcode(none, $exclude-hardcoded),
      header-indicator-display: token-definition.hardcode(inline-block, $exclude-hardcoded),
      container-background-color: map.get($systems, md-sys-color, surface),
      container-text-color: map.get($systems, md-sys-color, on-surface),
      actions-divider-color: map.get($systems, md-sys-color, outline),
      header-hover-state-layer-color: sass-utils.safe-color-change(
        map.get($systems, md-sys-color, on-surface),
        $alpha: map.get($systems, md-sys-state, hover-state-layer-opacity)
      ),
      header-focus-state-layer-color: sass-utils.safe-color-change(
        map.get($systems, md-sys-color, on-surface),
        $alpha: map.get($systems, md-sys-state, focus-state-layer-opacity)
      ),
      header-disabled-state-text-color: sass-utils.safe-color-change(
          map.get($systems, md-sys-color, on-surface), $alpha: 0.38),
      header-text-color: map.get($systems, md-sys-color, on-surface),
      header-description-color: map.get($systems, md-sys-color, on-surface-variant),
      header-indicator-color: map.get($systems, md-sys-color, on-surface-variant),
    )
  );

  @return token-definition.namespace-tokens($prefix, $tokens, $token-slots);
}
