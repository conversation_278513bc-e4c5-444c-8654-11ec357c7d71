{"version": 3, "file": "animations.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/src/animation_builder.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ANIMATION_MODULE_TYPE,\n  Inject,\n  inject,\n  Injectable,\n  Renderer2,\n  RendererFactory2,\n  RendererType2,\n  ViewEncapsulation,\n  ɵAnimationRendererType as AnimationRendererType,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {AnimationMetadata, AnimationOptions, sequence} from './animation_metadata';\nimport {RuntimeErrorCode} from './errors';\nimport {AnimationPlayer} from './players/animation_player';\n\n/**\n * An injectable service that produces an animation sequence programmatically within an\n * Angular component or directive.\n * Provided by the `BrowserAnimationsModule` or `NoopAnimationsModule`.\n *\n * @usageNotes\n *\n * To use this service, add it to your component or directive as a dependency.\n * The service is instantiated along with your component.\n *\n * Apps do not typically need to create their own animation players, but if you\n * do need to, follow these steps:\n *\n * 1. Use the <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code> method\n * to create a programmatic animation. The method returns an `AnimationFactory` instance.\n *\n * 2. Use the factory object to create an `AnimationPlayer` and attach it to a DOM element.\n *\n * 3. Use the player object to control the animation programmatically.\n *\n * For example:\n *\n * ```ts\n * // import the service from BrowserAnimationsModule\n * import {AnimationBuilder} from '@angular/animations';\n * // require the service as a dependency\n * class MyCmp {\n *   constructor(private _builder: AnimationBuilder) {}\n *\n *   makeAnimation(element: any) {\n *     // first define a reusable animation\n *     const myAnimation = this._builder.build([\n *       style({ width: 0 }),\n *       animate(1000, style({ width: '100px' }))\n *     ]);\n *\n *     // use the returned factory object to create a player\n *     const player = myAnimation.create(element);\n *\n *     player.play();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root', useFactory: () => inject(BrowserAnimationBuilder)})\nexport abstract class AnimationBuilder {\n  /**\n   * Builds a factory for producing a defined animation.\n   * @param animation A reusable animation definition.\n   * @returns A factory object that can create a player for the defined animation.\n   * @see {@link animate}\n   */\n  abstract build(animation: AnimationMetadata | AnimationMetadata[]): AnimationFactory;\n}\n\n/**\n * A factory object returned from the\n * <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code>\n * method.\n *\n * @publicApi\n */\nexport abstract class AnimationFactory {\n  /**\n   * Creates an `AnimationPlayer` instance for the reusable animation defined by\n   * the <code>[AnimationBuilder.build](api/animations/AnimationBuilder#build)()</code>\n   * method that created this factory and attaches the new player a DOM element.\n   *\n   * @param element The DOM element to which to attach the player.\n   * @param options A set of options that can include a time delay and\n   * additional developer-defined parameters.\n   */\n  abstract create(element: any, options?: AnimationOptions): AnimationPlayer;\n}\n\n@Injectable({providedIn: 'root'})\nexport class BrowserAnimationBuilder extends AnimationBuilder {\n  private animationModuleType = inject(ANIMATION_MODULE_TYPE, {optional: true});\n  private _nextAnimationId = 0;\n  private _renderer: Renderer2;\n\n  constructor(rootRenderer: RendererFactory2, @Inject(DOCUMENT) doc: Document) {\n    super();\n    const typeData: RendererType2 = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {animation: []},\n    };\n    this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n\n    if (this.animationModuleType === null && !isAnimationRenderer(this._renderer)) {\n      // We only support AnimationRenderer & DynamicDelegationRenderer for this AnimationBuilder\n\n      throw new RuntimeError(\n        RuntimeErrorCode.BROWSER_ANIMATION_BUILDER_INJECTED_WITHOUT_ANIMATIONS,\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n          'Angular detected that the `AnimationBuilder` was injected, but animation support was not enabled. ' +\n            'Please make sure that you enable animations in your application by calling `provideAnimations()` or `provideAnimationsAsync()` function.',\n      );\n    }\n  }\n\n  override build(animation: AnimationMetadata | AnimationMetadata[]): AnimationFactory {\n    const id = this._nextAnimationId;\n    this._nextAnimationId++;\n    const entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\n}\n\nclass BrowserAnimationFactory extends AnimationFactory {\n  constructor(\n    private _id: number,\n    private _renderer: Renderer2,\n  ) {\n    super();\n  }\n\n  override create(element: any, options?: AnimationOptions): AnimationPlayer {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n}\n\nclass RendererAnimationPlayer implements AnimationPlayer {\n  public parentPlayer: AnimationPlayer | null = null;\n  private _started = false;\n\n  constructor(\n    public id: number,\n    public element: any,\n    options: AnimationOptions,\n    private _renderer: Renderer2,\n  ) {\n    this._command('create', options);\n  }\n\n  private _listen(eventName: string, callback: (event: any) => any): () => void {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n\n  private _command(command: string, ...args: any[]): void {\n    issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n\n  onDone(fn: () => void): void {\n    this._listen('done', fn);\n  }\n\n  onStart(fn: () => void): void {\n    this._listen('start', fn);\n  }\n\n  onDestroy(fn: () => void): void {\n    this._listen('destroy', fn);\n  }\n\n  init(): void {\n    this._command('init');\n  }\n\n  hasStarted(): boolean {\n    return this._started;\n  }\n\n  play(): void {\n    this._command('play');\n    this._started = true;\n  }\n\n  pause(): void {\n    this._command('pause');\n  }\n\n  restart(): void {\n    this._command('restart');\n  }\n\n  finish(): void {\n    this._command('finish');\n  }\n\n  destroy(): void {\n    this._command('destroy');\n  }\n\n  reset(): void {\n    this._command('reset');\n    this._started = false;\n  }\n\n  setPosition(p: number): void {\n    this._command('setPosition', p);\n  }\n\n  getPosition(): number {\n    return unwrapAnimationRenderer(this._renderer)?.engine?.players[this.id]?.getPosition() ?? 0;\n  }\n\n  public totalTime = 0;\n}\n\nfunction issueAnimationCommand(\n  renderer: Renderer2,\n  element: any,\n  id: number,\n  command: string,\n  args: any[],\n): void {\n  renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\n/**\n * The following 2 methods cannot reference their correct types (AnimationRenderer &\n * DynamicDelegationRenderer) since this would introduce a import cycle.\n */\n\nfunction unwrapAnimationRenderer(\n  renderer: Renderer2,\n): {engine: {players: AnimationPlayer[]}} | null {\n  const type = (renderer as unknown as {ɵtype: AnimationRendererType}).ɵtype;\n  if (type === AnimationRendererType.Regular) {\n    return renderer as any;\n  } else if (type === AnimationRendererType.Delegated) {\n    return (renderer as any).animationRenderer;\n  }\n\n  return null;\n}\n\nfunction isAnimationRenderer(renderer: Renderer2): boolean {\n  const type = (renderer as unknown as {ɵtype: AnimationRendererType}).ɵtype;\n  return type === AnimationRendererType.Regular || type === AnimationRendererType.Delegated;\n}\n"], "names": ["RuntimeError"], "mappings": ";;;;;;;;;;;;AAyBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CG;MAEmB,gBAAgB,CAAA;kHAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAhB,gBAAgB,EAAA,UAAA,EADb,MAAM,EAAc,UAAA,EAAA,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAA,CAAA;;sGAC5D,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,MAAM,CAAC,uBAAuB,CAAC,EAAC;;AAWnF;;;;;;AAMG;MACmB,gBAAgB,CAAA;AAWrC;AAGK,MAAO,uBAAwB,SAAQ,gBAAgB,CAAA;IACnD,mBAAmB,GAAG,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IACrE,gBAAgB,GAAG,CAAC;AACpB,IAAA,SAAS;IAEjB,WAAY,CAAA,YAA8B,EAAoB,GAAa,EAAA;AACzE,QAAA,KAAK,EAAE;AACP,QAAA,MAAM,QAAQ,GAAkB;AAC9B,YAAA,EAAE,EAAE,GAAG;YACP,aAAa,EAAE,iBAAiB,CAAC,IAAI;AACrC,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,IAAI,EAAE,EAAC,SAAS,EAAE,EAAE,EAAC;SACtB;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC;AAEhE,QAAA,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;;YAG7E,MAAM,IAAIA,aAAY,CAAA,IAAA,+EAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC5C,oGAAoG;AAClG,oBAAA,0IAA0I,CAC/I;;;AAII,IAAA,KAAK,CAAC,SAAkD,EAAA;AAC/D,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB;QAChC,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS;AACxE,QAAA,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC;QACpE,OAAO,IAAI,uBAAuB,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC;;AAhC7C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,kDAKkB,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AALjD,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,cADX,MAAM,EAAA,CAAA;;sGAClB,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBADnC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;0BAMe,MAAM;2BAAC,QAAQ;;AA+B9D,MAAM,uBAAwB,SAAQ,gBAAgB,CAAA;AAE1C,IAAA,GAAA;AACA,IAAA,SAAA;IAFV,WACU,CAAA,GAAW,EACX,SAAoB,EAAA;AAE5B,QAAA,KAAK,EAAE;QAHC,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAS,CAAA,SAAA,GAAT,SAAS;;IAKV,MAAM,CAAC,OAAY,EAAE,OAA0B,EAAA;AACtD,QAAA,OAAO,IAAI,uBAAuB,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC;;AAEvF;AAED,MAAM,uBAAuB,CAAA;AAKlB,IAAA,EAAA;AACA,IAAA,OAAA;AAEC,IAAA,SAAA;IAPH,YAAY,GAA2B,IAAI;IAC1C,QAAQ,GAAG,KAAK;AAExB,IAAA,WAAA,CACS,EAAU,EACV,OAAY,EACnB,OAAyB,EACjB,SAAoB,EAAA;QAHrB,IAAE,CAAA,EAAA,GAAF,EAAE;QACF,IAAO,CAAA,OAAA,GAAP,OAAO;QAEN,IAAS,CAAA,SAAA,GAAT,SAAS;AAEjB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC;;IAG1B,OAAO,CAAC,SAAiB,EAAE,QAA6B,EAAA;QAC9D,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE,CAAI,CAAA,EAAA,SAAS,EAAE,EAAE,QAAQ,CAAC;;AAG3E,IAAA,QAAQ,CAAC,OAAe,EAAE,GAAG,IAAW,EAAA;AAC9C,QAAA,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC;;AAG7E,IAAA,MAAM,CAAC,EAAc,EAAA;AACnB,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;;AAG1B,IAAA,OAAO,CAAC,EAAc,EAAA;AACpB,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;;AAG3B,IAAA,SAAS,CAAC,EAAc,EAAA;AACtB,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;;IAG7B,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;;IAGvB,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ;;IAGtB,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;IAGtB,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;;IAGxB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;;IAG1B,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;;IAGzB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;;IAG1B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACtB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;AAGvB,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAC;;IAGjC,WAAW,GAAA;QACT,OAAO,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC;;IAGvF,SAAS,GAAG,CAAC;AACrB;AAED,SAAS,qBAAqB,CAC5B,QAAmB,EACnB,OAAY,EACZ,EAAU,EACV,OAAe,EACf,IAAW,EAAA;AAEX,IAAA,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,CAAA,EAAA,EAAK,EAAE,CAAA,CAAA,EAAI,OAAO,CAAA,CAAE,EAAE,IAAI,CAAC;AAC3D;AAEA;;;AAGG;AAEH,SAAS,uBAAuB,CAC9B,QAAmB,EAAA;AAEnB,IAAA,MAAM,IAAI,GAAI,QAAsD,CAAC,KAAK;IAC1E,IAAI,IAAI,KAAkC,CAAA,sCAAE;AAC1C,QAAA,OAAO,QAAe;;SACjB,IAAI,IAAI,KAAoC,CAAA,wCAAE;QACnD,OAAQ,QAAgB,CAAC,iBAAiB;;AAG5C,IAAA,OAAO,IAAI;AACb;AAEA,SAAS,mBAAmB,CAAC,QAAmB,EAAA;AAC9C,IAAA,MAAM,IAAI,GAAI,QAAsD,CAAC,KAAK;AAC1E,IAAA,OAAO,IAAI,KAAA,CAAA,wCAAsC,IAAI;AACvD;;;;"}