import { ContentContainerComponentHarness, HarnessPredicate, ComponentHarness, parallel } from '@angular/cdk/testing';

/** Harness for interacting with an Angular Material tab in tests. */
class MatTabHarness extends ContentContainerComponentHarness {
    /** The selector for the host element of a `MatTab` instance. */
    static hostSelector = '.mat-mdc-tab';
    /**
     * Gets a `HarnessPredicate` that can be used to search for a tab with specific attributes.
     * @param options Options for filtering which tab instances are considered a match.
     * @return a `HarnessPredicate` configured with the given options.
     */
    static with(options = {}) {
        return new HarnessPredicate(this, options)
            .addOption('label', options.label, (harness, label) => HarnessPredicate.stringMatches(harness.getLabel(), label))
            .addOption('selected', options.selected, async (harness, selected) => (await harness.isSelected()) == selected);
    }
    /** Gets the label of the tab. */
    async getLabel() {
        return (await this.host()).text();
    }
    /** Gets the aria-label of the tab. */
    async getAriaLabel() {
        return (await this.host()).getAttribute('aria-label');
    }
    /** Gets the value of the "aria-labelledby" attribute. */
    async getAriaLabelledby() {
        return (await this.host()).getAttribute('aria-labelledby');
    }
    /** Whether the tab is selected. */
    async isSelected() {
        const hostEl = await this.host();
        return (await hostEl.getAttribute('aria-selected')) === 'true';
    }
    /** Whether the tab is disabled. */
    async isDisabled() {
        const hostEl = await this.host();
        return (await hostEl.getAttribute('aria-disabled')) === 'true';
    }
    /** Selects the given tab by clicking on the label. Tab cannot be selected if disabled. */
    async select() {
        await (await this.host()).click('center');
    }
    /** Gets the text content of the tab. */
    async getTextContent() {
        const contentId = await this._getContentId();
        const contentEl = await this.documentRootLocatorFactory().locatorFor(`#${contentId}`)();
        return contentEl.text();
    }
    async getRootHarnessLoader() {
        const contentId = await this._getContentId();
        return this.documentRootLocatorFactory().harnessLoaderFor(`#${contentId}`);
    }
    /** Gets the element id for the content of the current tab. */
    async _getContentId() {
        const hostEl = await this.host();
        // Tabs never have an empty "aria-controls" attribute.
        return (await hostEl.getAttribute('aria-controls'));
    }
}

/** Harness for interacting with a mat-tab-group in tests. */
class MatTabGroupHarness extends ComponentHarness {
    /** The selector for the host element of a `MatTabGroup` instance. */
    static hostSelector = '.mat-mdc-tab-group';
    /**
     * Gets a `HarnessPredicate` that can be used to search for a tab group with specific attributes.
     * @param options Options for filtering which tab group instances are considered a match.
     * @return a `HarnessPredicate` configured with the given options.
     */
    static with(options = {}) {
        return new HarnessPredicate(this, options).addOption('selectedTabLabel', options.selectedTabLabel, async (harness, label) => {
            const selectedTab = await harness.getSelectedTab();
            return HarnessPredicate.stringMatches(await selectedTab.getLabel(), label);
        });
    }
    /**
     * Gets the list of tabs in the tab group.
     * @param filter Optionally filters which tabs are included.
     */
    async getTabs(filter = {}) {
        return this.locatorForAll(MatTabHarness.with(filter))();
    }
    /** Gets the selected tab of the tab group. */
    async getSelectedTab() {
        const tabs = await this.getTabs();
        const isSelected = await parallel(() => tabs.map(t => t.isSelected()));
        for (let i = 0; i < tabs.length; i++) {
            if (isSelected[i]) {
                return tabs[i];
            }
        }
        throw new Error('No selected tab could be found.');
    }
    /**
     * Selects a tab in this tab group.
     * @param filter An optional filter to apply to the child tabs. The first tab matching the filter
     *     will be selected.
     */
    async selectTab(filter = {}) {
        const tabs = await this.getTabs(filter);
        if (!tabs.length) {
            throw Error(`Cannot find mat-tab matching filter ${JSON.stringify(filter)}`);
        }
        await tabs[0].select();
    }
}

/** Harness for interacting with a Angular Material tab link in tests. */
class MatTabLinkHarness extends ComponentHarness {
    /** The selector for the host element of a `MatTabLink` instance. */
    static hostSelector = '.mat-mdc-tab-link';
    /**
     * Gets a `HarnessPredicate` that can be used to search for a tab link with specific attributes.
     * @param options Options for filtering which tab link instances are considered a match.
     * @return a `HarnessPredicate` configured with the given options.
     */
    static with(options = {}) {
        return new HarnessPredicate(this, options).addOption('label', options.label, (harness, label) => HarnessPredicate.stringMatches(harness.getLabel(), label));
    }
    /** Gets the label of the link. */
    async getLabel() {
        return (await this.host()).text();
    }
    /** Whether the link is active. */
    async isActive() {
        const host = await this.host();
        return host.hasClass('mdc-tab--active');
    }
    /** Whether the link is disabled. */
    async isDisabled() {
        const host = await this.host();
        return host.hasClass('mat-mdc-tab-disabled');
    }
    /** Clicks on the link. */
    async click() {
        await (await this.host()).click();
    }
}

/** Harness for interacting with a standard mat-tab-nav-panel in tests. */
class MatTabNavPanelHarness extends ContentContainerComponentHarness {
    /** The selector for the host element of a `MatTabNavPanel` instance. */
    static hostSelector = '.mat-mdc-tab-nav-panel';
    /**
     * Gets a `HarnessPredicate` that can be used to search for a tab nav panel with specific
     * attributes.
     * @param options Options for filtering which tab nav panel instances are considered a match.
     * @return a `HarnessPredicate` configured with the given options.
     */
    static with(options = {}) {
        return new HarnessPredicate(this, options);
    }
    /** Gets the tab panel text content. */
    async getTextContent() {
        return (await this.host()).text();
    }
}

/** Harness for interacting with a mat-tab-nav-bar in tests. */
class MatTabNavBarHarness extends ComponentHarness {
    /** The selector for the host element of a `MatTabNavBar` instance. */
    static hostSelector = '.mat-mdc-tab-nav-bar';
    /**
     * Gets a `HarnessPredicate` that can be used to search for a tab nav bar with specific
     * attributes.
     * @param options Options for filtering which tab nav bar instances are considered a match.
     * @return a `HarnessPredicate` configured with the given options.
     */
    static with(options = {}) {
        return new HarnessPredicate(this, options);
    }
    /**
     * Gets the list of links in the nav bar.
     * @param filter Optionally filters which links are included.
     */
    async getLinks(filter = {}) {
        return this.locatorForAll(MatTabLinkHarness.with(filter))();
    }
    /** Gets the active link in the nav bar. */
    async getActiveLink() {
        const links = await this.getLinks();
        const isActive = await parallel(() => links.map(t => t.isActive()));
        for (let i = 0; i < links.length; i++) {
            if (isActive[i]) {
                return links[i];
            }
        }
        throw new Error('No active link could be found.');
    }
    /**
     * Clicks a link inside the nav bar.
     * @param filter An optional filter to apply to the child link. The first link matching the filter
     *     will be clicked.
     */
    async clickLink(filter = {}) {
        const tabs = await this.getLinks(filter);
        if (!tabs.length) {
            throw Error(`Cannot find mat-tab-link matching filter ${JSON.stringify(filter)}`);
        }
        await tabs[0].click();
    }
    /** Gets the panel associated with the nav bar. */
    async getPanel() {
        const link = await this.getActiveLink();
        const host = await link.host();
        const panelId = await host.getAttribute('aria-controls');
        if (!panelId) {
            throw Error('No panel is controlled by the nav bar.');
        }
        const filter = { selector: `#${panelId}` };
        return await this.documentRootLocatorFactory().locatorFor(MatTabNavPanelHarness.with(filter))();
    }
}

export { MatTabGroupHarness, MatTabHarness, MatTabLinkHarness, MatTabNavBarHarness };
//# sourceMappingURL=testing.mjs.map
