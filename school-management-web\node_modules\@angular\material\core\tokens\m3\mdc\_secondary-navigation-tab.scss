@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, secondary-navigation-tab);

/// Generates the tokens for MDC tab
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of tokens for the MDC tab
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $mdc-tokens: token-definition.get-mdc-tokens(
      'secondary-navigation-tab', $systems, $exclude-hardcoded);

  @return token-definition.namespace-tokens($prefix, $mdc-tokens, $token-slots);
}
