import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../../commun/base_url.dart';
import 'classe_exercice.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class ExerciceService {
  // String baseURL = dotenv.get('baseURL');
  // final String baseUrl = "$baseURL/exercices";
  late final String baseURL;
  late final String baseUrl;

  ExerciceService(){
    baseURL = dotenv.get('baseURL');
    baseUrl = '$baseURL/exercices';
  }

  Future<String> _getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('token') ?? '';
  }

  Future<List<Exercice>> getAllExercices() async {
    final token = await _getToken();

    final response = await http.get(
      Uri.parse('$baseUrl/getAllExercices'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      List<dynamic> jsonData = json.decode(response.body);
      return jsonData.map((e) => Exercice.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load exercices');
    }
  }

  Future<void> addExercice(Exercice exercice) async {
    final token = await _getToken();

    final response = await http.post(
      Uri.parse('$baseUrl/createExercice'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(exercice.toJson()),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to add exercice');
    }
  }

  Future<void> updateExercice(Exercice exercice) async {
    final token = await _getToken();

    final response = await http.put(
      Uri.parse('$baseUrl/modifierExercice/${exercice.id}'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(exercice.toJson()),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to update exercice');
    }
  }

  Future<void> deleteExercice(String exerciceId) async {
    final token = await _getToken();

    final response = await http.delete(
      Uri.parse('$baseUrl/supprimerExercice/$exerciceId'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to delete exercice');
    }
  }
}
