import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:NovaSchool/features/welcome/welcome_screen.dart';
import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
import 'package:NovaSchool/services/eleve_services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:NovaSchool/services/cookies_management.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AuthService {
  static const String jwtTokenKey = 'token';
  static const String refreshTokenKey = 'refreshToken';
  static const String profileKey = 'profile';
  static const String nameKey = 'name';
  static const String lastnameKey = 'lastname';
  static const String userIdKey = 'userId';
  static const String emailKey = 'email';

  late final String backendLoginUrl;
  //late final String backendLogoutUrl;
  late final String clientId;
  late final String baseURL;

  AuthService(){
     backendLoginUrl = dotenv.get('backendLoginUrl');
     //backendLogoutUrl = dotenv.get('backendLogoutUrl');
     clientId = dotenv.get('clientId');
     baseURL = dotenv.get('baseURL');

  }

  // Future<Map<String, String>> login(String username, String password) async {
  //   final body = {
  //     'username': username,
  //     'password': password,
  //     'client_id': clientId,
  //     'client_secret': clientSecret,
  //     'grant_type': grantType,
  //   };
  //   print("Auth enter");
  //   final response = await http.post(
  //     Uri.parse(tokenUrl),
  //     headers: {'Content-Type': 'application/x-www-form-urlencoded'},
  //     body: body,
  //   );

  //   if (response.statusCode == 200) {
  //     final data = json.decode(response.body);
  //     final accessToken = data['access_token'];
  //     final refreshToken = data['refresh_token'];
  //     print("Auth success");

  //     // Store tokens locally
  //     await _storeTokens(accessToken, refreshToken);

  //     // Extract and store user details
  //     final profile = decodeToken(accessToken);
  //     final name = profile['name'] ?? '';
  //     final lastname = profile['family_name'] ?? '';
  //     final userId = profile['sub'] ?? '';
  //     final email = profile['email'] ?? '';

  //     await _storeUserProfile(name, lastname, userId, email);

  //     return {'accessToken': accessToken, 'refreshToken': refreshToken};
  //   } else {
  //     print("Auth failed");
  //     throw Exception('Failed to login');
  //   }
  // }

  Future<void> login(String username, String password) async {
    //String backendLoginUrl = dotenv.get('backendLoginUrl');
    final response = await http.post(
      Uri.parse(backendLoginUrl),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
      body: {
        'username': username,
        'password': password,
      },
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final accessToken = data['accessToken'];
      final refreshToken = data['refreshToken'];

      // Store tokens locally
      await _storeTokens(accessToken, refreshToken);

      // Extract and store user details
      final profile = decodeToken(accessToken);
      final name = profile['name'] ?? '';
      final lastname = profile['family_name'] ?? '';
      final userId = profile['sub'] ?? '';
      final email = profile['email'] ?? '';

      await _storeUserProfile(name, lastname, userId, email);
    } else {
      throw Exception('Failed to login');
    }
  }

  // static Future<void> logoutKeycloak() async {
  //   final prefs = await SharedPreferences.getInstance();
  //   final refresh_token = prefs.getString('refreshToken');
  //   final body = {
  //     'client_id': clientId,
  //     'client_secret': clientSecret,
  //     'refresh_token': refresh_token,
  //   };

  //   final response = await http.post(
  //     Uri.parse(logoutUrl),
  //     headers: {'Content-Type': 'application/x-www-form-urlencoded'},
  //     body: body,
  //   );

  //   if (response.statusCode == 204) {
  //     await prefs.clear();
  //     // Reset the ValueNotifier
  //     final notifier = Get.find<ValueNotifier<ChildProfile?>>();
  //     notifier.value = null;
  //     Get.offAll(() => const WelcomeScreen());
  //   } else {
  //     throw Exception('Failed to logout');
  //   }
  // }

  static Future<void> logoutKeycloak() async {
    String backendLogoutUrl = dotenv.get('backendLogoutUrl');
    final prefs = await SharedPreferences.getInstance();
    final refresh_token = prefs.getString('refreshToken');
    final body = {
      'refreshToken': refresh_token,
    };
    print(refresh_token);

    final response = await http.post(
      Uri.parse(backendLogoutUrl),
      headers: {'Content-Type': 'application/x-www-form-urlencoded'},
      body: body,
    );

    if (response.statusCode == 204) {
      await prefs.remove("token");
      await prefs.remove("refreshToken");
      await prefs.remove("name");
      await prefs.remove("lastname");
      await prefs.remove("userId");
      await prefs.remove("email");
      await prefs.remove("profile");

      // Reset the ValueNotifier
      final notifier = Get.find<ValueNotifier<ChildProfile?>>();
      notifier.value = null;
      Get.offAll(() => const WelcomeScreen());
    } else {
      throw Exception('Failed to logout');
    }
  }

  // Future<bool> introspectKeycloak() async {
  //   final prefs = await SharedPreferences.getInstance();
  //   final token = prefs.getString('token');

  //   final body = {
  //     'client_id': clientId,
  //     'client_secret': clientSecret,
  //     'token': token,
  //   };

  //   final response = await http.post(
  //     Uri.parse(introspectUrl),
  //     headers: {'Content-Type': 'application/x-www-form-urlencoded'},
  //     body: body,
  //   );

  //   if (response.statusCode == 200) {
  //     final Map<String, dynamic> responseData = json.decode(response.body);

  //     return responseData['active'] ?? false;
  //   } else {
  //     return false;
  //   }
  // }

  Future<void> _storeTokens(String accessToken, String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(jwtTokenKey, accessToken);
    await prefs.setString(refreshTokenKey, refreshToken);
  }

  Future<void> _storeUserProfile(
      String name, String lastname, String userId, String email) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(nameKey, name);
    await prefs.setString(lastnameKey, lastname);
    await prefs.setString(userIdKey, userId);
    await prefs.setString(emailKey, email);
  }

  Future<String?> getJwtToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(jwtTokenKey);
  }

  void clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(jwtTokenKey);
    await prefs.remove(refreshTokenKey);
  }

  Map<String, dynamic> decodeToken(String token) {
    final parts = token.split('.');
    if (parts.length != 3) {
      throw Exception('Invalid token');
    }

    final payload = _decodeBase64(parts[1]);
    final payloadMap = json.decode(payload);
    if (payloadMap is! Map<String, dynamic>) {
      throw Exception('Invalid payload');
    }

    return payloadMap;
  }

  String _decodeBase64(String str) {
    String output = str.replaceAll('-', '+').replaceAll('_', '/');
    switch (output.length % 4) {
      case 0:
        break;
      case 2:
        output += '==';
        break;
      case 3:
        output += '=';
        break;
      default:
        throw Exception('Illegal base64url string!');
    }
    return utf8.decode(base64Url.decode(output));
  }

  Future<bool> isAdmin() async {
    //String clientId = dotenv.get('clientId');
    final token = await getJwtToken();
    if (token != null) {
      final decodedToken = decodeToken(token);
      if (decodedToken['resource_access'] != null &&
          decodedToken['resource_access'][clientId] != null) {
        final roles =
            decodedToken['resource_access'][clientId]['roles'] as List;
        final isAdmin = roles.contains('admin');

        if (isAdmin) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(profileKey, 'Admin');
        }

        return isAdmin;
      }
    }
    return false;
  }

  Future<bool> isParent() async {
    //String clientId = dotenv.get('clientId');
    final token = await getJwtToken();
    if (token != null) {
      final decodedToken = decodeToken(token);
      if (decodedToken['resource_access'] != null &&
          decodedToken['resource_access'][clientId] != null) {
        final roles =
            decodedToken['resource_access'][clientId]['roles'] as List;
        final isParent = roles.contains('parent');

        if (isParent) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(profileKey, 'Parent');
        }

        return isParent;
      }
    }
    return false;
  }

  Future<bool> isEnseiganat() async {
    //String clientId = dotenv.get('clientId');
    final token = await getJwtToken();
    if (token != null) {
      final decodedToken = decodeToken(token);
      if (decodedToken['resource_access'] != null &&
          decodedToken['resource_access'][clientId] != null) {
        final roles =
            decodedToken['resource_access'][clientId]['roles'] as List;
        final isEnseignant = roles.contains('enseignant');

        if (isEnseignant) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(profileKey, 'Enseignant');
        }

        return isEnseignant;
      }
    }
    return false;
  }

  Future<bool> isAuthenticated() async {
    //String baseURL = dotenv.get('baseURL');
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('token');
    final response = await http.post(
      Uri.parse('$baseURL/users/isAuthenticate?token=$token'),
      headers: {'Content-Type': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['active'] ?? false;
    } else {
      
      throw Exception('Failed to check token authentication');
    }
  }


  Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
      print('No user ID found in SharedPreferences');
      return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
      List<EleveInfoPersoDTO> eleves =
          await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
      return eleves.map((eleve) {
        return ChildProfile(
            eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
      }).toList();
    } catch (e) {
      print('Failed to fetch profiles: $e');
      return [];
    }
  }

  static Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      set('AUTH_SESSION_ID', 'okk');
      set('AUTH_SESSION_ID_LEGACY', 'okk');
      set('KEYCLOAK_IDENTITY', 'okk');
      set('KEYCLOAK_IDENTITY_LEGACY', 'okk');
      set('KEYCLOAK_SESSION', 'okk');
      set('KEYCLOAK_SESSION_LEGACY', 'okk');

      final notifier = Get.find<ValueNotifier<ChildProfile?>>();
      notifier.value = null;
      Get.offAll(() => const WelcomeScreen());
    } catch (e) {
      print("Error during logout: $e");
    }
  }

  Future<String> getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('token') ?? '';
  }

  Future<String> getUsername() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('name') ?? '';
  }

  Future<String> getLastname() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('lastname') ?? '';
  }

  Future<String> getUserId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('userId') ?? '';
  }

  Future<int> getEleveId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getInt('eleveId') ?? null!;
  }

  Future<Map<String, String>> getSenderDetails() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final username = prefs.getString("name") ?? '';
    final surname = prefs.getString("lastname") ?? '';
    final userId = prefs.getString("userId") ?? '';
    final email = prefs.getString("email") ?? '';
    final profile = prefs.getString("profile") ?? '';

    return {
      "senderId": userId,
      "senderUsername": username,
      "senderUserSurname": surname,
      "email": email,
      "profile": profile,
    };
  }
}
