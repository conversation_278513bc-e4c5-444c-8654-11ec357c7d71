import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-teacher-profile',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-profile">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>👤 My Profile</h1>
            <p>Manage your personal information and preferences</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="toggleEdit()">
              <span class="icon">{{isEditing ? '💾' : '✏️'}}</span>
              {{isEditing ? 'Save Changes' : 'Edit Profile'}}
            </button>
          </div>
        </div>
      </header>

      <!-- Profile Content -->
      <div class="profile-content">
        <!-- Profile Card -->
        <div class="profile-card">
          <div class="profile-avatar-section">
            <div class="profile-avatar">
              <img [src]="profile.avatar" [alt]="profile.name" *ngIf="profile.avatar">
              <span *ngIf="!profile.avatar">{{profile.name.charAt(0)}}</span>
            </div>
            <button class="change-avatar-btn" *ngIf="isEditing">
              <span class="icon">📷</span>
              Change Photo
            </button>
          </div>
          <div class="profile-info">
            <div class="profile-name">
              <input *ngIf="isEditing" type="text" [(ngModel)]="profile.name" class="edit-input large">
              <h2 *ngIf="!isEditing">{{profile.name}}</h2>
            </div>
            <div class="profile-title">
              <input *ngIf="isEditing" type="text" [(ngModel)]="profile.title" class="edit-input">
              <p *ngIf="!isEditing">{{profile.title}}</p>
            </div>
            <div class="profile-stats">
              <div class="stat">
                <span class="stat-number">{{profile.experience}}</span>
                <span class="stat-label">Years Experience</span>
              </div>
              <div class="stat">
                <span class="stat-number">{{profile.students}}</span>
                <span class="stat-label">Students</span>
              </div>
              <div class="stat">
                <span class="stat-number">{{profile.classes}}</span>
                <span class="stat-label">Classes</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Profile Sections -->
        <div class="profile-sections">
          <!-- Personal Information -->
          <div class="profile-section">
            <h3>📋 Personal Information</h3>
            <div class="section-content">
              <div class="form-row">
                <div class="form-group">
                  <label>Email</label>
                  <input *ngIf="isEditing" type="email" [(ngModel)]="profile.email" class="edit-input">
                  <span *ngIf="!isEditing" class="info-value">{{profile.email}}</span>
                </div>
                <div class="form-group">
                  <label>Phone</label>
                  <input *ngIf="isEditing" type="tel" [(ngModel)]="profile.phone" class="edit-input">
                  <span *ngIf="!isEditing" class="info-value">{{profile.phone}}</span>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Department</label>
                  <select *ngIf="isEditing" [(ngModel)]="profile.department" class="edit-input">
                    <option value="Mathematics">Mathematics</option>
                    <option value="Science">Science</option>
                    <option value="Languages">Languages</option>
                    <option value="Arts">Arts</option>
                  </select>
                  <span *ngIf="!isEditing" class="info-value">{{profile.department}}</span>
                </div>
                <div class="form-group">
                  <label>Employee ID</label>
                  <span class="info-value">{{profile.employeeId}}</span>
                </div>
              </div>
              <div class="form-group">
                <label>Address</label>
                <textarea *ngIf="isEditing" [(ngModel)]="profile.address" class="edit-input" rows="2"></textarea>
                <span *ngIf="!isEditing" class="info-value">{{profile.address}}</span>
              </div>
            </div>
          </div>

          <!-- Professional Information -->
          <div class="profile-section">
            <h3>🎓 Professional Information</h3>
            <div class="section-content">
              <div class="form-row">
                <div class="form-group">
                  <label>Education</label>
                  <input *ngIf="isEditing" type="text" [(ngModel)]="profile.education" class="edit-input">
                  <span *ngIf="!isEditing" class="info-value">{{profile.education}}</span>
                </div>
                <div class="form-group">
                  <label>Certifications</label>
                  <input *ngIf="isEditing" type="text" [(ngModel)]="profile.certifications" class="edit-input">
                  <span *ngIf="!isEditing" class="info-value">{{profile.certifications}}</span>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Specializations</label>
                  <input *ngIf="isEditing" type="text" [(ngModel)]="profile.specializations" class="edit-input">
                  <span *ngIf="!isEditing" class="info-value">{{profile.specializations}}</span>
                </div>
                <div class="form-group">
                  <label>Languages</label>
                  <input *ngIf="isEditing" type="text" [(ngModel)]="profile.languages" class="edit-input">
                  <span *ngIf="!isEditing" class="info-value">{{profile.languages}}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Teaching Schedule -->
          <div class="profile-section">
            <h3>📅 Teaching Schedule</h3>
            <div class="section-content">
              <div class="schedule-grid">
                <div class="schedule-item" *ngFor="let item of profile.schedule">
                  <div class="schedule-time">{{item.time}}</div>
                  <div class="schedule-details">
                    <div class="schedule-subject">{{item.subject}}</div>
                    <div class="schedule-class">{{item.class}}</div>
                    <div class="schedule-room">{{item.room}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Preferences -->
          <div class="profile-section">
            <h3>⚙️ Preferences</h3>
            <div class="section-content">
              <div class="preferences-grid">
                <div class="preference-item">
                  <label class="preference-label">
                    <input type="checkbox" [(ngModel)]="profile.preferences.emailNotifications">
                    <span class="checkmark"></span>
                    Email Notifications
                  </label>
                </div>
                <div class="preference-item">
                  <label class="preference-label">
                    <input type="checkbox" [(ngModel)]="profile.preferences.smsNotifications">
                    <span class="checkmark"></span>
                    SMS Notifications
                  </label>
                </div>
                <div class="preference-item">
                  <label class="preference-label">
                    <input type="checkbox" [(ngModel)]="profile.preferences.weeklyReports">
                    <span class="checkmark"></span>
                    Weekly Reports
                  </label>
                </div>
                <div class="preference-item">
                  <label class="preference-label">
                    <input type="checkbox" [(ngModel)]="profile.preferences.parentUpdates">
                    <span class="checkmark"></span>
                    Parent Updates
                  </label>
                </div>
              </div>
              <div class="form-group">
                <label>Preferred Language</label>
                <select *ngIf="isEditing" [(ngModel)]="profile.preferences.language" class="edit-input">
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                </select>
                <span *ngIf="!isEditing" class="info-value">{{getLanguageName(profile.preferences.language)}}</span>
              </div>
            </div>
          </div>

          <!-- Security -->
          <div class="profile-section">
            <h3>🔒 Security</h3>
            <div class="section-content">
              <div class="security-actions">
                <button class="security-btn" (click)="changePassword()">
                  <span class="icon">🔑</span>
                  Change Password
                </button>
                <button class="security-btn" (click)="enable2FA()">
                  <span class="icon">🛡️</span>
                  Enable 2FA
                </button>
                <button class="security-btn" (click)="viewLoginHistory()">
                  <span class="icon">📊</span>
                  Login History
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .teacher-profile {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .profile-content {
      display: grid;
      gap: 30px;
    }

    .profile-card {
      background: white;
      border-radius: 16px;
      padding: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      gap: 30px;
      align-items: center;
    }

    .profile-avatar-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }

    .profile-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 48px;
      font-weight: 600;
      overflow: hidden;
    }

    .profile-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .change-avatar-btn {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      padding: 8px 16px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .profile-info {
      flex: 1;
    }

    .profile-name h2 {
      margin: 0 0 8px 0;
      color: #2d3748;
      font-size: 28px;
    }

    .profile-title p {
      margin: 0 0 20px 0;
      color: #667eea;
      font-size: 18px;
      font-weight: 500;
    }

    .profile-stats {
      display: flex;
      gap: 40px;
    }

    .stat {
      text-align: center;
    }

    .stat-number {
      display: block;
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #718096;
    }

    .profile-sections {
      display: grid;
      gap: 24px;
    }

    .profile-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .profile-section h3 {
      margin: 0 0 20px 0;
      color: #2d3748;
      font-size: 18px;
      padding-bottom: 12px;
      border-bottom: 2px solid #f1f3f4;
    }

    .section-content {
      display: grid;
      gap: 16px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .form-group label {
      font-weight: 500;
      color: #2d3748;
      font-size: 14px;
    }

    .info-value {
      color: #4a5568;
      font-size: 14px;
      padding: 8px 0;
    }

    .edit-input {
      padding: 10px 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    .edit-input:focus {
      outline: none;
      border-color: #667eea;
    }

    .edit-input.large {
      font-size: 24px;
      font-weight: 600;
      padding: 12px 16px;
    }

    .schedule-grid {
      display: grid;
      gap: 12px;
    }

    .schedule-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 12px;
      background: #f8fafc;
      border-radius: 8px;
    }

    .schedule-time {
      font-weight: 600;
      color: #667eea;
      min-width: 80px;
    }

    .schedule-details {
      flex: 1;
    }

    .schedule-subject {
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 2px;
    }

    .schedule-class,
    .schedule-room {
      font-size: 12px;
      color: #718096;
    }

    .preferences-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .preference-item {
      display: flex;
      align-items: center;
    }

    .preference-label {
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      font-size: 14px;
      color: #2d3748;
    }

    .preference-label input[type="checkbox"] {
      width: 18px;
      height: 18px;
      accent-color: #667eea;
    }

    .security-actions {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    .security-btn {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .security-btn:hover {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .profile-card {
        flex-direction: column;
        text-align: center;
      }

      .profile-stats {
        justify-content: center;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .preferences-grid {
        grid-template-columns: 1fr;
      }

      .security-actions {
        flex-direction: column;
      }
    }
  `]
})
export class TeacherProfileComponent {
  isEditing = false;

  profile = {
    name: 'Sarah Johnson',
    title: 'Mathematics Teacher',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Mathematics',
    employeeId: 'T001',
    address: '123 Main Street, City, State 12345',
    education: 'M.Ed. Mathematics Education, State University',
    certifications: 'Certified Mathematics Teacher, Advanced Pedagogy',
    specializations: 'Algebra, Geometry, Statistics',
    languages: 'English, Spanish',
    experience: 8,
    students: 125,
    classes: 5,
    avatar: null,
    schedule: [
      { time: '08:00 - 08:50', subject: 'Mathematics', class: 'Grade 5A', room: 'Room 101' },
      { time: '09:00 - 09:50', subject: 'Algebra', class: 'Grade 5B', room: 'Room 101' },
      { time: '10:30 - 11:20', subject: 'Mathematics', class: 'Grade 4A', room: 'Room 101' },
      { time: '14:00 - 14:50', subject: 'Advanced Math', class: 'Grade 6A', room: 'Room 101' }
    ],
    preferences: {
      emailNotifications: true,
      smsNotifications: false,
      weeklyReports: true,
      parentUpdates: true,
      language: 'en'
    }
  };

  toggleEdit(): void {
    if (this.isEditing) {
      this.saveProfile();
    }
    this.isEditing = !this.isEditing;
  }

  saveProfile(): void {
    console.log('Saving profile:', this.profile);
    // Here you would typically send the data to your backend
  }

  getLanguageName(code: string): string {
    const languages: { [key: string]: string } = {
      'en': 'English',
      'es': 'Spanish',
      'fr': 'French'
    };
    return languages[code] || code;
  }

  changePassword(): void {
    console.log('Change password');
  }

  enable2FA(): void {
    console.log('Enable 2FA');
  }

  viewLoginHistory(): void {
    console.log('View login history');
  }
}
