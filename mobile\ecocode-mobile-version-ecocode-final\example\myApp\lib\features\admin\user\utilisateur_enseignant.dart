import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:NovaSchool/features/teacher_app/Profil/enseignant_services.dart';
import '../../teacher_app/Profil/enseignant_class.dart';

class TeacherManagementWidget extends StatefulWidget {
  @override
  _TeacherManagementWidgetState createState() =>
      _TeacherManagementWidgetState();
}

class _TeacherManagementWidgetState extends State<TeacherManagementWidget> {
  List<Enseignant> teachers = [];
  bool isLoading = true;
  int currentPage = 0;
  int totalPages = 0;
  final int pageSize = 5;

  @override
  void initState() {
    super.initState();
    loadTeachers(currentPage);
  }

  Future<void> loadTeachers(int page) async {
    try {
      EnseignantService enseignantService = EnseignantService();
      Map<String, dynamic> result =
          await enseignantService.getEnseignantsByPage(page, pageSize);

      // Extract data from the result
      List<dynamic> content = result['content'] ?? [];
      List<Enseignant> fetchedEnseignant =
          content.map((data) => Enseignant.fromJson(data)).toList();
      int total =
          result['totalPages'] ?? 0; 

      setState(() {
        teachers = fetchedEnseignant;
        totalPages = total;
        currentPage = page;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('Failed to load teachers: $e');
    }
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < totalPages) {
      loadTeachers(newIndex);
    }
  }

  Future<void> addEnseignant(Enseignant enseignant) async {
    try {
      EnseignantService enseignantService = EnseignantService();
      await enseignantService.addEnseignant(enseignant);

      setState(() {
        currentPage = 0;
        // teachers.add(enseignant);
      });
      loadTeachers(currentPage);
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Enseignant ajouté avec succès!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
    } catch (e) {
      print('Failed to add enseignant: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Échec de l\'ajout de l\'enseignant'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  void showAddEnseignantDialog() {
    final _formKey = GlobalKey<FormState>();
    final _nomEnseignantController = TextEditingController();
    final _prenomEnseignantController = TextEditingController();
    final _nomArabeEnseignantController = TextEditingController();
    final _cinEnseignantController = TextEditingController();
    final _numeroTelephoneEnseignantController = TextEditingController();
    final _matriculeEnseignantController = TextEditingController();
    final _dateEmbaucheController = TextEditingController();
    final _dateDebutContratEnseignantController = TextEditingController();
    final _dateFinContratEnseignantController = TextEditingController();
    bool _chefFamille = false;
    Sexe? _selectedSexe = Sexe.Homme;
    Contrat? _selectedContrat = Contrat.SansContrat;
    int _nombreHeurEnseignant = 25;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return AlertDialog(
              backgroundColor: Colors.white,
              title: Text('Ajouter un Enseignant',style: TextStyle(color: Colors.blue.shade800),),//color: Colors.black)),//),),
              content: Theme(
            data: Theme.of(context).copyWith(
            inputDecorationTheme: InputDecorationTheme(
            focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.blue),
            ),
            enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.blue.shade100),
            ),
            labelStyle: TextStyle(color: Colors.black.withOpacity(0.6)),//blue.shade800),
            ),
            ),
            child: Container(
                width: double.maxFinite,
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(height: 10),
                        TextFormField(
                          controller: _cinEnseignantController,
                          decoration: InputDecoration(labelText: 'CIN'),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer un cin';
                            }
                            if (value.length != 8) {
                              return 'Le numéro CIN doit contenir 8 chiffres';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 10),
                        TextFormField(
                          controller: _nomEnseignantController,
                          decoration:
                              InputDecoration(labelText: 'Nom d\'utilisateur'),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer un nom d\'utilisateur';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 10),
                        TextFormField(
                          controller: _prenomEnseignantController,
                          decoration: InputDecoration(labelText: 'Prénom'),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer un prénom';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 10),
                        TextFormField(
                          controller: _nomArabeEnseignantController,
                          decoration: InputDecoration(labelText: 'Nom Arabe'),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer un nom arabe';
                            }
                            return null;
                          },
                        ),

                        SizedBox(height: 10),
                        TextFormField(
                          controller: _numeroTelephoneEnseignantController,
                          decoration: InputDecoration(labelText: 'Téléphone'),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer un numéro téléphone';
                            }
                            if (value.length != 8) {
                              return 'Le numéro de téléphone doit contenir 8 chiffres';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 10),
                        TextFormField(
                          controller: _matriculeEnseignantController,
                          decoration: InputDecoration(labelText: 'Matricule'),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer un matricule';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 10),
                        TextFormField(
                          controller: _dateEmbaucheController,
                          decoration: InputDecoration(
                            labelText: 'Date d\'Embauche',
                            suffixIcon: Icon(Icons.calendar_today , color: Colors.lightBlue),
                          ),
                          readOnly: true,
                          onTap: () async {
                            DateTime? pickedDate = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime(2000),
                              lastDate: DateTime(2101),
                              builder: (context, child) {
                                return Theme(
                                  data: Theme.of(context).copyWith(
                                    colorScheme: ColorScheme.light(
                                      primary: Colors.lightBlue,//Color(0xFF1565C0), // = Colors.blue.shade800
                                      onPrimary: Colors.white,
                                      onSurface: Colors.black87, // Texte dans le calendrier
                                    ),
                                    dialogBackgroundColor: Colors.white,
                                  ),
                                  child: child!,
                                );
                              },
                            );

                            if (pickedDate != null) {
                              String formattedDate =
                                  DateFormat('yyyy-MM-dd').format(pickedDate);
                              _dateEmbaucheController.text = formattedDate;

                            }
                          },
                          validator: (value) {
                            return null;
                          },
                        ),
                        SizedBox(height: 10),
                    Theme(
                      data: Theme.of(context).copyWith(
                        canvasColor: Colors.white,
                        inputDecorationTheme: InputDecorationTheme(
                          focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.blue),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.blue.shade100),
                          ),
                          labelStyle: TextStyle(color: Colors.black.withOpacity(0.6)),//blue.shade800),
                        ),
                      ),

                      child:DropdownButtonFormField<Sexe>(
                          decoration: InputDecoration(labelText: 'Sexe'),
                          value: _selectedSexe,
                          items: Sexe.values.map((Sexe sexe) {
                            return DropdownMenuItem<Sexe>(

                              value: sexe,
                              child: Text(sexe.name),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedSexe = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'Veuillez sélectionner un sexe';
                            }
                            return null;
                          },
                        ),),
                        SizedBox(height: 10),
                        Theme(
                          data: Theme.of(context).copyWith(
                            canvasColor: Colors.white,
                            inputDecorationTheme: InputDecorationTheme(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.blue),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.blue.shade100),
                              ),
                              labelStyle: TextStyle(color: Colors.black.withOpacity(0.6)),//blue.shade800),
                            ),
                          ),

                          child:
                        DropdownButtonFormField<Contrat>(
                          decoration:
                              InputDecoration(labelText: 'Type de Contrat',),//labelStyle:TextStyle(color: Colors.blue) ),
                          value: _selectedContrat,
                          items: Contrat.values.map((Contrat contrat) {
                            return DropdownMenuItem<Contrat>(
                              value: contrat,
                              child: Text(contrat.name),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedContrat = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'Veuillez sélectionner un type de contrat';
                            }
                            return null;
                          },
                        ),),
                        SizedBox(height: 10),

                        // Conditional rendering with ternary operator
                        _selectedContrat == Contrat.CIVP ||
                                _selectedContrat == Contrat.CDD ||
                                _selectedContrat == Contrat.ContratKARAMA
                            ? Column(
                                children: [
                                  TextFormField(
                                    controller:
                                        _dateDebutContratEnseignantController,
                                    decoration: InputDecoration(

                                      labelText: 'Date Debut Contrat',
                                      suffixIcon: Icon(Icons.calendar_today,color: Colors.lightBlue,),
                                    ),
                                    readOnly: true,
                                    onTap: () async {
                                      DateTime? pickedDate =
                                          await showDatePicker(
                                        context: context,
                                        initialDate: DateTime.now(),
                                        firstDate: DateTime(2000),
                                        lastDate: DateTime(2101),
                                            builder: (context, child) {
                                              return Theme(
                                                data: Theme.of(context).copyWith(
                                                  colorScheme: ColorScheme.light(
                                                    primary: Colors.lightBlue,//Color(0xFF1565C0), // = Colors.blue.shade800
                                                    onPrimary: Colors.white,
                                                    onSurface: Colors.black87, // Texte dans le calendrier
                                                  ),
                                                  dialogBackgroundColor: Colors.white,
                                                ),
                                                child: child!,
                                              );
                                            },
                                      );

                                      if (pickedDate != null) {
                                        String formattedDate =
                                            DateFormat('yyyy-MM-dd')
                                                .format(pickedDate);
                                        _dateDebutContratEnseignantController
                                            .text = formattedDate;
                                      }
                                    },
                                    validator: (value) {
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 10),
                                  TextFormField(
                                    controller:
                                        _dateFinContratEnseignantController,
                                    decoration: InputDecoration(
                                      labelText: 'Date Fin Contrat',
                                      suffixIcon: Icon(Icons.calendar_today, color: Colors.lightBlue,),
                                    ),
                                    readOnly: true,
                                    onTap: () async {
                                      DateTime? pickedDate =
                                          await showDatePicker(
                                        context: context,
                                        initialDate: DateTime.now(),
                                        firstDate: DateTime(2000),
                                        lastDate: DateTime(2101),
                                            builder: (context, child) {
                                              return Theme(
                                                data: Theme.of(context).copyWith(
                                                  colorScheme: ColorScheme.light(
                                                    primary: Colors.lightBlue,//Color(0xFF1565C0), // = Colors.blue.shade800
                                                    onPrimary: Colors.white,
                                                    onSurface: Colors.black87, // Texte dans le calendrier
                                                  ),
                                                  dialogBackgroundColor: Colors.white,
                                                ),
                                                child: child!,
                                              );
                                            },
                                      );

                                      if (pickedDate != null) {
                                        String formattedDate =
                                            DateFormat('yyyy-MM-dd')
                                                .format(pickedDate);
                                        _dateFinContratEnseignantController
                                            .text = formattedDate;

                                      }
                                    },
                                    validator: (value) {
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 10),
                                ],
                              )
                            : Container(), // Empty container when condition is false

                        SwitchListTile(activeColor: Colors.lightBlue,
                          title: Text('Chef de Famille', style: TextStyle( color:Colors.blue.shade800),),
                          value: _chefFamille,
                          onChanged: (value) {
                            setState(() {
                              _chefFamille = value;
                            });
                          },
                        ),
                        SizedBox(height: 10),
                        TextFormField(
                          initialValue: _nombreHeurEnseignant.toString(),
                          decoration:
                              InputDecoration(labelText: 'Nombre d\'Heures'),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            _nombreHeurEnseignant = int.tryParse(value) ?? 25;
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),),
              actions: [
                OutlinedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Color(0xFF4099FF), // Texte bleu
                    side: BorderSide(color: Color(0xFF4099FF)), // Bordure bleue
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  child: Text('Annuler'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      final newEnseignant = Enseignant(
                        nomEnseignant: _nomEnseignantController.text,
                        prenomEnseignant: _prenomEnseignantController.text,
                        nomArabeEnseignant: _nomArabeEnseignantController.text,
                        cinEnseignant: _cinEnseignantController.text,
                        numeroTelephoneEnseignant:
                        _numeroTelephoneEnseignantController.text,
                        matriculeEnseignant: _matriculeEnseignantController.text,
                        dateEmbaucheEnseignant: _dateEmbaucheController.text,
                        dateDebutContratEnseignant:
                        _selectedContrat == Contrat.CIVP ||
                            _selectedContrat == Contrat.CDD ||
                            _selectedContrat == Contrat.ContratKARAMA
                            ? _dateDebutContratEnseignantController.text
                            : null,
                        dateFinContratEnseignant:
                        _selectedContrat == Contrat.CIVP ||
                            _selectedContrat == Contrat.CDD ||
                            _selectedContrat == Contrat.ContratKARAMA
                            ? _dateFinContratEnseignantController.text
                            : null,
                        sexeEnseignant: _selectedSexe!,
                        typeContratEnseignant: _selectedContrat!,
                        chefFamille: _chefFamille,
                        nombreHeurEnseignant: _nombreHeurEnseignant,
                      );

                      addEnseignant(newEnseignant);
                      Navigator.of(context).pop();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF4099FF), // Bleu un peu désaturé
                    foregroundColor: Colors.white, // Texte blanc
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  child: Text('Ajouter'),
                ),
              ],

            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Liste des Enseignants',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 10),
          isLoading
              ? Center(child: CircularProgressIndicator())
              : teachers.isEmpty
                  ? Center(
                      child: Text('Aucun enseignant disponible',
                          style: TextStyle(fontSize: 18, color: Colors.grey)))
                  : Column(
                      children: teachers.map((teacher) {
                        return Container(
                          margin: EdgeInsets.symmetric(vertical: 5),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(5),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.5),
                                spreadRadius: 2,
                                blurRadius: 3,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ListTile(
                            title: Text(
                                '${teacher.nomEnseignant} ${teacher.prenomEnseignant}'),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // IconButton(
                                //   icon: Icon(Icons.edit, color: Color(0xFF8FE178)),
                                //   onPressed: () {
                                //     // Logique pour modifier l'teacher
                                //     // Peut-être afficher une boîte de dialogue ou une nouvelle page
                                //   },
                                // ),
                                // IconButton(
                                //   icon:
                                //       Icon(Icons.delete, color: Color(0xFFE17878)),
                                //   onPressed: () {
                                //     showDialog(
                                //       context: context,
                                //       builder: (BuildContext context) {
                                //         return AlertDialog(
                                //           title: Text("Confirmation"),
                                //           content: Text(
                                //               "Voulez-vous vraiment supprimer ce teacher ?"),
                                //           actions: [
                                //             TextButton(
                                //               onPressed: () {
                                //                 Navigator.of(context).pop();
                                //               },
                                //               child: Text("Annuler",  style: TextStyle(color: Color(0xFF4099FF)),),
                                //             ),
                                //             TextButton(
                                //               onPressed: () {
                                //                 // Logique pour supprimer le teacher
                                //                 // Peut-être appeler une méthode de service
                                //                 Navigator.of(context).pop();
                                //               },
                                //               child: Text("Supprimer",  style: TextStyle(color: Color(0xFF4099FF)),),
                                //             ),
                                //           ],
                                //         );
                                //       },
                                //     );
                                //   },
                                // ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
          SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.all(4.0), // Réduit le padding du bas
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(Icons.arrow_back_ios,
                      color: currentPage > 0 ? Color(0xFF4099FF) : Colors.grey,
                      size: 15),
                  onPressed: currentPage > 0
                      ? () {
                          _updatePageIndex(currentPage - 1);
                        }
                      : null,
                ),
                Text(
                  '${currentPage + 1} / $totalPages',
                  style: TextStyle(color: Colors.grey, fontSize: 15),
                ),
                IconButton(
                  icon: Icon(Icons.arrow_forward_ios,
                      color: currentPage < totalPages - 1
                          ? Color(0xFF4099FF)
                          : Colors.grey,
                      size: 15),
                  onPressed: currentPage < totalPages - 1
                      ? () {
                          _updatePageIndex(currentPage + 1);
                        }
                      : null,
                ),
              ],
            ),
          ),
          SizedBox(height: 10),
          ElevatedButton(
            onPressed: showAddEnseignantDialog,
            child: Text('Ajouter un Enseignant', ),
          ),
        ],
      ),
    );
  }
}
