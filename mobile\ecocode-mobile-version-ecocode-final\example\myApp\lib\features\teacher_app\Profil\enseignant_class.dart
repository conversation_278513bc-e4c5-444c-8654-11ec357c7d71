enum Sexe { <PERSON><PERSON>, Femme }

enum Contrat { Sans<PERSON><PERSON>rat, CIVP, CDD, CD<PERSON>, ContratKARAMA, <PERSON><PERSON><PERSON>, <PERSON><PERSON> }

class Enseignant {
  final int? idEnseignant;
  final String? nomEnseignant;
  final String? prenomEnseignant;
  final String? nomArabeEnseignant;
  final String? cinEnseignant;
  final String? numeroTelephoneEnseignant;
  final Contrat? typeContratEnseignant;
  final String? matriculeEnseignant;
  final String? dateEmbaucheEnseignant;
  final Sexe? sexeEnseignant;
  final bool? chefFamille;
  final int? nombreHeurEnseignant;
  final String? dateDebutContratEnseignant;
  final String? dateFinContratEnseignant;

  Enseignant({
    this.idEnseignant,
    this.nomEnseignant,
    this.prenomEnseignant,
    this.nomArabeEnseignant,
    this.cinEnseignant,
    this.numeroTelephoneEnseignant,
    this.typeContratEnseignant,
    this.matriculeEnseignant,
    this.dateEmbaucheEnseignant,
    this.sexeEnseignant,
    this.chefFamille = false,
    this.nombreHeurEnseignant = 25,
    this.dateDebutContratEnseignant,
    this.dateFinContratEnseignant,
  });

  factory Enseignant.fromJson(Map<String, dynamic> json) {
    return Enseignant(
      idEnseignant: json['idEnseignant'],
      nomEnseignant: json['nomEnseignant'],
      prenomEnseignant: json['prenomEnseignant'],
      nomArabeEnseignant: json['nomArabeEnseignant'],
      cinEnseignant: json['cinEnseignant'],
      numeroTelephoneEnseignant: json['numeroTelephoneEnseignant'],
      matriculeEnseignant: json['matriculeEnseignant'],
      dateEmbaucheEnseignant: json['dateEmbaucheEnseignant'],
      sexeEnseignant: _sexeFromString(json['sexeEnseignant']),
      typeContratEnseignant: _contratFromString(json['typeContratEnseignant']),
      chefFamille: json['chefFamille'] ?? false,
      nombreHeurEnseignant: (json['nombreHeurEnseignant'] is double)
        ? (json['nombreHeurEnseignant'] as double).round() 
        : json['nombreHeurEnseignant'] ?? 25,
      dateDebutContratEnseignant: json['dateDebutContratEnseignant'],
      dateFinContratEnseignant: json['dateFinContratEnseignant'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'idEnseignant': idEnseignant,
      'nomEnseignant': nomEnseignant,
      'prenomEnseignant': prenomEnseignant,
      'nomArabeEnseignant': nomArabeEnseignant,
      'cinEnseignant': cinEnseignant,
      'numeroTelephoneEnseignant': numeroTelephoneEnseignant,
      'matriculeEnseignant': matriculeEnseignant,
      'dateEmbaucheEnseignant': dateEmbaucheEnseignant,
      'sexeEnseignant': sexeEnseignant?.toString().split('.').last,
      'typeContratEnseignant': typeContratEnseignant?.toString().split('.').last,
      'chefFamille': chefFamille,
      'nombreHeurEnseignant': nombreHeurEnseignant,
      'dateDebutContratEnseignant': dateDebutContratEnseignant,
      'dateFinContratEnseignant': dateFinContratEnseignant,
    };
  }

  static Sexe? _sexeFromString(String? sexe) {
    if (sexe == null) return null;
    return Sexe.values.firstWhere(
        (e) => e.toString().split('.').last.toLowerCase() == sexe.toLowerCase(),
        orElse: () => Sexe.Homme); // Default to Homme if not found
  }

  static Contrat? _contratFromString(String? contrat) {
    if (contrat == null) return null;
    return Contrat.values.firstWhere(
        (e) => e.toString().split('.').last.toLowerCase() == contrat.toLowerCase(),
        orElse: () => Contrat.Autre); // Default to Autre if not found
  }
}
