import { Routes } from '@angular/router';

export const parentRoutes: Routes = [
  {
    path: '',
    loadComponent: () => import('./layout/parent-layout.component').then(m => m.ParentLayoutComponent),
    children: [
      {
        path: '',
        redirectTo: 'courses',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./courses/parent-courses.component').then(m => m.ParentCoursesComponent)
      },
      {
        path: 'courses',
        loadComponent: () => import('./courses/parent-courses.component').then(m => m.ParentCoursesComponent)
      },
      {
        path: 'exercises',
        loadComponent: () => import('./exercises/parent-exercises.component').then(m => m.ParentExercisesComponent)
      },
      {
        path: 'observations',
        loadComponent: () => import('./observations/parent-observations.component').then(m => m.ParentObservationsComponent)
      },
      {
        path: 'discipline',
        loadComponent: () => import('./discipline/parent-discipline.component').then(m => m.ParentDisciplineComponent)
      },
      {
        path: 'schedule',
        loadComponent: () => import('./schedule/parent-schedule.component').then(m => m.ParentScheduleComponent)
      },
      {
        path: 'grades',
        loadComponent: () => import('./grades/parent-grades.component').then(m => m.ParentGradesComponent)
      },
      {
        path: 'payments',
        loadComponent: () => import('./payments/parent-payments.component').then(m => m.ParentPaymentsComponent)
      },
      {
        path: 'attendance',
        loadComponent: () => import('./attendance/parent-attendance.component').then(m => m.ParentAttendanceComponent)
      },
      {
        path: 'messages',
        loadComponent: () => import('./messages/parent-messages.component').then(m => m.ParentMessagesComponent)
      },
      {
        path: 'profile',
        loadComponent: () => import('./profile/parent-profile.component').then(m => m.ParentProfileComponent)
      }
    ]
  }
];
