import 'dart:ui';

import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:NovaSchool/features/parent_app/chat/chat_Pscreen.dart';
import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:NovaSchool/commun/parent_app/app_contact.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../features/parent_app/Home/home_Pscreen.dart';

import '../../features/parent_app/profil/profil_Pscreen.dart';
import '../../utils/constants/colors.dart';

class PNavigationController extends GetxController {
  final Rx<int> selectedIndex = 0.obs;
  final Rx<int> eleveId = 0.obs;
  final Rx<String> nomClasse = ''.obs;
  final Rx<ChildProfile?> selectedProfile = Rx<ChildProfile?>(null);

  PNavigationController() {
    _loadEleveId();
  }

  Future<void> _loadEleveId() async {
    final prefs = await SharedPreferences.getInstance();
    eleveId.value = prefs.getInt('eleveId') ?? 0;
    nomClasse.value = prefs.getString('nomClasse') ?? '';
  }

  // Method to set the selected profile
  void setSelectedProfile(ChildProfile profile) {
    selectedProfile.value = profile;
    _saveProfileToLocalStorage(profile);
  }

  // Method to save selected profile to local storage
  Future<void> _saveProfileToLocalStorage(ChildProfile profile) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt('eleveId', profile.id);
    await prefs.setString('eleveName', profile.name);
    await prefs.setString('eleveFirstName', profile.firstName);
    await prefs.setString('nomClasse', profile.nomClasse);
  }

  // Méthode pour charger l'ID de l'élève depuis SharedPreferences
  // Future<void> _loadEleveId() async {
  //   final prefs = await SharedPreferences.getInstance();
  //   final id = prefs.getInt('eleveId') ?? 0;
  //   final savedNomClasse = prefs.getString('nomClasse') ?? '';
  //   eleveId.value = id;
  //   nomClasse.value = savedNomClasse;
  // }

  // Reset selectedIndex to 0 (Home Screen)
  void resetToHome() {
    selectedIndex.value = 0;
  }

  List<Widget> get screens => [
    PHomeScreen(),
    // PChatScreen(),
    Obx(() => PProfilScreen(
      eleveId: eleveId.value,
      nomClasse: nomClasse.value,
    )),
    PContactScreen(),
    PChatScreen(),
  ];
}
class ParentAppNavBar extends StatefulWidget {
  @override
  _ParentAppNavBarState createState() => _ParentAppNavBarState();
}

class _ParentAppNavBarState extends State<ParentAppNavBar> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final PNavigationController controller = Get.put(PNavigationController());

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this, // Using TickerProvider
      duration: Duration(milliseconds: 200),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedIndex.value != 0) {
          controller.selectedIndex.value = 0; // Retour à PHomeScreen
          return false;
        } else {
          return true;
        }
      },
      ////code1
      child: Scaffold(
      bottomNavigationBar: CurvedNavigationBar(

      backgroundColor: Color(0xFFF2F2F2), // Fond transparent pour effet flottant
      color:Colors.lightBlueAccent.shade400, // Couleur principale
      buttonBackgroundColor: AppColors.primary.withOpacity(0.3),//AppColors.accent, // Couleur du bouton sélectionné
      height: 65,
      animationDuration: Duration(milliseconds: 300),
      items: [
      Icon(Icons.home, size: 30, color: Colors.white),
      Icon(Icons.person, size: 30, color: Colors.white),
      Icon(Icons.contact_page, size: 30, color: Colors.white),
      Icon(Icons.chat, size: 30, color: Colors.white),
    ],
    onTap: (index) {
    controller.selectedIndex.value = index;
    },
    ),
    body: Obx(() {
    final currentIndex = controller.selectedIndex.value;
    if (currentIndex < controller.screens.length) {
    return controller.screens[currentIndex];
    }
    return Container();
    }),
    ),);
  }
}

/*
class ParentAppNavBar extends StatelessWidget {
  const ParentAppNavBar({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PNavigationController());

    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedIndex.value != 0) {
          controller.selectedIndex.value = 0; // Retour à PHomeScreen
          return false;
        } else {
          return true;
        }
      },
      child: Scaffold(
        bottomNavigationBar: Obx(() => NavigationBar(
          backgroundColor: AppColors.light,
          indicatorColor: AppColors.accent.withOpacity(0.5),
          height: 65,
          elevation: 0,
          selectedIndex: controller.selectedIndex.value,
          onDestinationSelected: (index) =>
          controller.selectedIndex.value = index,

          destinations: [
            NavigationDestination(
              icon: Image.asset(
                AppImages.HomeIcon,
                width: 30,
                height: 30,
                semanticLabel: 'Accueil',
                color: controller.selectedIndex.value == 0
                    ? AppColors.primary
                    : Colors.grey,
              ),
              label: 'Accueil', // String is used here
              tooltip: '',


            ),
            NavigationDestination(
              icon: Image.asset(
                AppImages.profilIcon,
                width: 30,
                height: 30,
                semanticLabel: 'Profil',
                color: controller.selectedIndex.value == 1
                    ? AppColors.primary
                    : Colors.grey,
              ),
              label: 'Profil',
              tooltip: '',

            ),
            NavigationDestination(
              icon: Icon(Icons.contact_page,
                size: 30,

                color: controller.selectedIndex.value == 2
                    ? AppColors.primary
                    : Colors.grey,),
              label: 'Contact',
              tooltip: '',
            ),
            NavigationDestination(
              icon: Icon(Icons.chat,
                size: 30,
                color: controller.selectedIndex.value == 3
                    ? AppColors.primary
                    : Colors.grey,),
              label: 'Messagerie',

              tooltip: '',
            ),
          ],
        )),
        body: Obx(() {
          final currentIndex = controller.selectedIndex.value;
          if (currentIndex < controller.screens.length) {
            return controller.screens[currentIndex];
          }
          return Container();
        }),
      ),
    );
  }
}*/

/* @override
  Widget build(BuildContext context) {
    final controller = Get.put(PNavigationController());

    return WillPopScope(
      onWillPop: () async {
        print("onWillPop appelé");
        if (controller.selectedIndex.value != 0) {
          controller.selectedIndex.value = 0;
          return false;
        }
        return true;
      },
      child: Scaffold(
        bottomNavigationBar: Obx(
              () => Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildNavItem(Icons.home, 'Accueil', 0, controller),
                _buildNavItem(Icons.contact_page, 'Contact', 1, controller),

                _buildNavItem(Icons.person, 'Profil', 2, controller),
                _buildNavItem(Icons.message, 'Messagerie', 3, controller),
              ],
            ),
          ),
        ),
        body: Obx(() {
          final currentIndex = controller.selectedIndex.value;
          if (controller.screens.isNotEmpty && currentIndex < controller.screens.length) {
            return controller.screens[currentIndex];
          }
          return Center(child: CircularProgressIndicator()); // Ajout d'une sécurité
        }),
        /*body: Obx(() {
          final currentIndex = controller.selectedIndex.value;
          return controller.screens[currentIndex];
        }),*/
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, int index, PNavigationController controller,
      {bool showBadge = false, bool isChat = false}) {
    bool isSelected = controller.selectedIndex.value == index;

    return GestureDetector(
      onTap: () {
        if (isChat) {
          Navigator.push(
            Get.context!,
            MaterialPageRoute(builder: (context) => PChatScreen()),
          );
        } else {
          controller.selectedIndex.value = index;};},
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedContainer(
            duration: Duration(milliseconds: 300),
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 7),
            decoration: BoxDecoration(
              color: isSelected ? AppColors.primary.withOpacity(0.2) : Colors.transparent,
              borderRadius: BorderRadius.circular(100),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Icon(
                  icon,
                  size: isSelected ? 30 : 26,
                  color: isSelected ? AppColors.primary : Colors.grey,
                ),
                /*if (showBadge)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        '0', // Ici, vous pouvez afficher le vrai nombre de messages non lus
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),*/
              ],
            ),
          ),
          SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isSelected ? AppColors.primary : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

}*/



/***import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:NovaSchool/commun/parent_app/app_contact.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../features/parent_app/Home/home_Pscreen.dart';

import '../../features/parent_app/profil/profil_Pscreen.dart';
import '../../utils/constants/colors.dart';

class PNavigationController extends GetxController { 
  final Rx<int> selectedIndex = 0.obs;
  final Rx<int> eleveId = 0.obs; 
  final Rx<String> nomClasse = ''.obs; 
   final Rx<ChildProfile?> selectedProfile = Rx<ChildProfile?>(null);

  PNavigationController() {
    _loadEleveId();
  }

    Future<void> _loadEleveId() async {
    final prefs = await SharedPreferences.getInstance();
    eleveId.value = prefs.getInt('eleveId') ?? 0;
    nomClasse.value = prefs.getString('nomClasse') ?? '';
  }

  // Method to set the selected profile
  void setSelectedProfile(ChildProfile profile) {
    selectedProfile.value = profile;
    _saveProfileToLocalStorage(profile);
  }

  // Method to save selected profile to local storage
  Future<void> _saveProfileToLocalStorage(ChildProfile profile) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt('eleveId', profile.id);
    await prefs.setString('eleveName', profile.name);
    await prefs.setString('eleveFirstName', profile.firstName);
    await prefs.setString('nomClasse', profile.nomClasse);
  }

  // Méthode pour charger l'ID de l'élève depuis SharedPreferences
  // Future<void> _loadEleveId() async {
  //   final prefs = await SharedPreferences.getInstance();
  //   final id = prefs.getInt('eleveId') ?? 0;
  //   final savedNomClasse = prefs.getString('nomClasse') ?? '';
  //   eleveId.value = id;
  //   nomClasse.value = savedNomClasse;
  // }

  List<Widget> get screens => [
        PHomeScreen(),
        // PChatScreen(),
        Obx(() => PProfilScreen(
              eleveId: eleveId.value,
              nomClasse: nomClasse.value,
            )),
        PContactScreen(),
      ];
}

class ParentAppNavBar extends StatelessWidget {
  const ParentAppNavBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PNavigationController());

    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedIndex.value != 0) {
          controller.selectedIndex.value = 0; // Retour à PHomeScreen
          return false;
        } else {
          return true;
        }
      },
      child: Scaffold(
        bottomNavigationBar: Obx(() => NavigationBar(
              backgroundColor: AppColors.light,
              indicatorColor: AppColors.accent.withOpacity(0.5),
              height: 65,
              elevation: 0,
              selectedIndex: controller.selectedIndex.value,
              onDestinationSelected: (index) =>
                  controller.selectedIndex.value = index,
              destinations: [
                NavigationDestination(
                  icon: Image.asset(
                    AppImages.HomeIcon,
                    width: 30,
                    height: 30,
                    semanticLabel: 'Accueil',
                  ),
                  label: 'Accueil',
                ),
                NavigationDestination(
                  icon: Image.asset(
                    AppImages.profilIcon,
                    width: 30,
                    height: 30,
                    semanticLabel: 'Profil',
                  ),
                  label: 'Profil',
                ),
                NavigationDestination(
                  icon: Icon(Icons.contact_page,
                      size: 30,
                      color: Color(
                          0xFF4099FF)), 
                  label: 'Contact',
                ),
              ],
            )),
        body: Obx(() {
          final currentIndex = controller.selectedIndex.value;
          if (currentIndex < controller.screens.length) {
            return controller.screens[currentIndex];
          }
          return Container(); 
        }),
      ),
    );
  }
}
    **/
