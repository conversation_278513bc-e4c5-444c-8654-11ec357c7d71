class EleveParListClassesDTO {
  final String nomEleve;
  final String prenomEleve;
  final int idClasse;
  final String nomClasse;
  final String niveauClasse;
  final String nomArabeClasse;
  final int idEleve;

  EleveParListClassesDTO({
    required this.nomEleve,
    required this.prenomEleve,
    required this.idClasse,
    required this.nomClasse,
    required this.niveauClasse,
    required this.nomArabeClasse,
    required this.idEleve,
  });

  factory EleveParListClassesDTO.fromJson(Map<String, dynamic> json) {
    return EleveParListClassesDTO(
      nomEleve: json['nomEleve'],
      prenomEleve: json['prenomEleve'],
      idClasse: json['idClasse'],
      nomClasse: json['nomClasse'],
      niveauClasse: json['niveauClasse'],
      nomArabeClasse: json['nomArabeClasse'] ?? '',
      idEleve: json['idEleve'],
    );
  }
}
