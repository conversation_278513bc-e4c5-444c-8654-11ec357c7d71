package com.ecocode.controller;

import org.springframework.web.bind.annotation.*;

import com.ecocode.dto.NotificationRequest;
import com.ecocode.service.NotificationService;

@RestController
@RequestMapping("/notifications")
public class NotificationController {
    private final NotificationService notificationService;

    public NotificationController(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

   /* @PostMapping("/send")
    public String sendNotification(@RequestParam String token, @RequestParam String title, @RequestParam String body) throws Exception {
        return notificationService.sendNotification(token, title, body);
    	 //return "Notification envoyée avec succès !";
    }*/
    @PostMapping("/send")
    public String sendNotification( @RequestBody NotificationRequest request) throws Exception {
        return notificationService.sendNotification(request.getToken(), request.getTitle(), request.getBody());
    }
}
