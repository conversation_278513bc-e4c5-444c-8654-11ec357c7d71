import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:NovaSchool/features/admin/A_appDrawer.dart';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/admin/profil/profil_AImage.dart';
import 'package:NovaSchool/features/admin/profil/profil_Aform.dart';
import 'package:NovaSchool/features/chat/test.dart';
import '../../../commun/app_background.dart';
import 'admin_class.dart';

class AProfilScreen extends StatefulWidget {
  final String firstName;
  final String lastName;
  final String email;
  final String profile;

  const AProfilScreen({
    Key? key,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.profile
  }) : super(key: key);

  @override
  _AProfilScreenState createState() => _AProfilScreenState();
}
Future<Map<String, String>> _getUserInfo() async {
  return await AuthService().getSenderDetails();
}
class _AProfilScreenState extends State<AProfilScreen> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AdminDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            iconTheme: IconThemeData(
              color: Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            title: GestureDetector(
              /*onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => AdminAppNavBar()), // remplace THomeScreen par ton écran d'accueil
                );
              },*/
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),
                ],
              ),
            ),/*Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/logos/ecocode.png',
                  height: 150,
                  color: Colors.white,
                ),
                SizedBox(width: 10),

              ],
            ),*/
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
             /* IconButton(
                icon: Icon(Icons.person_outline, color: Colors.white,size: 30,),
                onPressed: () async {
                  Map<String, String> userInfo = await _getUserInfo();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AProfilScreen(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                        email: userInfo['email'] ?? '',
                        profile: userInfo['profile'] ?? '',
                      ),
                    ),
                  );
                },
              ),*/
              /*IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  onPressed: () {},
                ),*/
            ],
          ),
        ),
      ),

      body: AppBackground(
        child: Column(
          children: [
            // SecondHeadBar(title: 'Profil'),
            /*PrimaryHeaderContainer(
              child: Container(
                height: 100, // Adjust the height here
                child: SecondHeadBar(
                  title: 'Profil',
                  titleColor: Colors.white,
                  iconColor: Colors.white,
                ),
              ),
            ),*/
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),),
            SizedBox(height: 20,),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    AProfilImage(
                      firstName: widget.firstName,
                      lastName: widget.lastName,
                    ),
                    SizedBox(height: 10,),
                    AProfilForm(
                      firstName: widget.firstName,
                      lastName: widget.lastName,
                      email: widget.email,
                      profile: widget.profile,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
