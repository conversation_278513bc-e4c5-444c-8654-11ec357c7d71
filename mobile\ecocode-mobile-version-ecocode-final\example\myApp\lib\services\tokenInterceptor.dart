import 'package:NovaSchool/services/auth_service.dart';
import 'package:http_interceptor/http_interceptor.dart';


class TokenInterceptor implements InterceptorContract {
  final AuthService authService = AuthService();

  @override
  Future<RequestData> interceptRequest({required RequestData data}) async {
    final token = await authService.getToken();
    
    data.headers['Authorization'] = 'Bearer $token';
    return data;
  }

  @override
  Future<ResponseData> interceptResponse({required ResponseData data}) async {
    return data;
  }
}