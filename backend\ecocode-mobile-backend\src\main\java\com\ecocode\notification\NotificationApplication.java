package com.ecocode.notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
/*
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan("com.ecocode") // S'assurer que tous les composants sont scannés
public class NotificationApplication {
	
    public static void main(String[] args) {
        SpringApplication.run(NotificationApplication.class, args);
    }
}*/
@SpringBootApplication
@ComponentScan("com.ecocode")
public class NotificationApplication {
    private static final Logger logger = LoggerFactory.getLogger(NotificationApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(NotificationApplication.class, args);
        logger.info("🔹🔹 Application Notification démarée avec succès 🔹🔹");
    }
}