/***import 'package:EssiddikPrivee/commun/parent_app/app_PheadBar.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/home_Pwelcome.dart';
import 'package:EssiddikPrivee/models/EleveInfoPersoDTO.dart';
import 'package:EssiddikPrivee/services/auth_service.dart';
import 'package:EssiddikPrivee/services/eleve_services.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppDrawer extends StatefulWidget {
  @override
  _AppDrawerState createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  late Future<List<ChildProfile>> _childProfilesFuture;

  @override
  void initState() {
    super.initState();
    _childProfilesFuture = _fetchChildProfiles();
  }

  Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
      return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
      List<EleveInfoPersoDTO> eleves =
      await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
      return eleves.map((eleve) {
        return ChildProfile(
            eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
      }).toList();
    } catch (e) {
      print('Erreur lors de la récupération des profils: $e');
      return [];
    }
  }

  void _navigateToScreen(int index) {
    switch (index) {
      case 0:
        Get.offAllNamed('/exercices');
        break;
      case 1:
        Get.offAllNamed('/cours');
        break;
      case 2:
        Get.offAllNamed('/observations');
        break;
      case 3:
        Get.offAllNamed('/discipline');
        break;
      case 4:
        Get.offAllNamed('/emploi');
        break;
      case 5:
        Get.offAllNamed('/bulletin');
        break;
      case 6:
        Get.offAllNamed('/paiement');
        break;
      case 7:
        Get.offAllNamed('/pointage');
        break;
      case 8:
        Get.offAllNamed('/notifications');
        break;

      case 9:
        Get.offAllNamed('/calendar');
        break;
      case 10:
        Get.offAllNamed('/settings');
        break;
      case 11:
        AuthService.logoutKeycloak();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          PAppHeadBar(),

          Padding(
            padding: const EdgeInsets.all(16.0),
            child: PHomeWelcome(),
          ),

          Expanded(
            child: ListView(
              children: [
                ListTile(
                  leading: Icon(Icons.assignment),
                  title: Text('Exercices'),
                  onTap: () => _navigateToScreen(0),
                ),
                ListTile(
                  leading: Icon(Icons.school),
                  title: Text('Cours'),
                  onTap: () => _navigateToScreen(1),
                ),
                ListTile(
                  leading: Icon(Icons.comment),
                  title: Text('Observations'),
                  onTap: () => _navigateToScreen(2),
                ),
                ListTile(
                  leading: Icon(Icons.gavel),
                  title: Text('Discipline'),
                  onTap: () => _navigateToScreen(3),
                ),
                ListTile(
                  leading: Icon(Icons.schedule),
                  title: Text('Emploi du Temps'),
                  onTap: () => _navigateToScreen(4),
                ),
                ListTile(
                  leading: Icon(Icons.grade),
                  title: Text('Bulletin'),
                  onTap: () => _navigateToScreen(5),
                ),
                ListTile(
                  leading: Icon(Icons.payment),
                  title: Text('Paiement'),
                  onTap: () => _navigateToScreen(6),
                ),
                ListTile(
                  leading: Icon(Icons.timelapse),
                  title: Text('Pointage'),
                  onTap: () => _navigateToScreen(7),
                ),
                ListTile(
                  leading: Icon(Icons.notifications),
                  title: Text('Notifications'),
                  onTap: () => _navigateToScreen(8),
                ),
                ListTile(
                  leading: Icon(Icons.history),
                  title: Text('Historique des Notes'),
                  onTap: () => _navigateToScreen(9),
                ),
                ListTile(
                  leading: Icon(Icons.history),
                  title: Text('Historique des Paiements'),
                  onTap: () => _navigateToScreen(10),
                ),
                ListTile(
                  leading: Icon(Icons.calendar_today),
                  title: Text('Calendrier'),
                  onTap: () => _navigateToScreen(11),
                ),
                ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Paramètres'),
                  onTap: () => _navigateToScreen(12),
                ),
                ListTile(
                  leading: Icon(Icons.logout, color: Colors.black),
                  title: Text('Déconnexion'),
                  onTap: () => _navigateToScreen(13),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}****/
/**************************
import 'package:EssiddikPrivee/commun/parent_app/app_PheadBar.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/carnet/carnet_Pscreen.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/home_Pwelcome.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/pedagogicalElement/cours/cours_Pscreen.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/pedagogicalElement/discipline/discipline_Pscreen.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/pedagogicalElement/exercice/exercice_Pscreen.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/pedagogicalElement/observation/observation_Pscreen.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/pedagogicalElement/paiement/paiement.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/pedagogicalElement/paiement/paiementManagement.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/pedagogicalElement/pointage/pointage.dart';
import 'package:EssiddikPrivee/models/EleveInfoPersoDTO.dart';
import 'package:EssiddikPrivee/services/auth_service.dart';
import 'package:EssiddikPrivee/services/eleve_services.dart';
import 'package:EssiddikPrivee/services/fileDownloadService.dart';
import 'package:EssiddikPrivee/services/message_services.dart';
import 'package:EssiddikPrivee/utils/constants/text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppDrawer extends StatefulWidget {
  @override
  _AppDrawerState createState() => _AppDrawerState();
}

int unreadExercices = 0;
int unreadCours = 0;
int unreadObservations = 0;
int unreadDisciplines = 0;
MessageServices msg = MessageServices();
AuthService auth = AuthService();
FileDownloadService fileDownloadService = FileDownloadService();
bool isLoading = false;

class _AppDrawerState extends State<AppDrawer> {
  late Future<List<ChildProfile>> _childProfilesFuture;

  @override
  void initState() {
    super.initState();
    _childProfilesFuture = _fetchChildProfiles();
  }

  Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
      return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
      List<EleveInfoPersoDTO> eleves =
      await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
      return eleves.map((eleve) {
        return ChildProfile(
            eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
      }).toList();
    } catch (e) {
      print('Erreur lors de la récupération des profils: $e');
      return [];
    }
  }

  void _navigateToScreen(int index) {
    switch (index) {
      case 0:
        Get.offAllNamed('/home');
        break;
      case 1:
        Get.offAllNamed('/profile');
        break;
      case 2:
        Get.offAllNamed('/contact');
        break;
      case 3:
        Get.offAllNamed('/settings');
        break;
      case 4:
        AuthService.logoutKeycloak();
        break;
    }
  }
  Future<void> _onMessageRead(String type) async {
    setState(() {
      if (type == 'Cours' && unreadCours > 0) {
        unreadCours--;
      } else if (type == 'Exercices' && unreadExercices > 0) {
        unreadExercices--;
      } else if (type == 'Observation' && unreadObservations > 0) {
        unreadObservations--;
      }else if (type == 'Discipline' && unreadDisciplines > 0) {
        unreadDisciplines--;
      }
    });
  }
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // 🟢 Barre d'en-tête personnalisée
          PAppHeadBar(),

          // 🟢 Texte de bienvenue personnalisé
          Padding(
            padding: const EdgeInsets.all(2),
            child: PHomeWelcome(),
          ),


          // 🟢 Liste des options du drawer
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                /*DrawerHeader(
                  decoration: BoxDecoration(
                    color: Colors.blue,
                  ),
                  child: Text(
                    'Menu',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                    ),
                  ),
                ),*/
                ListTile(
                  leading: Icon(Icons.book),
                  title: Text('Cours'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => PCoursScreen(onMessageRead: _onMessageRead)),
                    );
                  },
                ),
                ListTile(
                  leading: Icon(Icons.assignment),
                  title: Text('Exercices'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => PExerciceScreen(onMessageRead: _onMessageRead)),
                    );
                  },
                ),
                ListTile(
                  leading: Icon(Icons.schedule),
                  title: Text('Emploi du temps'),
                  onTap: () async {
                    final eleveId = await auth.getEleveId();
                    await fileDownloadService.displayImageByEleve(context, eleveId);
                  },
                ),
                ListTile(
                  leading: Icon(Icons.grade),
                  title: Text('Notes'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => PCarnetScreen()),
                    );
                  },
                ),
                ListTile(
                  leading: Icon(Icons.payment),
                  title: Text('Paiements'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => PaiementManagementPage()),
                    );
                  },
                ),

                ListTile(
                  leading: Icon(Icons.check_circle),
                  title: Text('Pointage'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => PPointageScreen()),
                    );
                  },
                ),


                ListTile(
                  leading: Icon(Icons.warning),
                  title: Text('Discipline'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => PDisciplineScreen(onMessageRead: _onMessageRead)),
                    );
                  },
                ),
                ListTile(
                  leading: Icon(Icons.visibility),
                  title: Text('Observations'),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => PObservationScreen(onMessageRead: _onMessageRead)),
                    );
                  },
                ),
                Divider(),
                ListTile(
                  leading: Icon(Icons.logout, color: Colors.black),
                  title: Text('Déconnexion'),
                  onTap: () => _navigateToScreen(4),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}*******************/
import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/features/parent_app/Home/carnet/carnet_Pscreen.dart';
import 'package:NovaSchool/features/parent_app/Home/home_Pscreen.dart';
import 'package:NovaSchool/features/parent_app/Home/home_Pwelcome.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/cours/cours_Pscreen.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/discipline/discipline_Pscreen.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/exercice/exercice_Pscreen.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/observation/observation_Pscreen.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/paiement/paiementManagement.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/eleve_services.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppDrawer extends StatefulWidget {
  @override
  _AppDrawerState createState() => _AppDrawerState();
}

int unreadExercices = 0;
int unreadCours = 0;
int unreadObservations = 0;
int unreadDisciplines = 0;
MessageServices msg = MessageServices();
AuthService auth = AuthService();
FileDownloadService fileDownloadService = FileDownloadService();
bool isLoading = false;

class _AppDrawerState extends State<AppDrawer> {
  late Future<List<ChildProfile>> _childProfilesFuture;
  final PNavigationController controller = Get.put(PNavigationController());

  @override
  void initState() {
    super.initState();
    _childProfilesFuture = _fetchChildProfiles();
  }

  Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
      return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
      List<EleveInfoPersoDTO> eleves =
      await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
      return eleves.map((eleve) {
        return ChildProfile(
            eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
      }).toList();
    } catch (e) {
      print('Erreur lors de la récupération des profils: $e');
      return [];
    }
  }

  void _navigateToScreen(int index) {
    switch (index) {
      case 0:
        Get.offAllNamed('/home');
        break;
      case 1:
        Get.offAllNamed('/profile');
        break;
      case 2:
        Get.offAllNamed('/contact');
        break;
      case 3:
        Get.offAllNamed('/settings');
        break;
      case 4:
        AuthService.logoutKeycloak();
        break;
    }
  }
  Future<void> _onMessageRead(String type) async {
    setState(() {
      if (type == 'Cours' && unreadCours > 0) {
        unreadCours--;
      } else if (type == 'Exercices' && unreadExercices > 0) {
        unreadExercices--;
      } else if (type == 'Observation' && unreadObservations > 0) {
        unreadObservations--;
      }else if (type == 'Discipline' && unreadDisciplines > 0) {
        unreadDisciplines--;
      }
    });
  }
  @override
  Widget build(BuildContext context) {

    return Drawer(
      backgroundColor:Color(0xFFF2F2F2),
      child: Column(
        children: [
          // En-tête du drawer adapté avec PHomeWelcome et PAppHeadBar
          Container(
            padding: EdgeInsets.all(1),
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: Column(
              children: [
                PAppHeadBar(),

                PHomeWelcome(),
                SizedBox(height: 20),
              ],
            ),
          ),

          // Liste des options
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(context, Icons.house, "Accueil", ParentAppNavBar(), isHome: true,),
                Divider(color: Colors.grey.shade300),
                _buildDrawerItem(context, Icons.book, "Cours", PCoursScreen(onMessageRead: _onMessageRead)),
                _buildDrawerItem(context, Icons.assignment, "Exercices", PExerciceScreen(onMessageRead: _onMessageRead)),
                _buildDrawerItem(context, Icons.schedule, "Emploi du temps", null, isFile: true),
                _buildDrawerItem(context, Icons.school, "Notes", PCarnetScreen()),
                _buildDrawerItem(context, Icons.payment, "Paiements", PaiementManagementPage()),
                //_buildDrawerItem(context, Icons.fingerprint, "Pointage", PPointageScreen()),
                _buildDrawerItem(context, Icons.warning, "Discipline", PDisciplineScreen(onMessageRead: _onMessageRead)),
                _buildDrawerItem(context, Icons.visibility, "Observations", PObservationScreen(onMessageRead: _onMessageRead)),
                //Divider(),
                //_buildDrawerItem(context, Icons.logout, "Déconnexion", null, isLogout: true),
              ],
            ),
          ),
          Divider(),
          Padding(
            padding: EdgeInsets.only(bottom: 16.0),
            child:
            _buildDrawerItem(context, Icons.logout, "Déconnexion", null, isLogout: true),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(BuildContext context, IconData icon, String title, Widget? screen, {bool isFile = false, bool isLogout = false, bool isHome = false}) {
    return ListTile(
      leading: Icon(icon, color: Color(0xFF0993D7)),
      title: Text(title, style: GoogleFonts.lato(fontSize: 16)),
      onTap: () async {
        Navigator.pop(context); // <<<<<< FERMER LE DRAWER D'ABORD

        await Future.delayed(Duration(milliseconds: 250)); // Petit délai pour éviter animation saccadée

        if (isLogout) {
          AuthService.logoutKeycloak();
        } else if (isFile) {
          final eleveId = await auth.getEleveId();
          await fileDownloadService.displayImageByEleve(context, eleveId);
        } else if (screen != null) {
          if (isHome) {
            controller.resetToHome();
          }
          Navigator.push(context, MaterialPageRoute(builder: (context) => screen));
        }
      },
      trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
    );
  }
}

/*import 'package:EssiddikPrivee/commun/parent_app/app_PheadBar.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/home_Pwelcome.dart';
import 'package:EssiddikPrivee/models/EleveInfoPersoDTO.dart';
import 'package:EssiddikPrivee/services/auth_service.dart';
import 'package:EssiddikPrivee/services/eleve_services.dart';
import 'package:EssiddikPrivee/utils/constants/images.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppDrawer extends StatefulWidget {
  final Function(int)? onItemSelected; // ✅ Rend onItemSelected optionnel
  final int selectedIndex; // ✅ Rend selectedIndex optionnel avec une valeur par défaut


  AppDrawer({required this.selectedIndex, required this.onItemSelected});

  @override
  _AppDrawerState createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  late Future<List<ChildProfile>> _childProfilesFuture;

  @override
  void initState() {
    super.initState();
    _childProfilesFuture = _fetchChildProfiles();
  }

  Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
      return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
      List<EleveInfoPersoDTO> eleves =
      await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
      return eleves.map((eleve) {
        return ChildProfile(
            eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
      }).toList();
    } catch (e) {
      print('Erreur lors de la récupération des profils: $e');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // 🟢 Barre d'en-tête personnalisée
          PAppHeadBar(),

          // 🟢 Texte de bienvenue personnalisé
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: PHomeWelcome(),
          ),

          // 🟢 Liste des options du drawer
          Expanded(
            child: ListView(
              children: [
                _buildDrawerItem(
                  icon: Icon(Icons.home),
                  text: 'Accueil',
                  index: 0,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Image.asset(AppImages.profilIcon, width: 30, height: 30),
                  text: 'Profil',
                  index: 1,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Icon(Icons.contact_page, size: 30, color: Color(0xFF025598)),
                  text: 'Contact',
                  index: 2,
                  context: context,
                ),
                _buildDrawerItem(
                  icon: Icon(Icons.settings),
                  text: 'Paramètres',
                  index: 3,
                  context: context,
                ),
                ListTile(
                  leading: Icon(Icons.logout, color: Colors.black),
                  title: Text('Déconnexion'),
                  onTap: () async {
                    await AuthService.logoutKeycloak();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required Widget icon,
    required String text,
    required int index,
    required BuildContext context,
  }) {
    return Obx(() => ListTile(
      leading: icon,
      title: Text(text),
      selected: widget.selectedIndex.value == index,
      selectedTileColor: Colors.blue.withOpacity(0.2), // Effet de sélection
      onTap: () {
        widget.onItemSelected(index);
        Navigator.pop(context); // Fermer le Drawer après sélection
      },
    ));
  }
}*/
/********
import 'package:EssiddikPrivee/commun/parent_app/app_PheadBar.dart';
import 'package:EssiddikPrivee/features/parent_app/Home/home_Pwelcome.dart';
import 'package:EssiddikPrivee/models/EleveInfoPersoDTO.dart';
import 'package:EssiddikPrivee/services/auth_service.dart';
import 'package:EssiddikPrivee/services/eleve_services.dart';
import 'package:EssiddikPrivee/utils/constants/text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppDrawer extends StatefulWidget {
  @override
  _AppDrawerState createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  late Future<List<ChildProfile>> _childProfilesFuture;

  @override
  void initState() {
    super.initState();
    _childProfilesFuture = _fetchChildProfiles();
  }

  Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
      return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
      List<EleveInfoPersoDTO> eleves =
      await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
      return eleves.map((eleve) {
        return ChildProfile(
            eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
      }).toList();
    } catch (e) {
      print('Erreur lors de la récupération des profils: $e');
      return [];
    }
  }
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // 🟢 Barre d'en-tête personnalisée
          PAppHeadBar(),

          // 🟢 Texte de bienvenue personnalisé
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: PHomeWelcome(),
          ),

          // 🟢 Liste des options du drawer
          Expanded(
            child: ListView(
              children: [
                ListTile(
                  leading: Icon(Icons.home),
                  title: Text('Accueil'),
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  leading: Icon(Icons.person),
                  title: Text('Profil'),
                  onTap: () {
                    // Naviguer vers l'écran de profil
                  },
                ),
                ListTile(
                  leading: Icon(Icons.contact_mail),
                  title: Text('Contact'),
                  onTap: () {
                    // Naviguer vers l'écran de contact
                  },
                ),
                ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Paramètres'),
                  onTap: () {},
                ),

                ListTile(
                  leading: Icon(Icons.logout, color: Colors.black),
                  title: Text('Déconnexion'),
                  onTap: () async {
                    await AuthService.logoutKeycloak();
                  },
                ),
              ],
            ),

          ),
        ],
      ),
    );
  }
}
*/


  /*@override
  _AppDrawerState createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  late Future<List<ChildProfile>> _childProfilesFuture;

  @override
  void initState() {
    super.initState();
    _childProfilesFuture = _fetchChildProfiles();
  }

  Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
      return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
      List<EleveInfoPersoDTO> eleves =
      await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
      return eleves.map((eleve) {
        return ChildProfile(
            eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
      }).toList();
    } catch (e) {
      print('Erreur lors de la récupération des profils: $e');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(color: Colors.blueAccent),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppText.Apptitle,
                  style: GoogleFonts.dmSerifDisplay(
                    fontSize: 22,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 10),
                FutureBuilder<List<ChildProfile>>(
                  future: _childProfilesFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return CircularProgressIndicator(color: Colors.white);
                    } else if (snapshot.hasError) {
                      return Text(
                        'Erreur lors du chargement',
                        style: TextStyle(color: Colors.white),
                      );
                    } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                      return Text(
                        'Aucun élève trouvé',
                        style: TextStyle(color: Colors.white),
                      );
                    } else {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: snapshot.data!.map((profile) {
                          return ListTile(
                            leading: Icon(Icons.person, color: Colors.white),
                            title: Text(
                              "${profile.firstName} ${profile.name}",
                              style: TextStyle(color: Colors.white),
                            ),
                            subtitle: Text(
                              profile.nomClasse,
                              style: TextStyle(color: Colors.white70),
                            ),
                          );
                        }).toList(),
                      );
                    }
                  },
                ),
              ],
            ),
          ),
          ListTile(
            leading: Icon(Icons.logout, color: Colors.black),
            title: Text('Déconnexion'),
            onTap: () async {
              await AuthService.logoutKeycloak();
            },
          ),
        ],
      ),
    );
  }*/

