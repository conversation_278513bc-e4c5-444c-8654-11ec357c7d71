import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:NovaSchool/utils/constants/text.dart';
import '../../utils/constants/images.dart';
import 'login_form.dart';

class LoginScreen extends StatelessWidget {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  /// Définition du dégradé linéaire pour le titre
  final Shader _linearGradient = const LinearGradient(colors: [Colors.blue, Colors.cyan],/*LinearGradient(
    colors: [Color(0xFF4099FF), Color(0xFF4099FF)],*/
    begin: Alignment.centerLeft,
    end: Alignment.bottomRight,
  ).createShader(const Rect.fromLTWH(0.0, 0.0, 350.0, 80.0));

  LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final EdgeInsets padding = MediaQuery.of(context)
        .viewInsets; // Obtention du padding en cas de clavier ouvert

    return AnimatedPadding(
      padding: padding,
      duration: const Duration(milliseconds: 300),
      curve: Curves.decelerate,
      child: Container(
        height: 600,
        margin: const EdgeInsets.symmetric(horizontal: 15),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.94),
          borderRadius: const BorderRadius.all(Radius.circular(40)),
        ),
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: Colors.transparent,
          body: SingleChildScrollView(
            child: Column(
              children: [
                /// Logo de l'école
                Image(
                  image: AssetImage(dotenv.get('schoolLogo')),
                  width: 150,
                ),
                // Image.network(
                //   '$schoolLogo',
                //   width: 150,
                //   // errorBuilder: (context, error, stackTrace) {
                //   //   return const Text('Failed to load logo');
                //   // },
                //   loadingBuilder: (context, child, loadingProgress) {
                //     if (loadingProgress == null) return child;
                //     return const CircularProgressIndicator();
                //   },
                // ),
                Padding(
                  padding: const EdgeInsets.only(top: 5),

                  /// Titre de connexion
                  child: Text(
                    AppText.Logintitle,
                    style: GoogleFonts.dmSerifDisplay(
                      fontSize: 28,
                      fontWeight: FontWeight.w500,
                      foreground: Paint()..shader = _linearGradient,
                    ),
                  ),
                ),

                /// Sous-titre de connexion
                // Text(
                //   AppText.LoginSubtitle,
                //   textAlign: TextAlign.center,
                //   style: TextStyle(
                //     fontSize: 15,
                //     color: Colors.black54,
                //   ),
                // ),
                LoginForm(formKey: formKey),

                /// Widget du formulaire de connexion
              ],
            ),
          ),
        ),
      ),
    );
  }
}
