import 'dart:async';
import 'dart:convert';
import 'package:NovaSchool/commun/admin_app/select_niveau.dart';
import 'package:NovaSchool/services/tokenInterceptor.dart';
import 'package:http/http.dart' as http;
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/models/IClasseProjection.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class ClassServices {
// String baseURL = dotenv.get('baseURL');
//   final String baseClassUrl = "$baseURL/classe";
  late final String baseURL;
  late final String baseClassUrl;
  final httpClient =
      InterceptedClient.build(interceptors: [TokenInterceptor()]);
  final auth = AuthService();

  ClassServices() {
    baseURL = dotenv.get('baseURL');
    baseClassUrl = "$baseURL/classe";
  }

  Future<List<IClasseProjection>> getAllClasseProjection(String niveau) async {
    final response = await httpClient.get(
      Uri.parse('$baseClassUrl/classeByListNiveau?listNiveau=$niveau'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    if (response.statusCode == 200) {
      List<dynamic> jsonData = json.decode(response.body);
      return jsonData.map((e) => IClasseProjection.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load classes');
    }
  }

  Future<List<IClasseProjection>> getAllClasseEnseignant() async {
    final userId = await auth.getUserId();
    final response = await httpClient.get(
      Uri.parse('$baseClassUrl/by-enseignant?keycloakUserId=$userId'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
    );

    if (response.statusCode == 200) {
      List<dynamic> jsonData = json.decode(response.body);
      return jsonData.map((e) => IClasseProjection.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load classes');
    }
  }

  Future<List<IClasseProjection>> fetchClasses(
      List<String> selectedLevels) async {
    String niveauList;
    List<String> niveaux = dotenv.get('niveaux').split(',');
    if (selectedLevels.length == niveaux.length) {
      niveauList = 'tous';
    } else {
      niveauList = selectedLevels.join(',');
    }

    print('Fetching classes for niveaux: $niveauList');

    List<IClasseProjection> fetchedClasses =
        await getAllClasseProjection(niveauList);
    print('Fetched classes: ${fetchedClasses.length}');
    return fetchedClasses;
  }
}
