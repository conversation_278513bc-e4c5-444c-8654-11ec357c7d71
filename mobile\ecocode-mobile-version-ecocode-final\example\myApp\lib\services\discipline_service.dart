import 'dart:convert';

import 'package:NovaSchool/models/disciplineEntity.dart';
import 'package:NovaSchool/models/disciplineList.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/tokenInterceptor.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class DisciplineServices {
  //String baseURL = dotenv.get('baseURL');
  late final String baseURL;
  late final String baseEleveUrl;
  late final String eleveUrl;
  final auth = AuthService();
  final httpClient =
      InterceptedClient.build(interceptors: [TokenInterceptor()]);

  DisciplineServices() {
    baseURL = dotenv.get('baseURL');
    baseEleveUrl = "$baseURL/disciplines";
  }

  Future<List<DisciplineEntity>> fetchDisciplinesByEleveId({
    required int eleveId,
  }) async {
    try {
      final url = Uri.parse('$baseEleveUrl/eleve/$eleveId');
      print("url: $url");
      final response = await httpClient.get(url);

      if (response.statusCode == 200) {
        // Assuming the response body contains a list of disciplines (not a Map)
        final List<dynamic> disciplineList = jsonDecode(response.body);

        // Map each JSON object in the list to a `DisciplineEntity`
        final disciplines = disciplineList
            .map((disciplineJson) => DisciplineEntity.fromJson(
                disciplineJson as Map<String, dynamic>))
            .toList();

        // Debugging output
        for (var discipline in disciplines) {
          print('Discipline ID: ${discipline.id}');
          print('Type: ${discipline.typeDiscipline}');
          print('Motif: ${discipline.motifDiscipline}');
          print('Date: ${discipline.dateDiscipline}');
        }

        return disciplines;
      } else {
        print('Failed to fetch disciplines: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Error fetching disciplines for Eleve ID $eleveId: $e');
      return [];
    }
  }

  Future<Disciplinelist> getAllDisciplines({
    required int page,
    required int size,
  }) async {
    try {
      final url = Uri.parse('$baseEleveUrl?page=$page&size=$size');
      print("url: $url");
      final response = await httpClient.get(url);

      if (response.statusCode == 200) {
        // Parse the paginated response
        final Map<String, dynamic> jsonResponse =
            jsonDecode(response.body) as Map<String, dynamic>;

        // Use Disciplinelist factory to parse response
        final disciplineList = Disciplinelist.fromJson(jsonResponse);

        // Debugging output
        print('Total Elements: ${disciplineList.totalElements}');
        print('Total Pages: ${disciplineList.totalPages}');
        for (var discipline in disciplineList.content) {
          print('Discipline ID: ${discipline.id}');
          print('Type: ${discipline.typeDiscipline}');
          print('Motif: ${discipline.motifDiscipline}');
          print('Date: ${discipline.dateDiscipline}');
        }

        return disciplineList;
      } else {
        print('Failed to fetch disciplines with pagination: ${response.statusCode}');
        return Disciplinelist(content: [], totalPages: 0, totalElements: 0);
      }
    } catch (e) {
      print('Error fetching paginated disciplines: $e');
      return Disciplinelist(content: [], totalPages: 0, totalElements: 0);
    }
  }
}
