import * as i0 from '@angular/core';
import * as i1 from '@angular/cdk/a11y';
import { M as MatCommonModule } from '../common-module.d-C8xzHJDr.js';
import { M as MatBadge, _ as _MatBadgeStyleLoader } from '../badge.d-mlaO4g0J.js';
export { a as MatBadgePosition, b as MatBadgeSize } from '../badge.d-mlaO4g0J.js';
import '@angular/cdk/bidi';
import '../palette.d-BSSFKjO6.js';

declare class MatBadgeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MatBadgeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MatBadgeModule, never, [typeof i1.A11yModule, typeof MatCommonModule, typeof MatBadge, typeof _MatBadgeStyleLoader], [typeof MatBadge, typeof MatCommonModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MatBadgeModule>;
}

export { MatBadge, MatBadgeModule };
