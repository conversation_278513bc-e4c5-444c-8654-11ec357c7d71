import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-parent-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="dashboard-container">
      <!-- Simple Test Header -->
      <div style="background: #667eea; color: white; padding: 20px; margin: 20px; border-radius: 8px;">
        <h1>🎉 Parent Dashboard Working!</h1>
        <p>This is the parent dashboard content. Navigation is working correctly.</p>
        <button (click)="testNavigation()" style="background: white; color: #667eea; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
          Test Navigation
        </button>
      </div>

      <!-- Header -->
      <header class="dashboard-header">
        <div class="header-content">
          <div class="welcome-section">
            <h1>👋 Welcome back, Parent!</h1>
            <p>Here's what's happening with your children today</p>
          </div>
          <div class="header-actions">
            <button class="notification-btn">
              <span class="icon">🔔</span>
              <span class="badge">3</span>
            </button>
            <button class="profile-btn" (click)="logout()">
              <span class="icon">👤</span>
              Logout
            </button>
          </div>
        </div>
      </header>

      <!-- Quick Stats -->
      <section class="stats-section">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">👨‍👩‍👧‍👦</div>
            <div class="stat-content">
              <h3>2</h3>
              <p>Children</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📚</div>
            <div class="stat-content">
              <h3>8</h3>
              <p>Active Courses</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-content">
              <h3>5</h3>
              <p>Pending Assignments</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📅</div>
            <div class="stat-content">
              <h3>3</h3>
              <p>Upcoming Events</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Main Content -->
      <div class="main-content">
        <!-- Children Overview -->
        <section class="children-section">
          <h2>📋 Children Overview</h2>
          <div class="children-grid">
            <div class="child-card">
              <div class="child-header">
                <div class="child-avatar">👧</div>
                <div class="child-info">
                  <h3>Sarah Johnson</h3>
                  <p>Grade 5A • Age 10</p>
                </div>
                <div class="child-status good">
                  <span class="status-dot"></span>
                  Good
                </div>
              </div>
              <div class="child-details">
                <div class="detail-item">
                  <span class="label">Attendance:</span>
                  <span class="value">95%</span>
                </div>
                <div class="detail-item">
                  <span class="label">Average Grade:</span>
                  <span class="value">A-</span>
                </div>
                <div class="detail-item">
                  <span class="label">Next Class:</span>
                  <span class="value">Mathematics at 10:00 AM</span>
                </div>
              </div>
            </div>

            <div class="child-card">
              <div class="child-header">
                <div class="child-avatar">👦</div>
                <div class="child-info">
                  <h3>Michael Johnson</h3>
                  <p>Grade 3B • Age 8</p>
                </div>
                <div class="child-status excellent">
                  <span class="status-dot"></span>
                  Excellent
                </div>
              </div>
              <div class="child-details">
                <div class="detail-item">
                  <span class="label">Attendance:</span>
                  <span class="value">98%</span>
                </div>
                <div class="detail-item">
                  <span class="label">Average Grade:</span>
                  <span class="value">A+</span>
                </div>
                <div class="detail-item">
                  <span class="label">Next Class:</span>
                  <span class="value">Art Class at 11:30 AM</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Recent Activities -->
        <section class="activities-section">
          <h2>📈 Recent Activities</h2>
          <div class="activities-list">
            <div class="activity-item">
              <div class="activity-icon">📝</div>
              <div class="activity-content">
                <h4>Math Assignment Submitted</h4>
                <p>Sarah submitted her algebra homework</p>
                <span class="activity-time">2 hours ago</span>
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-icon">🏆</div>
              <div class="activity-content">
                <h4>Achievement Unlocked</h4>
                <p>Michael received "Perfect Attendance" badge</p>
                <span class="activity-time">1 day ago</span>
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-icon">📚</div>
              <div class="activity-content">
                <h4>New Assignment Posted</h4>
                <p>Science project due next Friday</p>
                <span class="activity-time">2 days ago</span>
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- Quick Actions -->
      <section class="quick-actions">
        <h2>⚡ Quick Actions</h2>
        <div class="actions-grid">
          <button class="action-btn" (click)="navigateToGrades()">
            <span class="action-icon">📊</span>
            <span class="action-text">View Grades</span>
          </button>
          <button class="action-btn" (click)="navigateToSchedule()">
            <span class="action-icon">📅</span>
            <span class="action-text">View Schedule</span>
          </button>
          <button class="action-btn" (click)="navigateToMessages()">
            <span class="action-icon">💬</span>
            <span class="action-text">Messages</span>
          </button>
          <button class="action-btn" (click)="navigateToAttendance()">
            <span class="action-icon">📋</span>
            <span class="action-text">Attendance</span>
          </button>
        </div>
      </section>
    </div>
  `,
  styles: [`
    .dashboard-container {
      min-height: 100vh;
      background: #f5f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .dashboard-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin-bottom: 30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
    }

    .welcome-section h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .welcome-section p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 16px;
      align-items: center;
    }

    .notification-btn, .profile-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
    }

    .notification-btn:hover, .profile-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .badge {
      position: absolute;
      top: -5px;
      right: -5px;
      background: #ff4757;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .stats-section {
      max-width: 1200px;
      margin: 0 auto 40px auto;
      padding: 0 30px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .stat-card {
      background: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      gap: 16px;
      transition: transform 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
    }

    .stat-icon {
      font-size: 32px;
      background: #f8f9fa;
      padding: 12px;
      border-radius: 8px;
    }

    .stat-content h3 {
      margin: 0 0 4px 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    .stat-content p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }

    .main-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 30px;
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 30px;
    }

    .children-section h2, .activities-section h2 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .children-grid {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .child-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .child-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
    }

    .child-avatar {
      font-size: 32px;
      background: #f8f9fa;
      padding: 12px;
      border-radius: 50%;
    }

    .child-info h3 {
      margin: 0 0 4px 0;
      color: #333;
      font-size: 18px;
    }

    .child-info p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }

    .child-status {
      margin-left: auto;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .child-status.good {
      background: #d4edda;
      color: #155724;
    }

    .child-status.excellent {
      background: #cce5ff;
      color: #004085;
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: currentColor;
    }

    .child-details {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .detail-item .label {
      color: #666;
      font-size: 14px;
    }

    .detail-item .value {
      color: #333;
      font-weight: 500;
      font-size: 14px;
    }

    .activities-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .activity-item {
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      gap: 16px;
    }

    .activity-icon {
      font-size: 24px;
      background: #f8f9fa;
      padding: 8px;
      border-radius: 8px;
      height: fit-content;
    }

    .activity-content h4 {
      margin: 0 0 4px 0;
      color: #333;
      font-size: 16px;
    }

    .activity-content p {
      margin: 0 0 8px 0;
      color: #666;
      font-size: 14px;
    }

    .activity-time {
      color: #999;
      font-size: 12px;
    }

    .quick-actions {
      max-width: 1200px;
      margin: 40px auto 0 auto;
      padding: 0 30px;
    }

    .quick-actions h2 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .action-btn {
      background: white;
      border: 2px solid #e1e5e9;
      padding: 20px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }

    .action-btn:hover {
      border-color: #667eea;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    }

    .action-icon {
      font-size: 24px;
    }

    .action-text {
      color: #333;
      font-weight: 500;
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      }

      .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      }
    }
  `]
})
export class ParentDashboardComponent {
  constructor(private router: Router) {}

  testNavigation(): void {
    alert('Navigation is working! Dashboard component is loaded correctly.');
  }

  logout(): void {
    // Navigate back to login
    this.router.navigate(['/auth/login']);
  }

  navigateToGrades(): void {
    this.router.navigate(['/parent/grades']);
  }

  navigateToSchedule(): void {
    this.router.navigate(['/parent/schedule']);
  }

  navigateToMessages(): void {
    this.router.navigate(['/parent/messages']);
  }

  navigateToAttendance(): void {
    this.router.navigate(['/parent/attendance']);
  }
}
