import 'package:NovaSchool/utils/constants/text.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:NovaSchool/utils/constants/images.dart';

class THomeWelcome extends StatelessWidget {
  final String firstName;
  final String lastName;

  const THomeWelcome({
    Key? key,
    required this.firstName,
    required this.lastName,
  }) : super(key: key);

  // Future<String> _fetchTeacherName() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   final name = prefs.getString('name') ?? 'Nom du Professeur';
  //   final lastname = prefs.getString('lastname') ?? '';
  //   return name + " " + lastname;
  // }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20.0, top: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 84,
                height: 84,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.3),
                      spreadRadius: 2,
                      blurRadius: 6,
                      offset: Offset(0, 3),
                    ),
                  ],
                  image: DecorationImage(
                    image: AssetImage(AppImages.teacher),
                    fit: BoxFit.cover,


                  ),
                ),
              ),
              SizedBox(width: 15),

      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppText.Hometitle,
            style: GoogleFonts.dmSerifDisplay(
              fontSize: 23,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
              Text(
                "$firstName",
                style: GoogleFonts.dmSerifDisplay(
                    fontSize: 25,
                    fontStyle: FontStyle.italic, // 🔥 Ajout de l'italique
                    fontWeight: FontWeight.w800,
                    color: Colors.orange.shade200,
                ),
              ),
            ],

          ),
        ],

      ),],),

    );

  }
}
