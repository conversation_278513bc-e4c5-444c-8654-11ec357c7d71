class SoldeEleve {
  final int idSoldeEleve;
  final double soldeTotale;
  final int nombreTranche;
  final double totalePaye;
  final double acompte;
  final double montantTranche;
  final double resteMontantTranche;
  final DateTime dateDebutTranche;

  SoldeEleve({
    required this.idSoldeEleve,
    required this.soldeTotale,
    required this.nombreTranche,
    required this.totalePaye,
    required this.acompte,
    required this.montantTranche,
    required this.resteMontantTranche,
    required this.dateDebutTranche,
  });

  // Factory method to create an instance of SoldeEleve from JSON
  factory SoldeEleve.fromJson(Map<String, dynamic> json) {
    return SoldeEleve(
      idSoldeEleve: json['idSoldeEleve'],
      soldeTotale: json['soldeTotale'],
      nombreTranche: json['nombreTranche'],
      totalePaye: json['totalePaye'],
      acompte: json['acompte'],
      montantTranche: json['montantTranche'],
      resteMontantTranche: json['resteMontantTranche'],
      dateDebutTranche: DateTime.parse(json['dateDebutTranche']),
    );
  }
}
