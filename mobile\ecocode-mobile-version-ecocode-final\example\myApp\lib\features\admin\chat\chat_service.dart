import 'dart:convert';

import 'package:dialog_flowtter/dialog_flowtter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'class_chat.dart';

class MessageService {
  static const _key = 'messages';

  static Future<List<Message>> getMessages() async {
    final prefs = await SharedPreferences.getInstance();
    final messages = prefs.getStringList(_key)?.map((e) => Message.fromJson(jsonDecode(e))) ?? [];
    return messages.toList();
  }

  static Future<void> addMessage(Message message) async {
    final prefs = await SharedPreferences.getInstance();
    final messages = await getMessages();
    messages.add(message);
    final jsonMessages = messages.map((e) => jsonEncode(e.toJson())).toList();
    prefs.setStringList(_key, jsonMessages);
  }
}