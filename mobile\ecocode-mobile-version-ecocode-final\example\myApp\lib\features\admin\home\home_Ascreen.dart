import 'package:NovaSchool/features/admin/A_appDrawer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/app_secondHeadBar.dart';
import 'package:NovaSchool/features/admin/profil/profil_Ascreen.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'home_Adahboard.dart';
import 'home_Awelcome.dart';
import 'package:NovaSchool/services/auth_service.dart';

class AHomeScreen extends StatelessWidget {
  const AHomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, String>>(
      future: _getUserInfo(),
      builder: (context, AsyncSnapshot<Map<String, String>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        } else if (snapshot.hasError) {
          return Scaffold(
            body: Center(child: Text('Erreur: ${snapshot.error}')),
          );
        } else if (snapshot.hasData) {
          final userInfo = snapshot.data!;
          return Scaffold(
            backgroundColor: Color(0xFFF2F2F2),
            drawer: AdminDrawer(),
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(kToolbarHeight),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
                ),
                child: AppBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  centerTitle: true,
                  iconTheme: IconThemeData(
                    color: Colors.white, // 🔁 change la couleur ici selon ton besoin
                    size: 30, // facultatif : ajuste la taille
                  ),
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/logos/ecocode.png',
                        height: 150,
                        color: Colors.white,
                      ),
                      SizedBox(width: 10),

                    ],
                  ),
                  actions: [
                    IconButton(
                      icon: Icon(Icons.logout, color: Colors.white),
                      //title: Text('Déconnexion'),
                      onPressed: () async {
                        await AuthService.logoutKeycloak();
                      },
                    ),
                   /* IconButton(
                      icon: Icon(Icons.person_outline, color: Colors.white,size: 30,),
                      onPressed: () async {
                        Map<String, String> userInfo = await _getUserInfo();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AProfilScreen(
                              firstName: userInfo['senderUsername'] ?? '',
                              lastName: userInfo['senderUserSurname'] ?? '',
                              email: userInfo['email'] ?? '',
                              profile: userInfo['profile'] ?? '',
                            ),
                          ),
                        );
                      },
                    ),*/
                    /*IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  onPressed: () {},
                ),*/
                  ],
                ),
              ),
            ),body: Column(
              children: [
                /*PrimaryHeaderContainer(
                  child: Column(
                    children: [
                      SAppHeadBar(
                        profilPage: AProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),
                      AHomeWelcome(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                      ),
                      SizedBox(height: 70),
                    ],
                  ),
                ),*/
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        AHomePedagogicalElement(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          return Scaffold(
            body: Center(child: Text('Aucune donnée disponible')),
          );
        }
      },
    );
  }

  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }
}


// class AHomeScreen extends StatelessWidget {
//   const AHomeScreen({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder(
//       future: _getUserInfo(),
//       builder: (context, AsyncSnapshot<Map<String, String>> snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return Scaffold(
//             body: Center(child: CircularProgressIndicator()),
//           );
//         } else if (snapshot.hasError) {
//           return Scaffold(
//             body: Center(child: Text('Erreur: ${snapshot.error}')),
//           );
//         } else if (snapshot.hasData) {
//           final userInfo = snapshot.data!;
//           return Scaffold(
//             body: AppBackground(
//               child: Column(
//                 children: [
//                   SAppHeadBar(
//                   profilPage: AProfilScreen(
//                     firstName: userInfo['senderUsername'] ?? '',
//                     lastName: userInfo['senderUserSurname'] ?? '',
//                   )),
//                   AHomeWelcome(
//                     firstName: userInfo['senderUsername'] ?? '',
//                     lastName: userInfo['senderUserSurname'] ?? '',
//                   ),
//                   Expanded(
//                     child: SingleChildScrollView(
//                       child: Column(
//                         children: [
//                           AHomePedagogicalElement(),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         } else {
//           return Scaffold(
//             body: Center(child: Text('Aucune donnée disponible')),
//           );
//         }
//       },
//     );
//   }
//
//   Future<Map<String, String>> _getUserInfo() async {
//     final authService = AuthService();
//     return await authService.getSenderDetails();
//   }
// }


