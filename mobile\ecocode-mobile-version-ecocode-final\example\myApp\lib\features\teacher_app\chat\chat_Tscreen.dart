import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:NovaSchool/utils/helpers/helper_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class TChatScreen extends StatefulWidget {
  const TChatScreen({Key? key}) : super(key: key);

  @override
  _PChatScreenState createState() => _PChatScreenState();
}


class _PChatScreenState extends State<TChatScreen> {
  TextEditingController _messageController = TextEditingController();
  List<String> messages = ['Salut'];
  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      messages.insert(0, _messageController.text.trim());
      _messageController.clear();
    }
  }
  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: TeacherDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: Image.asset(
              'assets/logos/ecocode.png', // Remplace avec ton chemin
              height: 150, // Ajuste la taille
              color: Colors
                  .white, // Applique du blanc au logo (si PNG avec transparence)
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: Column(
          children: [/*
            SizedBox(
              height: 90, // Réduction de la hauteur du header
              child: PHeadHomeScreen(),
            ),*/
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      height: AppHelperFunction.screenHeight(context) * 0.8, // Ajustement de la hauteur
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          double innerHeight = constraints.maxHeight;
                          double innerWidth = constraints.maxWidth;
                          return Stack(
                            fit: StackFit.expand,
                            children: [
                              Positioned(
                                bottom: 10,
                                left: 15,
                                right: 15,
                                child: Container(
                                  height: innerHeight * 0.85, // Ajustement de la hauteur du container
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20), // Coins plus arrondis
                                    color: AppColors.light,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.3), // Ombre plus légère
                                        spreadRadius: 0,
                                        blurRadius: 5,
                                        offset: Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      // Liste de messages
                                      Expanded(
                                        child: ListView.builder(
                                          itemCount: messages.length,
                                          reverse: true,
                                          itemBuilder: (context, index) {
                                            final reversedIndex = messages.length - 1 - index;
                                            return Padding(
                                              padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15.0),
                                              child: Align(
                                                alignment: reversedIndex % 2 == 0 ? Alignment.centerLeft : Alignment.centerRight,
                                                child: Container(
                                                  padding: EdgeInsets.all(12),
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.only(
                                                      topLeft: Radius.circular(30),
                                                      topRight: Radius.circular(30),
                                                      bottomLeft: Radius.circular(reversedIndex % 2 == 0 ? 5 : 30),
                                                      bottomRight: Radius.circular(reversedIndex % 2 == 0 ? 25 : 5),
                                                    ),
                                                    color: reversedIndex % 2 == 0 ? AppColors.dark.withOpacity(0.1) : Color(0xFF4099FF),
                                                  ),
                                                  child: Text(
                                                    messages[reversedIndex],
                                                    style: TextStyle(
                                                      color: reversedIndex % 2 == 0 ? Colors.black : Colors.white,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                      // Champ de saisie de texte
                                      Padding(
                                        padding: const EdgeInsets.all(10.0),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: TextField(
                                                controller: _messageController,
                                                decoration: InputDecoration(
                                                  hintText: 'Entrez votre message...',
                                                  filled: true,
                                                  fillColor: AppColors.light,
                                                  border: OutlineInputBorder(
                                                    borderRadius: BorderRadius.circular(10.0),
                                                    borderSide: BorderSide.none,
                                                  ),
                                                  contentPadding: EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
                                                ),
                                              ),
                                            ),
                                            IconButton(
                                              icon: Icon(
                                                Iconsax.send_2,
                                                color: Color(0xFF4099FF),
                                              ),
                                              onPressed: () {
                                                if (_messageController.text.isNotEmpty) {
                                                  setState(() {
                                                    messages.add(_messageController.text);
                                                    _messageController.clear();
                                                  });
                                                }
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 15,
                                left: 0,
                                right: 0,
                                child: Center(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(50),
                                      color: Color(0xFF4099FF),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.3),
                                          spreadRadius: 0,
                                          blurRadius: 5,
                                          offset: Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                    width: innerWidth * 0.6,
                                    height: 45,
                                    child: Align(
                                      alignment: Alignment.center,
                                      child: Text(
                                        'Support & Assistance', // Nouveau titre
                                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                          fontSize: 15,
                                          color: AppColors.light,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}// import 'package:flutter/material.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../../../commun/app_background.dart';
// import '../../../commun/teacher_app/app_TheadBar.dart';
// import '../../../utils/constants/colors.dart';
// import '../../../utils/helpers/helper_functions.dart';
// import '../../chat/class_chat.dart';
// import '../../chat/messsage_ervice.dart';
//
//
// class TChatScreen extends StatefulWidget {
//   const TChatScreen({Key? key}) : super(key: key);
//
//   @override
//   _TChatScreenState createState() => _TChatScreenState();
// }
//
// class _TChatScreenState extends State<TChatScreen> {
//   final MessageService _messageService = MessageService();
//   List<Message> _messages = [];
//   List<Message> _sentMessages = [];
//   final TextEditingController _controller = TextEditingController();
//   late int _teacherId;
//   late int _adminId;
//
//   @override
//   void initState() {
//     super.initState();
//     _fetchMessages();
//   }
//
//   Future<void> _fetchMessages() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     _teacherId = prefs.getInt('userId')!;
//     _adminId = 9;  // Replace with the real admin ID if necessary
//
//     try {
//       List<String> messageStrings = await _messageService.getMessages(_teacherId);
//
//       // Convert list of message strings to list of Message objects
//       List<Message> messages = messageStrings.map((messageString) => Message(content: messageString, message: messageString)).toList();
//
//
//       setState(() {
//         _messages = messages;
//       });
//     } catch (e) {
//       print('Failed to load messages: $e');
//     }
//   }
//
//
//
//   Future<void> _sendMessage() async {
//     if (_controller.text.isEmpty) return;
//
//     try {
//       await _messageService.sendMessage(_adminId, _teacherId, _controller.text);
//       // Ajouter le message envoyé à la liste des messages envoyés
//       _sentMessages.add(Message(
//         senderId: _teacherId,
//         recipientId: _adminId,
//         content: _controller.text,
//         message: _controller.text,
//       ));
//       _controller.clear();
//       await _fetchMessages();  // Mettre à jour les messages affichés
//     } catch (e) {
//       print('Failed to send message: $e');
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: AppBackground(
//         child: SingleChildScrollView(
//           child: Column(
//             children: [
//               TAppHeadBar(),
//               SizedBox(height: 10), // Ajout d'un espace supplémentaire
//               Container(
//                 height: AppHelperFunction.screenHeight(context) * 0.82,
//                 child: LayoutBuilder(
//                   builder: (context, constraints) {
//                     double innerHeight = constraints.maxHeight;
//                     double innerWidth = constraints.maxWidth;
//                     return Stack(
//                       fit: StackFit.expand,
//                       children: [
//                         Positioned(
//                           bottom: 10,
//                           left: 15,
//                           right: 15,
//                           child: Container(
//                             height: innerHeight * 0.9,
//                             decoration: BoxDecoration(
//                               borderRadius: BorderRadius.circular(15),
//                               color: AppColors.light,
//                               boxShadow: [
//                                 BoxShadow(
//                                   color: Colors.black.withOpacity(0.5),
//                                   spreadRadius: 0,
//                                   blurRadius: 5,
//                                   offset: Offset(0, 1),
//                                 ),
//                               ],
//                             ),
//                             child: Column(
//                               children: [
//                                 Expanded(
//                                   child: ListView.builder(
//                                     itemCount: _messages.length + _sentMessages.length, // Ajouter la longueur des messages envoyés
//                                     reverse: true,
//                                     itemBuilder: (context, index) {
//                                       if (index < _sentMessages.length) {
//                                         // Si l'index est inférieur à la longueur des messages envoyés, afficher un message envoyé
//                                         final message = _sentMessages[index];
//                                         final isTeacherMessage = message.senderId == _teacherId;
//                                         return Padding(
//                                           padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15.0),
//                                           child: Align(
//                                             alignment: isTeacherMessage ? Alignment.centerRight : Alignment.centerLeft,
//                                             child: Container(
//                                               padding: EdgeInsets.all(12),
//                                               decoration: BoxDecoration(
//                                                 borderRadius: BorderRadius.only(
//                                                   topLeft: Radius.circular(30),
//                                                   topRight: Radius.circular(30),
//                                                   bottomLeft: Radius.circular(isTeacherMessage ? 5 : 30),
//                                                   bottomRight: Radius.circular(isTeacherMessage ? 30 : 5),
//                                                 ),
//                                                 color: isTeacherMessage ? Colors.blue : Colors.grey.withOpacity(0.5),
//                                               ),
//                                               child: Text(
//                                                 message.content,
//                                                 style: TextStyle(
//                                                   color: isTeacherMessage ? Colors.white : Colors.black,
//                                                 ),
//                                               ),
//                                             ),
//                                           ),
//                                         );
//                                       } else {
//                                         // Si l'index est supérieur ou égal à la longueur des messages envoyés, afficher un message reçu
//                                         final reversedIndex = index - _sentMessages.length;
//                                         final message = _messages[_messages.length - 1 - reversedIndex];
//                                         final isTeacherMessage = message.senderId == _teacherId;
//                                         return Padding(
//                                           padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15.0),
//                                           child: Align(
//                                             alignment: isTeacherMessage ? Alignment.centerRight : Alignment.centerLeft,
//                                             child: Container(
//                                               padding: EdgeInsets.all(12),
//                                               decoration: BoxDecoration(
//                                                 borderRadius: BorderRadius.only(
//                                                   topLeft: Radius.circular(30),
//                                                   topRight: Radius.circular(30),
//                                                   bottomLeft: Radius.circular(isTeacherMessage ? 30 : 5),
//                                                   bottomRight: Radius.circular(isTeacherMessage ? 5 : 30),
//                                                 ),
//                                                 color: isTeacherMessage ? Colors.blue : Colors.grey.withOpacity(0.5),
//                                               ),
//                                               child: Text(
//                                                 message.content,
//                                                 style: TextStyle(
//                                                   color: isTeacherMessage ? Colors.white : Colors.black,
//                                                 ),
//                                               ),
//                                             ),
//                                           ),
//                                         );
//                                       }
//                                     },
//                                   ),
//                                 ),
//
//                                 Padding(
//                                   padding: const EdgeInsets.all(15.0),
//                                   child: Row(
//                                     children: [
//                                       Expanded(
//                                         child: TextField(
//                                           controller: _controller,
//                                           decoration: InputDecoration(
//                                             hintText: 'Entrez votre message...',
//                                             filled: true,
//                                             fillColor: AppColors.light,
//                                             border: OutlineInputBorder(
//                                               borderRadius: BorderRadius.circular(10.0),
//                                               borderSide: BorderSide.none,
//                                             ),
//                                             contentPadding: EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
//                                           ),
//                                         ),
//                                       ),
//                                       IconButton(
//                                         icon: Icon(
//                                           Icons.send,
//                                           color: Color(0xFF4099FF),
//                                         ),
//                                         onPressed: _sendMessage,
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                         Positioned(
//                           top: 25,
//                           left: 0,
//                           right: 0,
//                           child: Center(
//                             child: Container(
//                               decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.circular(50),
//                                 color: Color(0xFF4099FF),
//                                 boxShadow: [
//                                   BoxShadow(
//                                     color: Colors.black.withOpacity(0.5),
//                                     spreadRadius: 0,
//                                     blurRadius: 5,
//                                     offset: Offset(0, 1),
//                                   ),
//                                 ],
//                               ),
//                               width: innerWidth * 0.6,
//                               height: 50,
//                               child: Align(
//                                 alignment: Alignment.center,
//                                 child: Text(
//                                   'Messagerie administration',
//                                   style: Theme.of(context).textTheme.headlineMedium?.copyWith(
//                                     fontSize: 15,
//                                     color: AppColors.light,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ],
//                     );
//                   },
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }