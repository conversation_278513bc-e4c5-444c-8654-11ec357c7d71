export { U as UniqueSelectionDispatcher } from './unique-selection-dispatcher-DtHZDqyJ.mjs';
export { A as ArrayDataSource, _ as _RecycleViewRepeaterStrategy, b as _VIEW_REPEATER_STRATEGY, a as _ViewRepeaterOperation } from './recycle-view-repeater-strategy-DoWdPqVw.mjs';
export { D as DataSource, i as isDataSource } from './data-source-D34wiQZj.mjs';
export { _ as _DisposeViewRepeaterStrategy } from './dispose-view-repeater-strategy-D_JReLI1.mjs';
export { S as SelectionModel, g as getMultipleValuesInSingleSelectionError } from './selection-model-CeeHVIcP.mjs';
import '@angular/core';
import 'rxjs';
//# sourceMappingURL=collections.mjs.map
