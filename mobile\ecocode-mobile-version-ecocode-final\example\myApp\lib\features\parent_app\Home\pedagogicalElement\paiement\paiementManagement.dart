import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/paiement/paiement.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/paiement/services.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/models/soldeEleve.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/paiementService.dart';
import 'package:NovaSchool/services/soldeEleveService.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
class PaiementManagementPage extends StatefulWidget {
  @override
  _PaiementManagementPageState createState() => _PaiementManagementPageState();
}

class _PaiementManagementPageState extends State<PaiementManagementPage> {
   bool showServices = false;
  SoldeEleve? solde;
  bool _isLoading = true;
  final AuthService authService = AuthService();
  final SoldeEleveService paiementService = SoldeEleveService();

  @override
  void initState() {
    super.initState();
    _fetchSolde();
  }

  Future<void> _fetchSolde() async {
    setState(() {
      _isLoading = true;
    });

    final eleveId = await authService.getEleveId();
    solde = await paiementService.getSoldeByEleveId(eleveId);

    setState(() {
      _isLoading = false;
    });
  }

   @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<PNavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => ParentAppNavBar());
              },
              child: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: Column(
          children: [
            SecondHeadBar1(title: 'Paiements',icon: Icons.credit_card),

            /*  Padding(
              padding: const EdgeInsets.only(top: 10, left: 10, right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start, // Aligner à droite
                children: [
                  Tooltip(
                    message: 'Retour',
                    child: IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Icons.arrow_back_ios, color: Colors.blueGrey),
                    ),
                  ),
                ],
              ),
            ),
*/
           /* PrimaryHeaderContainer(
              child: Container(
                height: 100,
                child: SecondHeadBar(
                  title: 'Paiements',
                  titleColor: Colors.white,
                  iconColor: Colors.white,
                ),
              ),
            ),*/
            if (_isLoading)
              CircularProgressIndicator()
            else
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Card for Montant Total
                    _buildInfoCard(
                      title: "Montant Total",
                      value: "${solde?.soldeTotale?.toStringAsFixed(2) ?? "0"} TND",
                    ),
                    // Card for Reste à Payer
                    _buildInfoCard(
                      title: "Reste à Payer",
                      value:  "${((solde?.soldeTotale ?? 0) - (solde?.totalePaye ?? 0)).toStringAsFixed(2)} TND",

                    ),
                  ],
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          showServices = false;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: !showServices ? Colors.lightBlue.shade300 : Colors.lightBlue.shade300.withOpacity(0.5),
                          width: 2.0,
                        ),
                      ),
                      child: Text(
                        'Paiements',
                        style: TextStyle(fontSize: 17,
                          color: !showServices ? Colors.lightBlue : Colors.blue.withOpacity(0.5),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          showServices = true;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(
                          color: showServices ? Colors.lightBlue.shade300  : Colors.lightBlue.shade300.withOpacity(0.5),
                          width: 2.0,
                        ),
                      ),
                      child: Text(
                        'Services',
                        style: TextStyle(fontSize: 17,
                          color: showServices ? Colors.lightBlue : Colors.blue.withOpacity(0.5),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: showServices ? PServicesScreen() : PPaiementScreen(),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build info card
  Widget _buildInfoCard({required String title, required String value}) {
    return Card(
      color: Colors.white,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Container(
        padding: EdgeInsets.all(16.0),
        width: MediaQuery.of(context).size.width * 0.45,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
            SizedBox(height: 8.0),
            Text(
              value,
              style: TextStyle(
                fontSize: 18.0,
                color: Colors.blueAccent,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
