/*import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/services/extensionConfigService.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:NovaSchool/models/extensionConfig.dart';
import 'package:url_launcher/url_launcher.dart';

class TAppHeadBar extends StatefulWidget {
  final Widget profilPage;

  const TAppHeadBar({
    Key? key,
    required this.profilPage,
  }) : super(key: key);

  @override
  _SAppHeadBarState createState() => _SAppHeadBarState();
}

class _SAppHeadBarState extends State<TAppHeadBar> {
  final int configId = 1;
  final ExtensionConfigService service = ExtensionConfigService();
  final FileDownloadService fileDownloadService = FileDownloadService();

  void _showSnackbar(BuildContext context, String message, Color color,
      {String? actionLabel, VoidCallback? onPressed}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        action: actionLabel != null
            ? SnackBarAction(label: actionLabel, onPressed: onPressed ?? () {})
            : null,
      ),
    );
  }

  Future<void> _downloadFile(BuildContext context, String url) async {
    try {
      fileDownloadService.showLoadingDialog(
          context, 'Téléchargement en cours...');
      if (await canLaunch(url)) {
        await launch(url);
        _showSnackbar(
          context,
          'Téléchargement démarré. Vérifiez votre dossier Téléchargements.',
          Colors.green,
        );
      } else {
        _showSnackbar(
          context,
          'Impossible de lancer le téléchargement.',
          Colors.red,
        );
      }
    } catch (e) {
      _showSnackbar(
        context,
        'Une erreur s\'est produite pendant le téléchargement.',
        Colors.red,
      );
    } finally {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ExtensionConfig>(
      future: service.getById(configId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }

        final config = snapshot.data;

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue, Colors.cyan],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Logo centré
              Center(
                child: Image.asset(
                  'assets/logos/ecocode.png',
                  color: Colors.white.withOpacity(0.89),
                  width: 120,
                  height: 120,
                ),
              ),

              // Espacement entre logo et texte
              SizedBox(height: 8),

              // Widget de téléchargement (en dessous du logo)
              if (config?.message?.isNotEmpty ?? false)
                Align(
                  alignment: Alignment.center,
                  child: GestureDetector(
                    onTap: config?.path?.isNotEmpty ?? false
                        ? () => _downloadFile(context, config!.path!)
                        : null,
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color: Colors.blue[700]?.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (config?.path?.isNotEmpty ?? false)
                            Icon(Icons.download, color: Colors.yellow, size: 20),
                          if (config?.path?.isNotEmpty ?? false)
                            SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              config!.message!,
                              style: GoogleFonts.dmSerifDisplay(
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                color: Colors.yellow,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}*/
import 'package:NovaSchool/commun/admin_app/app_ASecondHead.dart';
import 'package:NovaSchool/models/extensionConfig.dart';
import 'package:NovaSchool/services/extensionConfigService.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/commun/parent_app/appBar_config.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui' as ui;
import '../../utils/constants/text.dart';

class TAppHeadBar extends StatefulWidget {
  final Widget profilPage;

  const TAppHeadBar({
    Key? key,
    required this.profilPage,
  }) : super(key: key);

  @override
  _SAppHeadBarState createState() => _SAppHeadBarState();
}

class _SAppHeadBarState extends State<TAppHeadBar> {
  final int configId = 1;
  final ExtensionConfigService service = ExtensionConfigService();
  final FileDownloadService fileDownloadService = FileDownloadService();

  void _showSnackbar(BuildContext context, String message, Color color,
      {String? actionLabel, VoidCallback? onPressed}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        action: actionLabel != null
            ? SnackBarAction(label: actionLabel, onPressed: onPressed ?? () {})
            : null,
      ),
    );
  }

  Future<void> _downloadFile(BuildContext context, String url) async {
    try {
      fileDownloadService.showLoadingDialog(
          context, 'Téléchargement en cours...');
      if (await canLaunch(url)) {
        await launch(url);
        _showSnackbar(
          context,
          'Téléchargement démarré. Vérifiez votre dossier Téléchargements.',
          Colors.green,
        );
      } else {
        _showSnackbar(
          context,
          'Impossible de lancer le téléchargement.',
          Colors.red,
        );
      }
    } catch (e) {
      _showSnackbar(
        context,
        'Une erreur s\'est produite pendant le téléchargement.',
        Colors.red,
      );
    } finally {
      Navigator.of(context).pop();
    }
  }
/*
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ExtensionConfig>(
        future: service.getById(configId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return CircularProgressIndicator(); // Optional: Show a loader
          }

          ExtensionConfig? config = snapshot.data;

          return AppBarConfig(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      AppText.Apptitle,
                      style: GoogleFonts.dmSerifDisplay(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10),
                if (config != null &&
                    config.message != null &&
                    config.message!.isNotEmpty)
                  GestureDetector(
                    onTap: config.path != null && config.path!.isNotEmpty
                        ? () => _downloadFile(context, config.path!)
                        : null,
                    child: Row(
                      children: [
                        if (config.path != null && config.path!.isNotEmpty)
                          Icon(
                            Icons.download,
                            color: Colors.yellow,
                            size: 20,
                          ),
                        SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            config.message!,
                            style: GoogleFonts.dmSerifDisplay(
                              fontSize: 20,
                              fontWeight: FontWeight.w400,
                              color: Colors.yellow,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            actions: [
              Padding(
                padding: const EdgeInsets.only(right: 2.0),
                child: Tooltip(
                  message: 'Profile',
                  child: IconButton(
                    icon: Icon(Icons.person_outline),
                    color: Colors.white,
                    iconSize: 20,
                    onPressed: () async {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => widget.profilPage),
                      );
                    },
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Tooltip(
                  message: 'Déconnexion',
                  child: IconButton(
                    icon: Icon(Icons.logout),
                    color: Colors.white,
                    iconSize: 20, // Ajustez la taille si nécessaire
                    onPressed: () async {
                      await AuthService.logoutKeycloak();
                    },
                  ),
                ),
              ),
            ],
          );
        });
  }
}
*/
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ExtensionConfig>(
        future: service.getById(configId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          }

          ExtensionConfig? config = snapshot.data;

          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue, Colors.cyan],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              /*borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(5),
                bottomRight: Radius.circular(5),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 8,
                  offset: Offset(0, 4),
                )
              ],*/
            ),
            child: AppBarConfig(
              title: SizedBox(
                height: 200, // ou plus selon la taille du titre
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                        'assets/logos/ecocode.png',
                        color: Colors.white.withOpacity(0.89),
                        colorBlendMode: BlendMode.srcIn,
                        width: 120, height: 120 // Ajuste selon ton design
                    ),
                   /* Text(
                      AppText.Apptitle,
                      style: GoogleFonts.dmSerifDisplay(
                        fontSize: 26, // augmente ici si besoin
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),*/
                    //const SizedBox(height: 6),

                    if (config?.message?.isNotEmpty ?? false)
                      GestureDetector(
                        onTap: config?.path?.isNotEmpty ?? false
                            ? () => _downloadFile(context, config!.path!)
                            : null,
                        child: Row(
                          children: [
                            if (config?.path?.isNotEmpty ?? false)
                              Icon(Icons.download, color: Colors.yellow, size: 20),
                            const SizedBox(width: 6),
                            Flexible(
                              child: Text(
                                config!.message!,
                                style: GoogleFonts.dmSerifDisplay(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.yellow,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),

              /***title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppText.Apptitle,
                    style: GoogleFonts.dmSerifDisplay(
                      fontSize: 22,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 6),
                  if (config?.message?.isNotEmpty ?? false)
                    GestureDetector(
                      onTap: config?.path?.isNotEmpty ?? false
                          ? () => _downloadFile(context, config!.path!)
                          : null,
                      child: Row(
                        children: [
                          if (config?.path?.isNotEmpty ?? false)
                            Icon(Icons.download, color: Colors.yellow, size: 20),
                          const SizedBox(width: 6),
                          Flexible(
                            child: Text(
                              config!.message!,
                              style: GoogleFonts.dmSerifDisplay(
                                fontSize: 18,
                                fontWeight: FontWeight.w400,
                                color: Colors.yellow,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),*/

              actions: [/*
                _buildIconButton(
                  icon: Icons.person_outline,
                  tooltip: 'Profil',
                  onPressed: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => widget.profilPage),
                  ),
                ),
                _buildIconButton(
                  icon: Icons.logout,
                  tooltip: 'Déconnexion',
                  onPressed: () async => await AuthService.logoutKeycloak(),
                ),*/

              ],

            ),



          );

        });
  }

  Widget _buildIconButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: Tooltip(
        message: tooltip,
        child: InkWell(
          borderRadius: BorderRadius.circular(0),
          onTap: onPressed,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Icon(icon, color: Colors.white, size: 12),
          ),
        ),
      ),
    );
  }
}