import 'dart:convert';
import 'dart:typed_data';

import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../utils/constants/colors.dart';
import '../../../../utils/constants/images.dart';
import '../../../../utils/helpers/helper_functions.dart';

class PProfileImage extends StatefulWidget {
  final String imageData;
  final EleveInfoPersoDTO profile;
  const PProfileImage({Key? key, required this.imageData, required this.profile,}) : super(key: key);

  @override
  _PProfileImageState createState() => _PProfileImageState();
}

class _PProfileImageState extends State<PProfileImage> {
  late ValueNotifier<ChildProfile?> _studentProfileNotifier;
  Uint8List? _imageBytes;

  @override
  void initState() {
    super.initState();
    _studentProfileNotifier = Get.find<ValueNotifier<ChildProfile?>>();
    if (widget.imageData.isNotEmpty) {
      _imageBytes = base64Decode(widget.imageData);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppHelperFunction.screenHeight(context) * 0.22,
      child: LayoutBuilder(
        builder: (context, constraints) {
          double innerHeight = constraints.maxHeight;
          double innerWidth = constraints.maxWidth;
          return Stack(
            fit: StackFit.expand,
            children: [
              Positioned(
                bottom: 0,
                left: 15,
                right: 15,
                child: Container(
                  height: innerHeight * 0.6,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    color: AppColors.light,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.5),
                        spreadRadius: 0,
                        blurRadius: 5,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              ),
                Positioned(
                top: 0,
                left: (innerWidth - innerWidth * 0.25) / 2,
                child: Center(
                  child: Container(
                    width: innerWidth * 0.25,
                    height: innerHeight * 0.67,

                    decoration: BoxDecoration(
                      shape: BoxShape.circle,


                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.2),


                          spreadRadius: 0,
                          blurRadius: 5,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),

                    child: ClipOval( // Ensure the image is in a circular shape
                      child: _imageBytes != null
                          ? Image.memory(
                              _imageBytes!, // Display the image from byte code
                              fit: BoxFit.cover,
                            )
                          : Image.asset(
                              AppImages.imageLogo,

                              fit: BoxFit. cover,
                            ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 50),
              Positioned(

                bottom: innerHeight * 0.1,
                left: 0,
                right: 0,


                child: Center(

                  child: Text(
                    '${widget.profile.nomEleve} ${widget.profile.prenomEleve}', // Display the profile name
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontSize: 28,
                      color: Colors.blue.shade700// Color(0xFF4099FF),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
