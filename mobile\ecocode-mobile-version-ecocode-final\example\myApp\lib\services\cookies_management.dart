import 'package:universal_io/io.dart' as io;
import 'package:universal_html/html.dart' as html;

String? get(String key) {
  if (io.Platform.isWindows) {
    final cookies = html.document.cookie?.split('; ') ?? [];
    for (var cookie in cookies) {
      var parts = cookie.split('=');
      var name = Uri.decodeComponent(parts[0]);
      if (key == name) {
        return parts[1].isNotEmpty ? Uri.decodeComponent(parts[1]) : null;
      }
    }
  }
  return null;
}

void set(
  String key,
  String value, {
  DateTime? expires,
  Duration? maxAge,
  String? path,
  String? domain,
  bool? secure,
}) {
  if (io.Platform.isWindows) {
    if (maxAge != null) {
      expires = DateTime.now().add(maxAge);
    }
    var cookie = ([
      Uri.encodeComponent(key),
      '=',
      Uri.encodeComponent(value),
      expires != null ? '; expires=' + formatDate(expires) : '',
      path != null ? '; path=' + path : '',
      domain != null ? '; domain=' + domain : '',
      secure != null && secure == true ? '; secure' : ''
    ].join(''));
    html.document.cookie = cookie;
  }
}

Future<bool> removeAllCookies() async {
  if (io.Platform.isWindows) {
    final cookies = html.document.cookie?.split('; ') ?? [];
    for (var cookiedata in cookies) {
      var parts = cookiedata.split('=');
      var name = Uri.decodeComponent(parts[0]);
      remove(name);
    }
    return true;
  }
  return false;
}

bool remove(String key, {String? path, String? domain, bool? secure}) {
  if (get(key) != null) {
    set(
      key,
      '',
      expires: DateTime.fromMillisecondsSinceEpoch(0),
      path: path,
      domain: domain,
      secure: secure,
    );
    return true;
  }
  return false;
}

const _weekday = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
const _months = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];

String _pad(int number) => '$number'.padLeft(2, '0');

String formatDate(DateTime date) {
  date = date.toUtc();
  final weekday = _weekday[date.weekday - 1];
  final month = _months[date.month - 1];
  return '$weekday, ${_pad(date.day)} $month ${date.year} ${_pad(date.hour)}:${_pad(date.minute)}:${_pad(date.second)} UTC';
}
