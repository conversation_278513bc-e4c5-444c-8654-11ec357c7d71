class DisciplineEntity {
  final int id;
  final String typeDiscipline;
  final String motifDiscipline;
  final DateTime dateDiscipline;
  final int eleveId;

  DisciplineEntity({
    required this.id,
    required this.typeDiscipline,
    required this.motifDiscipline,
    required this.dateDiscipline,
    required this.eleveId,
  });

  factory DisciplineEntity.fromJson(Map<String, dynamic> json) {
    return DisciplineEntity(
      id: json['id'] ?? 0,
      typeDiscipline: json['typeDiscipline'] ?? '',
      motifDiscipline: json['motifDiscipline'] ?? '',
      dateDiscipline: json['dateDiscipline'] != null
          ? DateTime.parse(json['dateDiscipline'])
          : DateTime.now(),
      eleveId: json['eleveId'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'typeDiscipline': typeDiscipline,
      'motifDiscipline': motifDiscipline,
      'dateDiscipline': dateDiscipline.toIso8601String(),
      'eleveId': eleveId,
    };
  }
}
