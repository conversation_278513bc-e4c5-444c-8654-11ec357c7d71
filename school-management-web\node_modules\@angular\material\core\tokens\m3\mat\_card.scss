@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, card);

/// Generates custom tokens for the mat-card.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-card
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: sass-utils.merge-all(
    token-definition.generate-typography-tokens($systems, title-text, title-large),
    token-definition.generate-typography-tokens($systems, subtitle-text, title-medium),
    (
      subtitle-text-color: map.get($systems, md-sys-color, on-surface)
    )
  );

  @return token-definition.namespace-tokens($prefix, $tokens, $token-slots);
}

