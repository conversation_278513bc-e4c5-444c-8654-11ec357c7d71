@use 'sass:map';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, circular-progress);

/// Generates the tokens for MDC circular-progress
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of tokens for the MDC circular-progress
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $mdc-tokens: token-definition.get-mdc-tokens(
      'circular-progress-indicator', $systems, $exclude-hardcoded);
  $variant-tokens: (
    primary: (), // Default, no overrides needed
    secondary: (
      active-indicator-color: map.get($systems, md-sys-color, secondary),
    ),
    tertiary: (
      active-indicator-color: map.get($systems, md-sys-color, tertiary),
    ),
    error: (
      active-indicator-color: map.get($systems, md-sys-color, error),
    )
  );

  @return token-definition.namespace-tokens(
      $prefix, ($mdc-tokens, $variant-tokens), $token-slots);
}
