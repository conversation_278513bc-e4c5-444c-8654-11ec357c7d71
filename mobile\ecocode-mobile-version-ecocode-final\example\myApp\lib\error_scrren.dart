import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/features/welcome/welcome_screen.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:NovaSchool/utils/constants/images.dart';

// import 'main_amana.dart';


class ErrorScreen extends StatelessWidget {
  final String errorMessage;

  const ErrorScreen({Key? key, required this.errorMessage}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AppBackground(
        child: Padding(
          padding: const EdgeInsets.only(top: 40.0),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Oups!',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 30,
                    color: Color(0xFF4099FF),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                Image(
                  image: AssetImage(AppImages.Connexion),
                  width: 280,
                ),
                const SizedBox(height: 20),
                Text(
                  "Une erreur s'est produite",
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 20,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  errorMessage,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(builder: (context) => WelcomeScreen()),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 20), // Taille du bouton
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30), // Rayon des coins
                    ),
                  ),
                  child: const Text(
                    'Retour à l\'accueil',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFFFFFFFF),
                    ),
                  ),
                ),

              ],
            ),
          ),
        ),
      ),
    );
  }
}

// import 'package:flutter/material.dart';
// import 'package:NovaSchool/commun/app_background.dart';
// import 'package:NovaSchool/utils/constants/colors.dart';
// import 'package:NovaSchool/utils/constants/images.dart';

// import 'main.dart';


// class ErrorScreen extends StatelessWidget {
//   final String errorMessage;

//   const ErrorScreen({Key? key, required this.errorMessage}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: AppBackground(
//         child: Padding(
//           padding: const EdgeInsets.only(top: 40.0),
//           child: Center(
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 Text(
//                   'Oups!',
//                   textAlign: TextAlign.center,
//                   style: const TextStyle(
//                     fontSize: 30,
//                     color: Color(0xFF4099FF),
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 const SizedBox(height: 20),
//                 Image(
//                   image: AssetImage(AppImages.Connexion),
//                   width: 280,
//                 ),
//                 const SizedBox(height: 20),
//                 Text(
//                   "Une erreur s'est produite",
//                   textAlign: TextAlign.center,
//                   style: const TextStyle(
//                     fontSize: 20,
//                     color: Colors.black,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 const SizedBox(height: 20),
//                 Text(
//                   errorMessage,
//                   textAlign: TextAlign.center,
//                   style: const TextStyle(
//                     fontSize: 15,
//                     color: Colors.black,
//                   ),
//                 ),
//                 const SizedBox(height: 20),
//                 ElevatedButton(
//                   onPressed: () {
//                     Navigator.pushReplacement(
//                       context,
//                       MaterialPageRoute(builder: (context) => WelcomeScreen()),
//                     );
//                   },
//                   style: ElevatedButton.styleFrom(
//                     padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 20), // Taille du bouton
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(30), // Rayon des coins
//                     ),
//                   ),
//                   child: const Text(
//                     'Retour à l\'accueil',
//                     style: TextStyle(
//                       fontSize: 18,
//                       fontWeight: FontWeight.w500,
//                       color: Color(0xFFFFFFFF),
//                     ),
//                   ),
//                 ),

//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
