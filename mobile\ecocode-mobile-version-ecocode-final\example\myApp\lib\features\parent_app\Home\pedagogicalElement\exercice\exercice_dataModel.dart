class ExerciceData {
  final String title;
  final String? description;
  final String date;
  final String? fichier;
  bool showDetails; // Nouveau champ pour stocker l'état d'affichage des détails

  ExerciceData({
    required this.title,
    this.description,
    required this.date,
    this.fichier,
    this.showDetails = false, // Initialiser à false par défaut
  });

  factory ExerciceData.fromJson(Map<String, dynamic> json) {
    return ExerciceData(
      title: json['title'] ?? "",
      description: json['description'],
      date: json['date'] ?? "",
      fichier: json['fichier'],
    );
  }
}
