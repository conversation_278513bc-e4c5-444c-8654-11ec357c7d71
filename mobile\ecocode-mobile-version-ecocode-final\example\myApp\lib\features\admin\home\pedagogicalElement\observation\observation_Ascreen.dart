import 'dart:io';
import 'dart:typed_data';

import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:NovaSchool/features/admin/A_appDrawer.dart';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/admin/profil/profil_Ascreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/models/messageResponse.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/models/fileMessage.dart';
import 'package:NovaSchool/models/messageEntity.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../../commun/app_background.dart';
import '../../../../../commun/parent_app/app_secondheadBar.dart';
import '../../../../../utils/constants/colors.dart';
import 'observation_Aajout.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:get/get.dart';

class AObservationScreen extends StatefulWidget {
  @override
  _AObservationScreenState createState() => _AObservationScreenState();
}

class _AObservationScreenState extends State<AObservationScreen> {
  List<MessageResponse> observations = [];
  bool _isLoading = true;
  String localUsername = '';
  int _itemsPerPage = 0;
  int _currentPageIndex = 0;
  final int _pageSize = 5;
  int _totalPages = 0;

  MessageServices msg = MessageServices();
  final FileDownloadService _fileDownloadService = FileDownloadService();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateItemsPerPage();
    });
    _fetchObservations();
    _loadLocalUsername();
  }

  Future<void> _downloadFiles(int messageId) async {
    await _fileDownloadService.downloadFiles(context, messageId);
  }

  Future<void> _fetchObservations({int page = 0}) async {
    setState(() {
      _isLoading = true;
    });

    List<MessageResponse> fetchedObservations = await msg.fetchMessagesForAdmin(
      type: "Observation",
      page: page,
      pageSize: _pageSize,
      isSender: true,
    );

    if (mounted) {
      setState(() {
        observations = fetchedObservations;
        _totalPages = (fetchedObservations.length / _pageSize).ceil();
        _isLoading = false;
      });
    }
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true;
      });
      _fetchObservations(page: newIndex);
    }
  }

  Future<void> _deleteObservations(int idmsg) async {
    await msg.deleteMessageAndExecute(
      context,
      'Voulez-vous être sûr de supprimer ce observation !',
      () async {
        await msg.deleteMessage(idmsg);
        await _fetchObservations();
      },
    );
  }

  Future<void> _confirmObservation(int idMessage) async {
    await msg.confirmAndExecute(
      context,
      'Voulez-vous être sûr de valider ce observation !',
      () async {
        await msg.changeConfirmation(idMessage);
        await _fetchObservations();
      },
    );
  }

  Future<void> _loadLocalUsername() async {
    final prefs = await SharedPreferences.getInstance();
    String username = prefs.getString('name') ?? '';
    setState(() {
      localUsername = username;
      _isLoading = false; // Set loading to false after fetching
    });
  }
  Future<Map<String, String>> _getUserInfo() async {
    return await AuthService().getSenderDetails();
  }
  @override
  Widget build(BuildContext context) {
    List<MessageResponse> currentPageObservations = observations.isEmpty
        ? []
        : observations
            .skip(_currentPageIndex * _pageSize)
            .take(_pageSize)
            .toList();


    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AdminDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            iconTheme: IconThemeData(
              color: Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            title: GestureDetector(
              onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => AdminAppNavBar()), // remplace THomeScreen par ton écran d'accueil
                );
              },
            /*title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<ANavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => AdminAppNavBar());
              },*/
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),
                ],
              ),
            ),/*Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/logos/ecocode.png',
                  height: 150,
                  color: Colors.white,
                ),
                SizedBox(width: 10),

              ],
            ),*/
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),/*
              IconButton(
                icon: Icon(Icons.person_outline, color: Colors.white,size: 30,),
                onPressed: () async {
                  Map<String, String> userInfo = await _getUserInfo();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AProfilScreen(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                        email: userInfo['email'] ?? '',
                        profile: userInfo['profile'] ?? '',
                      ),
                    ),
                  );
                },
              ),*/
              /*IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  onPressed: () {},
                ),*/
            ],
          ),
        ),
      ),
      resizeToAvoidBottomInset: false,
      body: AppBackground(
        child: Column(
          children: [
            SecondHeadBar1(title: 'Observations',icon: Icons.visibility,),

            // Your existing header
            /*PrimaryHeaderContainer(
              child: Container(
                height: 100, // Adjust the height here
                child: SecondHeadBar(
                  title: 'Observations',
                  titleColor: Colors.white,
                  iconColor: Colors.white,
                ),
              ),
            ),*/
            Expanded(
              // Wrap this in Expanded
              child: _isLoading
                  ? Center(child: CircularProgressIndicator()) // spinner pendant le chargement
                  : observations.isEmpty
                  ? Center(child: Text('Aucune observation disponible'))
                  : RefreshIndicator(
                onRefresh: _fetchObservations
                ,child:ListView.builder(
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          itemCount: currentPageObservations.length,
                          itemBuilder: (context, index) {
                            MessageResponse observationsItem =
                                currentPageObservations[index];
                                bool isConfirmed = observationsItem.messageEntity.confirmed;
                            return Container(
                              // Remove fixed height for flexibility
                              child: Card(
                                margin: EdgeInsets.symmetric(
                                    vertical: 4.0, horizontal: 15),
                                    color: isConfirmed ? Colors.white : Color.fromARGB(255, 207, 207, 207),
                                child: ListTile(
                                  contentPadding: EdgeInsets.symmetric(
                                      vertical: 2.0, horizontal: 10.0),
                                  title: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              '${observationsItem.messageEntity.objet[0].toUpperCase()}${observationsItem.messageEntity.objet.substring(1)}',
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16),
                                            ),
                                            SizedBox(height: 4),
                                            Text(
                                              '${observationsItem.messageEntity.body[0].toUpperCase()}${observationsItem.messageEntity.body.substring(1)}',
                                              style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.black
                                                      .withOpacity(0.6)),
                                            ),
                                            SizedBox(height: 12),
                                            Text(
                                              observationsItem.messageEntity
                                                          .senderUsername
                                                          .toLowerCase() ==
                                                      localUsername
                                                          .toLowerCase()
                                                  ? 'Expéditeur: Moi'
                                                  : 'Expéditeur: ${observationsItem.messageEntity.senderUsername}',
                                              style: TextStyle(
                                                fontSize: 13,
                                                  color: Colors.black
                                                      .withOpacity(0.5)
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          if (!observationsItem
                                              .messageEntity.confirmed)
                                            Tooltip(
                                              message: 'Confirmer',
                                              child: IconButton(
                                                icon: Icon(
                                                  Icons.check_circle_outline,
                                                  color: Colors.grey,
                                                ),
                                                iconSize: 20,
                                                onPressed: () async {
                                                  await _confirmObservation(
                                                      observationsItem
                                                          .messageEntity
                                                          .idMessage);
                                                },
                                              ),
                                            ),
                                          SizedBox(height: 10),
                                          if (observationsItem
                                              .messageEntity.existFile)
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment
                                                  .end, // Aligns the items to the right
                                              children: [
                                                Text(
                                                  '${observationsItem.messageEntity.numberOfFile} fichiers',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.black
                                                        .withOpacity(0.5),
                                                  ),
                                                ),
                                                SizedBox(
                                                    width:
                                                        8), // Adds space between the text and the button
                                                Tooltip(
                                                  message: 'Télécharger',
                                                  child: IconButton(
                                                    icon: Icon(
                                                      Icons
                                                          .file_download_outlined,
                                                      color:
                                                          Colors.green.shade700,
                                                      size: 30,
                                                    ),
                                                    onPressed: () async {
                                                      await _downloadFiles(
                                                          observationsItem
                                                              .messageEntity
                                                              .idMessage);
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          SizedBox(height: 10),
                                          Tooltip(
                                            message: 'Supprimer',
                                            child: IconButton(
                                              icon: Icon(Icons.delete,
                                                  color: Color(0xFFE17878),
                                                  size: 20),
                                              onPressed: () {
                                                _deleteObservations(
                                                    observationsItem
                                                        .messageEntity
                                                        .idMessage);
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
            ),),
            // Pagination controls
            Padding(
              padding: const EdgeInsets.all(4.0), // Adjust padding
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: Icon(Icons.arrow_back_ios,
                        color: _currentPageIndex > 0
                            ? Color(0xFF4099FF)
                            : Colors.grey,
                        size: 20),
                    onPressed: _currentPageIndex > 0
                        ? () {
                            _updatePageIndex(_currentPageIndex - 1);
                          }
                        : null,
                  ),
                  Text(
                    '${_currentPageIndex + 1} / $_totalPages',
                    style: TextStyle(color: Colors.grey, fontSize: 15),
                  ),
                  IconButton(
                    icon: Icon(Icons.arrow_forward_ios,
                        color: _currentPageIndex < _totalPages - 1
                            ? Color(0xFF4099FF)
                            : Colors.grey,
                        size: 20),
                    onPressed: _currentPageIndex < _totalPages - 1
                        ? () {
                            _updatePageIndex(_currentPageIndex + 1);
                          }
                        : null,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Color(0xFF4099FF),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => AjoutObservation(
                      onObservationsAdded: () {
                        _fetchObservations();
                      },
                    )),
          );
        },
        child: Icon(
          Icons.add,
          color: AppColors.light,
        ),
      ),
    );
  }
}
