class GetLigneArticleDTO {
  final int? idLigneAchat;
  final DateTime? dateAchat;
  final int? quantite;
  final int? nombreDeTranche;
  final double? prixArticle;
  final double? resteMontantArticle;
  final double? remisePourcentage;
  final double? remiseMontant;
  final String? typePaiement;
  final DateTime? dateAlertPaiement;
  final bool? etatPaiement;
  final bool? annulation;
  final bool? confirmer;
  final int? idArticle;
  final String? nomArticle;
  final int? idTranche;
  final double? montantTranche;
  final DateTime? dateAlertpaiement;
  final bool? etat;

  GetLigneArticleDTO({
    this.idLigneAchat,
    this.dateAchat,
    this.quantite,
    this.nombreDeTranche,
    this.prixArticle,
    this.resteMontantArticle,
    this.remisePourcentage,
    this.remiseMontant,
    this.typePaiement,
    this.dateAlertPaiement,
    this.etatPaiement,
    this.annulation,
    this.confirmer,
    this.idArticle,
    this.nomArticle,
    this.idTranche,
    this.montantTranche,
    this.dateAlertpaiement,
    this.etat,
  });

  // Factory method to create a GetLigneArticleDTO from a map (JSON)
  factory GetLigneArticleDTO.fromJson(Map<String, dynamic> json) {
  return GetLigneArticleDTO(
    idLigneAchat: json['idLigneAchat'] is int
        ? json['idLigneAchat']
        : (json['idLigneAchat'] as num?)?.toInt(),
    dateAchat: json['dateAchat'] != null ? DateTime.parse(json['dateAchat']) : null,
    quantite: json['quantite'] is int
        ? json['quantite']
        : (json['quantite'] as num?)?.toInt(),
    nombreDeTranche: json['nombreDeTranche'] is int
        ? json['nombreDeTranche']
        : (json['nombreDeTranche'] as num?)?.toInt(),
    prixArticle: json['prixArticle'],
    resteMontantArticle: json['resteMontantArticle'],
    remisePourcentage: json['remisePourcentage'],
    remiseMontant: json['remiseMontant'],
    typePaiement: json['typePaiement'],
    dateAlertPaiement: json['dateAlertPaiement'] != null
        ? DateTime.parse(json['dateAlertPaiement'])
        : null,
    etatPaiement: json['etatPaiement'],
    annulation: json['annulation'],
    confirmer: json['confirmer'],
    idArticle: json['idArticle'] is int
        ? json['idArticle']
        : (json['idArticle'] as num?)?.toInt(),
    nomArticle: json['nomArticle'],
    idTranche: json['idTranche'] is int
        ? json['idTranche']
        : (json['idTranche'] as num?)?.toInt(),
    montantTranche: json['montantTranche'],
    dateAlertpaiement: json['dateAlertpaiement'] != null
        ? DateTime.parse(json['dateAlertpaiement'])
        : null,
    etat: json['etat'],
  );
}

  // Method to convert a GetLigneArticleDTO to a map (for serialization, if needed)
  Map<String, dynamic> toJson() {
    return {
      'idLigneAchat': idLigneAchat,
      'dateAchat': dateAchat?.toIso8601String(),
      'quantite': quantite,
      'nombreDeTranche': nombreDeTranche,
      'prixArticle': prixArticle,
      'resteMontantArticle': resteMontantArticle,
      'remisePourcentage': remisePourcentage,
      'remiseMontant': remiseMontant,
      'typePaiement': typePaiement,
      'dateAlertPaiement': dateAlertPaiement?.toIso8601String(),
      'etatPaiement': etatPaiement,
      'annulation': annulation,
      'confirmer': confirmer,
      'idArticle': idArticle,
      'nomArticle': nomArticle,
      'idTranche': idTranche,
      'montantTranche': montantTranche,
      'dateAlertpaiement': dateAlertpaiement?.toIso8601String(),
      'etat': etat,
    };
  }
}
