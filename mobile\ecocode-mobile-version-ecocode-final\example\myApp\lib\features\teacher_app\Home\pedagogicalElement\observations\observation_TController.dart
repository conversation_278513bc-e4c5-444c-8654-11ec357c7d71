import 'dart:io';
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/commun/teacher_app/app_TnavBar.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/admin_app/select_niveau.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/models/IClasseProjection.dart';
import 'package:NovaSchool/models/eleveParListClassesDTO.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/class_services.dart';
import 'package:NovaSchool/services/eleve_services.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:logger/logger.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
final log = Logger();

class TObservationsController extends StatefulWidget {
  final VoidCallback onObservationsAdded;

  TObservationsController({required this.onObservationsAdded});
  @override
  _TObservationsControllerState createState() =>
      _TObservationsControllerState();
}

class _TObservationsControllerState extends State<TObservationsController> {
  final _formKey = GlobalKey<FormState>();
  List<String> _selectedLevels = [];
  List<EleveParListClassesDTO> _selectedStudents = [];
  List<EleveParListClassesDTO> _allStudents = [];
  List<IClasseProjection> _classes = [];
  List<IClasseProjection> _selectedClasses = [];
  List<Map<String, Object?>> _selectedFiles = [];
  TextEditingController titreController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  EleveServices eleveServices = EleveServices();
  ClassServices classService = ClassServices();
  FileDownloadService fileDownloadService = FileDownloadService();
  AuthService auth = AuthService();
  MessageServices msg = MessageServices();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _fetchClasses();
  }

  Future<void> _fetchClasses() async {
    try {
      List<IClasseProjection> fetchedClasses =
          await classService.getAllClasseEnseignant();
      if (mounted) {
        setState(() {
          _classes = fetchedClasses;
          _selectedClasses = [];
          _selectedStudents = [];
        });
      }
    } catch (e) {
      print('Error fetching classes: $e');
    }
  }

  Future<void> _fetchStudents() async {
    try {
      List<int> selectedClassIds =
          _selectedClasses.map((c) => c.idClasse).toList();
      List<EleveParListClassesDTO> fetchedStudents =
          await eleveServices.fetchStudents(selectedClassIds);
      if (mounted) {
        setState(() {
          _allStudents = fetchedStudents;
        });
      }
    } catch (e) {
      print('Error fetching students: $e');
    }
  }

  Future<void> _pickFiles() async {
    List<Map<String, dynamic>> selectedFiles =
        await fileDownloadService.pickFiles(context);
    setState(() {
      _selectedFiles = selectedFiles;
    });
  }

  Future<void> _createObservations() async {
    bool confirmed = dotenv.get('confirmed').toLowerCase() == 'true';
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedClasses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Veuillez sélectionner au moins un classe'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }
    fileDownloadService.showLoadingDialog(
        context, "Ajout de l'observations ...");
    setState(() {
      _isLoading = true;
    });

    try {
      final senderDetails = await auth.getSenderDetails();
      senderDetails.remove("email");
      senderDetails.remove("profile");

      final addObservationsDTO = {
        "MessageEntity": {
          "type": "Observation",
          "objet": titreController.text,
          "body": descriptionController.text,
          "readStatus": false,
           "confirmed": confirmed,
          ...senderDetails,
        },
        "niveauList": _selectedLevels,
        "classeList":
            _selectedClasses.map((classe) => classe.idClasse).toList(),
        "eleveList":
            _selectedStudents.map((student) => student.idEleve).toList(),
        "ListofUsers": [],
      };

      await msg.addMessage(context, addObservationsDTO, _selectedFiles);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Observations ajouté avec succès'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      widget.onObservationsAdded();

      resetFieldsAndSelections();
      Navigator.pop(context);
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Échec de l\'ajout du Observations'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
      Navigator.of(context).pop();
    }
  }

  void resetFieldsAndSelections() {
    setState(() {
      _selectedLevels = [];
      _selectedClasses = [];
      _selectedStudents = [];
      titreController.clear();
      descriptionController.clear();
      _selectedFiles = [];
    });
  }

  Map<String, IconData> fileIcons = {
    'pdf': Icons.picture_as_pdf,
    'doc': Icons.description,
    'docx': Icons.description,
    'xls': Icons.table_chart,
    'xlsx': Icons.table_chart,
    'jpg': Icons.image,
    'jpeg': Icons.image,
    'png': Icons.image,
    'txt': Icons.text_fields,
    'zip': Icons.folder_zip,
  };
  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }
  IconData getFileIcon(String fileName) {
    String extension = fileName.split('.').last.toLowerCase();
    return fileIcons[extension] ?? Icons.picture_as_pdf; // Default icon
  }

  @override
  Widget build(BuildContext context) {
    List<String> classes = _selectedClasses.map((c) => c.nomClasse).toList();
    List<String> niveaux = dotenv.get('niveaux').split(',');
    bool allLevelsSelected = niveaux.length == _selectedLevels.length;
    bool allClassesSelected =
        _classes.isNotEmpty && _classes.length == _selectedClasses.length;
    bool allStudentsSelected = _allStudents.isNotEmpty &&
        _allStudents.length == _selectedStudents.length;

    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      //drawer: TeacherDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
          /*  iconTheme: IconThemeData(
              color: Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),*/
            leading:Tooltip(
              message: 'Retour',
              child: IconButton(
                onPressed: () => Get.back(),
                icon: Icon(Icons.arrow_back_ios, color: Colors.white), // Icône de retour
              ),
            ),
            title: GestureDetector(
              onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => TeacherAppNavBar()), // remplace THomeScreen par ton écran d'accueil
                );
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),
                ],
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                icon: Icon(Icons.person_outline, color: Colors.white,size: 30,),
                onPressed: () async {
                  Map<String, String> userInfo = await _getUserInfo();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TProfilScreen(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                        email: userInfo['email'] ?? '',
                        profile: userInfo['profile'] ?? '',
                      ),
                    ),
                  );
                },
              ),*/
              /*IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  onPressed: () {},
                ),*/
            ],
          ),
        ),
      ),
      resizeToAvoidBottomInset: true,
      body: AppBackground(
        child: Form(
          key: _formKey,
          child: Column(
            children: [
               SecondHeadBar1(title: 'Ajouter une observation',icon: Icons.add_circle,),
              /*PrimaryHeaderContainer(
                child: Container(
                  height: 100, // Adjust the height here
                  child: SecondHeadBar(
                    title: 'Ajouter un Observations',
                    titleColor: Colors.white,
                    iconColor: Colors.white,
                  ),
                ),
              ),*/
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //SizedBox(height: 20),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Classe :',
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                              Row(
                                children: [
                                  Checkbox(
                                    value: allClassesSelected,
                                    onChanged: (bool? value) {
                                      setState(() {
                                        if (value == true) {
                                          _selectedClasses =
                                              List<IClasseProjection>.from(
                                                  _classes);
                                        } else {
                                          _selectedClasses = [];
                                        }
                                        _fetchStudents();
                                      });
                                    },
                                  ),
                                  Text('Tous les classes'),
                                ],
                              ),
                            ],
                          ),
                          GestureDetector(
                            onTap: () async {
                              final results =
                                  await showDialog<List<IClasseProjection>>(
                                context: context,
                                builder: (context) {
                                  return MultiSelectDialog<IClasseProjection>(
                                    items: _classes
                                        .map((classe) => MultiSelectItem(classe,
                                            '${classe.niveauClasse} ${classe.nomClasse}'))
                                        .toList(),
                                    title: Text("Sélectionner classes"),
                                    selectedColor: Colors.blue,
                                    initialValue: _selectedClasses,
                                    backgroundColor: Colors.white,
                                  );
                                },
                              );

                              if (results != null) {
                                setState(() {
                                  _selectedClasses =
                                      List<IClasseProjection>.from(results);
                                  _fetchStudents();
                                });
                              }
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  vertical: 12.0, horizontal: 16.0),
                              decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(15)),
                                border: Border.all(
                                  color: Colors.grey,
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      allClassesSelected
                                          ? "Toutes les classes"
                                          : (_selectedClasses.isEmpty
                                              ? "Classe"
                                              : _selectedClasses
                                                  .map((c) =>
                                                      '${c.niveauClasse} ${c.nomClasse}')
                                                  .join(', ')),
                                      style: TextStyle(fontSize: 16),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Icon(Icons.arrow_drop_down),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 20),
                        ],
                      ),
                      if (_selectedClasses.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'Liste élèves :',
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                                Row(
                                  children: [
                                    Checkbox(
                                      value: _allStudents.isNotEmpty &&
                                          allStudentsSelected,
                                      onChanged: (bool? value) {
                                        setState(() {
                                          if (value == true) {
                                            _selectedStudents = List<
                                                    EleveParListClassesDTO>.from(
                                                _allStudents);
                                          } else {
                                            _selectedStudents = [];
                                          }
                                        });
                                      },
                                    ),
                                    Text('Tous les élèves'),
                                  ],
                                ),
                              ],
                            ),
                            GestureDetector(
                              onTap: () async {
                                final results = await showDialog<
                                    List<EleveParListClassesDTO>>(
                                  context: context,
                                  builder: (context) {
                                    return MultiSelectDialog<
                                        EleveParListClassesDTO>(
                                      items: _allStudents
                                          .map((eleve) => MultiSelectItem(eleve,
                                              "${eleve.prenomEleve} ${eleve.nomEleve}"))
                                          .toList(),
                                      title: Text("Sélectionner élèves"),
                                      selectedColor: Colors.blue,
                                      initialValue: _selectedStudents,
                                    );
                                  },
                                );

                                if (results != null) {
                                  setState(() {
                                    _selectedStudents =
                                        List<EleveParListClassesDTO>.from(
                                            results);
                                  });
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 12.0, horizontal: 16.0),
                                decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(15)),
                                  border: Border.all(
                                    color: Colors.grey,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        allStudentsSelected
                                            ? "Tous les élèves"
                                            : (_selectedStudents.isEmpty
                                                ? "Liste élèves"
                                                : _selectedStudents
                                                    .map((e) =>
                                                        "${e.prenomEleve} ${e.nomEleve}")
                                                    .join(', ')),
                                        style: TextStyle(fontSize: 16),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    Icon(Icons.arrow_drop_down),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(height: 20),
                          ],
                        ),
                      Text(
                        'Objet',
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      TextFormField(
                        controller: titreController,
                        decoration: InputDecoration(
                          labelText: 'Objet',
                          floatingLabelBehavior: FloatingLabelBehavior.never,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Le titre est requis';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 20),
                      TextFormField(
                        controller: descriptionController,
                        decoration: InputDecoration(
                          labelText: 'Écrire un message...',
                          floatingLabelBehavior: FloatingLabelBehavior.never,
                        ),
                        minLines: 3,
                        maxLines: 5,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'La description est requise';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 20),
                      TextButton.icon(
                        onPressed: _pickFiles,
                        icon: Icon(Icons.attach_file, color: Color(0xFF4099FF)),
                        label: Text('Choisir un fichier',
                            style: TextStyle(color: Color(0xFF4099FF))),
                      ),
                      if (_selectedFiles.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: _selectedFiles.map((file) {
                            String fileName =
                                file['name'] as String; // Cast to String
                            return Card(
                              margin: EdgeInsets.symmetric(vertical: 8.0),
                              child: ListTile(
                                leading: Icon(
                                  getFileIcon(fileName),
                                  color: Colors.blue,
                                ),
                                title: Text(fileName),
                                trailing: IconButton(
                                  icon: Icon(Icons.delete, color: Colors.red),
                                  onPressed: () {
                                    setState(() {
                                      // Remove the file from the list
                                      _selectedFiles.removeWhere(
                                          (f) => f['name'] == fileName);
                                    });
                                  },
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      SizedBox(height: 20),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _createObservations,
                          child: Text('Ajouter',
                              style: TextStyle(
                                  fontSize:
                                      18)), // Augmenter la taille du texte si nécessaire
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                vertical: 20.0,
                                horizontal:
                                    24.0), // Augmenter la taille du bouton
                            shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(5)),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
