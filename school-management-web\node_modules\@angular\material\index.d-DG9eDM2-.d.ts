import * as i0 from '@angular/core';
import { M as MatCommonModule } from './common-module.d-C8xzHJDr.js';
import { M as MatRipple } from './ripple.d-BxTUZJt7.js';

declare class MatRippleModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MatRippleModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MatRippleModule, never, [typeof MatCommonModule, typeof MatRipple], [typeof MatRipple, typeof MatCommonModule]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MatRippleModule>;
}

export { MatRippleModule as M };
