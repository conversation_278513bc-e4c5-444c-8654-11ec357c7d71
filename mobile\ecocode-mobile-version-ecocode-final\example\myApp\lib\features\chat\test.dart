import 'package:flutter/material.dart';

class PrimaryHeaderContainer extends StatelessWidget {
  const
  PrimaryHeaderContainer({
    Key? key,
    required this.child,
  }) : super(key: key);

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return CurvedEdgeWidget(
      child: Container(
        decoration: BoxDecoration(
        gradient: LinearGradient(
        colors: [Colors.blue, Colors.cyan],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
    ),),
        //color: Color(0xFF4099FF),
        child: Stack(
          children: [
            Positioned(top:-150 , right :-250 ,child: TCircularContainer(backgroundColor: Colors.white.withOpacity(0.1))),
            Positioned(top: 100 , right :-300 ,child: TCircularContainer(backgroundColor: Colors.white.withOpacity(0.1))),
            child,
          ],
        ),
      ),
    );
  }
}

class CurvedEdgeWidget extends StatelessWidget {
  const CurvedEdgeWidget({
    Key? key, this.child,
  }) : super(key: key);
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: CustomCurvedEdges(),
      child: child,
    );
  }
}

class TCircularContainer extends StatelessWidget {
  const TCircularContainer({
    Key? key,
    this.width =400,
    this.height =400,
    this.radius =400,
    this.padding =0,
    this.child,
    this.backgroundColor = Colors.blue,
    this.margin ,
  })  : super(key: key);

  final double? width;
  final double? height;
  final double radius;
  final double padding;
  final EdgeInsets? margin;
  final Widget? child;
  final Color? backgroundColor;


  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin:margin,
      padding : EdgeInsets.all(padding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius),
        color: backgroundColor,
      ),
    );
  }
}

class CustomCurvedEdges extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path =Path();
    path.lineTo(0,size.height);
    final firstCurve = Offset(0,size.height-20);
    final lastCurve = Offset(30,size.height-20);
    path.quadraticBezierTo(firstCurve.dx,firstCurve.dy, lastCurve.dx, lastCurve.dy);
    final secondFirtCurve = Offset(0,size.height-20);
    final secondLastCurve = Offset(size.width -30,size.height-20);
    path.quadraticBezierTo(secondFirtCurve.dx,secondFirtCurve.dy, secondLastCurve.dx, secondLastCurve.dy);
    final thirdFirtCurve = Offset(size.width ,size.height-20);
    final thirdLastCurve = Offset(size.width ,size.height);
    path.quadraticBezierTo(thirdFirtCurve.dx,thirdFirtCurve.dy, thirdLastCurve.dx, thirdLastCurve.dy);
    path.lineTo(size.width,0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}
