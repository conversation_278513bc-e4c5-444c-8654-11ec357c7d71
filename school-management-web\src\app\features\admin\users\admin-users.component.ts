import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin-users',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="admin-users">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <button class="back-btn" (click)="goBack()">
              <span class="icon">←</span>
              Back to Dashboard
            </button>
            <h1>👥 User Management</h1>
            <p>Manage students, teachers, and parents</p>
          </div>
          <div class="header-actions">
            <button class="add-user-btn" (click)="showAddUserModal = true">
              <span class="icon">➕</span>
              Add New User
            </button>
          </div>
        </div>
      </header>

      <!-- Filters and Search -->
      <section class="filters-section">
        <div class="filters-content">
          <div class="search-box">
            <input 
              type="text" 
              placeholder="Search users..." 
              [(ngModel)]="searchTerm"
              (input)="filterUsers()">
            <span class="search-icon">🔍</span>
          </div>
          
          <div class="filter-tabs">
            <button 
              class="filter-tab" 
              [class.active]="selectedFilter === 'all'"
              (click)="setFilter('all')">
              All Users ({{getTotalUsers()}})
            </button>
            <button 
              class="filter-tab" 
              [class.active]="selectedFilter === 'students'"
              (click)="setFilter('students')">
              Students ({{getStudentCount()}})
            </button>
            <button 
              class="filter-tab" 
              [class.active]="selectedFilter === 'teachers'"
              (click)="setFilter('teachers')">
              Teachers ({{getTeacherCount()}})
            </button>
            <button 
              class="filter-tab" 
              [class.active]="selectedFilter === 'parents'"
              (click)="setFilter('parents')">
              Parents ({{getParentCount()}})
            </button>
          </div>
        </div>
      </section>

      <!-- Users Table -->
      <section class="users-table-section">
        <div class="table-container">
          <table class="users-table">
            <thead>
              <tr>
                <th>User</th>
                <th>Role</th>
                <th>Email</th>
                <th>Status</th>
                <th>Last Login</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let user of filteredUsers" class="user-row">
                <td class="user-info">
                  <div class="user-avatar">{{user.avatar}}</div>
                  <div class="user-details">
                    <div class="user-name">{{user.name}}</div>
                    <div class="user-id">ID: {{user.id}}</div>
                  </div>
                </td>
                <td>
                  <span class="role-badge" [ngClass]="user.role">
                    {{user.role | titlecase}}
                  </span>
                </td>
                <td class="user-email">{{user.email}}</td>
                <td>
                  <span class="status-badge" [ngClass]="user.status">
                    {{user.status | titlecase}}
                  </span>
                </td>
                <td class="last-login">{{user.lastLogin}}</td>
                <td class="actions">
                  <button class="action-btn edit" (click)="editUser(user)">
                    <span class="icon">✏️</span>
                  </button>
                  <button class="action-btn delete" (click)="deleteUser(user)">
                    <span class="icon">🗑️</span>
                  </button>
                  <button class="action-btn more" (click)="showUserDetails(user)">
                    <span class="icon">👁️</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <!-- Add User Modal -->
      <div class="modal-overlay" *ngIf="showAddUserModal" (click)="showAddUserModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>➕ Add New User</h2>
            <button class="close-btn" (click)="showAddUserModal = false">✕</button>
          </div>
          <form class="add-user-form">
            <div class="form-row">
              <div class="form-group">
                <label>First Name</label>
                <input type="text" [(ngModel)]="newUser.firstName" name="firstName">
              </div>
              <div class="form-group">
                <label>Last Name</label>
                <input type="text" [(ngModel)]="newUser.lastName" name="lastName">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Email</label>
                <input type="email" [(ngModel)]="newUser.email" name="email">
              </div>
              <div class="form-group">
                <label>Role</label>
                <select [(ngModel)]="newUser.role" name="role">
                  <option value="">Select Role</option>
                  <option value="student">Student</option>
                  <option value="teacher">Teacher</option>
                  <option value="parent">Parent</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Phone</label>
                <input type="tel" [(ngModel)]="newUser.phone" name="phone">
              </div>
              <div class="form-group">
                <label>Status</label>
                <select [(ngModel)]="newUser.status" name="status">
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showAddUserModal = false">
                Cancel
              </button>
              <button type="button" class="save-btn" (click)="addUser()">
                <span class="icon">💾</span>
                Save User
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .admin-users {
      min-height: 100vh;
      background: #f5f7fa;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 30px;
      margin-bottom: 30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .back-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      margin-bottom: 16px;
      transition: all 0.3s ease;
    }

    .back-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .add-user-btn {
      background: #27ae60;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .add-user-btn:hover {
      background: #229954;
      transform: translateY(-2px);
    }

    .filters-section {
      max-width: 1400px;
      margin: 0 auto 30px auto;
      padding: 0 30px;
    }

    .filters-content {
      background: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 24px;
    }

    .search-box {
      position: relative;
      flex: 1;
      max-width: 400px;
    }

    .search-box input {
      width: 100%;
      padding: 12px 16px 12px 40px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      box-sizing: border-box;
    }

    .search-box input:focus {
      outline: none;
      border-color: #3498db;
    }

    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #7f8c8d;
    }

    .filter-tabs {
      display: flex;
      gap: 8px;
    }

    .filter-tab {
      background: #f8f9fa;
      border: none;
      padding: 10px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #7f8c8d;
      transition: all 0.3s ease;
    }

    .filter-tab.active {
      background: #3498db;
      color: white;
    }

    .filter-tab:hover:not(.active) {
      background: #e9ecef;
    }

    .users-table-section {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 30px;
    }

    .table-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }

    .users-table {
      width: 100%;
      border-collapse: collapse;
    }

    .users-table th {
      background: #f8f9fa;
      padding: 16px;
      text-align: left;
      font-weight: 600;
      color: #2c3e50;
      border-bottom: 1px solid #e1e5e9;
    }

    .users-table td {
      padding: 16px;
      border-bottom: 1px solid #f1f3f4;
    }

    .user-row:hover {
      background: #f8f9fa;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
    }

    .user-name {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 2px;
    }

    .user-id {
      font-size: 12px;
      color: #7f8c8d;
    }

    .role-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .role-badge.student {
      background: #e3f2fd;
      color: #1976d2;
    }

    .role-badge.teacher {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    .role-badge.parent {
      background: #e8f5e8;
      color: #388e3c;
    }

    .role-badge.admin {
      background: #fff3e0;
      color: #f57c00;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .status-badge.active {
      background: #d4edda;
      color: #155724;
    }

    .status-badge.inactive {
      background: #f8d7da;
      color: #721c24;
    }

    .status-badge.pending {
      background: #fff3cd;
      color: #856404;
    }

    .actions {
      display: flex;
      gap: 8px;
    }

    .action-btn {
      background: none;
      border: none;
      padding: 8px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-btn.edit:hover {
      background: #e3f2fd;
    }

    .action-btn.delete:hover {
      background: #ffebee;
    }

    .action-btn.more:hover {
      background: #f3e5f5;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .filters-content {
        flex-direction: column;
        gap: 16px;
      }

      .filter-tabs {
        flex-wrap: wrap;
      }

      .users-table {
        font-size: 14px;
      }

      .users-table th,
      .users-table td {
        padding: 12px 8px;
      }
    }
  `]
})
export class AdminUsersComponent {
  searchTerm = '';
  selectedFilter = 'all';
  showAddUserModal = false;

  newUser = {
    firstName: '',
    lastName: '',
    email: '',
    role: '',
    phone: '',
    status: 'active'
  };

  users = [
    {
      id: 'USR001',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'teacher',
      status: 'active',
      lastLogin: '2 hours ago',
      avatar: '👩‍🏫'
    },
    {
      id: 'USR002',
      name: 'Michael Chen',
      email: '<EMAIL>',
      role: 'student',
      status: 'active',
      lastLogin: '1 day ago',
      avatar: '👨‍🎓'
    },
    {
      id: 'USR003',
      name: 'Emily Davis',
      email: '<EMAIL>',
      role: 'parent',
      status: 'active',
      lastLogin: '3 hours ago',
      avatar: '👩‍💼'
    }
  ];

  filteredUsers = [...this.users];

  constructor(private router: Router) {}

  goBack(): void {
    this.router.navigate(['/admin']);
  }

  setFilter(filter: string): void {
    this.selectedFilter = filter;
    this.filterUsers();
  }

  filterUsers(): void {
    let filtered = [...this.users];

    if (this.selectedFilter !== 'all') {
      filtered = filtered.filter(user => user.role === this.selectedFilter);
    }

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term) ||
        user.id.toLowerCase().includes(term)
      );
    }

    this.filteredUsers = filtered;
  }

  getTotalUsers(): number {
    return this.users.length;
  }

  getStudentCount(): number {
    return this.users.filter(user => user.role === 'student').length;
  }

  getTeacherCount(): number {
    return this.users.filter(user => user.role === 'teacher').length;
  }

  getParentCount(): number {
    return this.users.filter(user => user.role === 'parent').length;
  }

  editUser(user: any): void {
    console.log('Edit user:', user);
  }

  deleteUser(user: any): void {
    if (confirm(`Are you sure you want to delete ${user.name}?`)) {
      this.users = this.users.filter(u => u.id !== user.id);
      this.filterUsers();
    }
  }

  showUserDetails(user: any): void {
    console.log('Show user details:', user);
  }

  addUser(): void {
    if (this.newUser.firstName && this.newUser.lastName && this.newUser.email && this.newUser.role) {
      const newUser = {
        id: `USR${String(this.users.length + 1).padStart(3, '0')}`,
        name: `${this.newUser.firstName} ${this.newUser.lastName}`,
        email: this.newUser.email,
        role: this.newUser.role,
        status: this.newUser.status,
        lastLogin: 'Never',
        avatar: this.getAvatarForRole(this.newUser.role)
      };

      this.users.push(newUser);
      this.filterUsers();
      this.showAddUserModal = false;
      this.resetNewUser();
    }
  }

  private getAvatarForRole(role: string): string {
    const avatars = {
      student: '👨‍🎓',
      teacher: '👩‍🏫',
      parent: '👨‍💼',
      admin: '👨‍💻'
    };
    return avatars[role as keyof typeof avatars] || '👤';
  }

  private resetNewUser(): void {
    this.newUser = {
      firstName: '',
      lastName: '',
      email: '',
      role: '',
      phone: '',
      status: 'active'
    };
  }
}
