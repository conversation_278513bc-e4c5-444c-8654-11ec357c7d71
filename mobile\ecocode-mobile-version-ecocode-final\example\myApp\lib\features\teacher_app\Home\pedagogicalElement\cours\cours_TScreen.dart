import 'dart:io';
import 'dart:typed_data';

import 'package:NovaSchool/commun/teacher_app/app_TnavBar.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:NovaSchool/models/messageResponse.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/services/message_services.dart';
import '../../../../../commun/app_background.dart';
import '../../../../../commun/parent_app/app_secondheadBar.dart';
import '../../../../../utils/constants/colors.dart';
import 'cours_TController.dart';

class TCoursScreen extends StatefulWidget {
  @override
  _TCoursScreenState createState() => _TCoursScreenState();
}

class _TCoursScreenState extends State<TCoursScreen> {
  List<MessageResponse> cours = [];
  bool _isLoading = true;
  int _itemsPerPage = 0;
  int _currentPageIndex = 0;
  final int _pageSize = 5;
  int _totalPages = 0;
  MessageServices msg = MessageServices();
  final FileDownloadService _fileDownloadService = FileDownloadService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateItemsPerPage();
    });
    _fetchCours();
  }
  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }
  Future<void> _fetchCours({int page = 0}) async {
    setState(() {
      _isLoading = true;
    });

    List<MessageResponse> fetchedCours = await msg.fetchMessages(
      type: "Cours",
      page: page,
      pageSize: _pageSize,
      isSender: true,
    );

    if (mounted) {
      setState(() {
        cours = fetchedCours;
        _totalPages = (fetchedCours.length / _pageSize).ceil();
        _isLoading = false;
      });
    }
  }

  Future<void> _downloadFiles(int messageId) async {
    await _fileDownloadService.downloadFiles(context, messageId);
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true;
      });
      _fetchCours(page: newIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    List<MessageResponse> currentPageCours = cours.isEmpty
        ? []
        : cours.skip(_currentPageIndex * _pageSize).take(_pageSize).toList();

    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: TeacherDrawer (),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            iconTheme: IconThemeData(
              color: Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            title: GestureDetector(
              onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => TeacherAppNavBar()), // remplace THomeScreen par ton écran d'accueil
                );
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),
                ],
              ),
            ),/*Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/logos/ecocode.png',
                  height: 150,
                  color: Colors.white,
                ),
                SizedBox(width: 10),

              ],
            ),*/
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),/*IconButton(
                icon: Icon(Icons.person_outline, color: Colors.white,size: 30,),
                onPressed: () async {
                  Map<String, String> userInfo = await _getUserInfo();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TProfilScreen(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                        email: userInfo['email'] ?? '',
                        profile: userInfo['profile'] ?? '',
                      ),
                    ),
                  );
                },
              ),*/
              /*IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  onPressed: () {},
                ),*/
            ],
          ),
        ),
      ),
      resizeToAvoidBottomInset: false,
      body: AppBackground(
        child: Column(
          children: [
             SecondHeadBar1(title: 'Cours', icon: Icons.book,),
            /*PrimaryHeaderContainer(
              child: Container(
                height: 100, // Adjust the height here
                child: SecondHeadBar(
                  title: 'Cours',
                  titleColor: Colors.white,
                  iconColor: Colors.white,
                ),
              ),
            ),*/
            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : cours.isEmpty
                      ? Center(
                          child: Text('Aucun cours disponible'),
                        )
                      : ListView.builder(
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          itemCount: currentPageCours.length,
                          itemBuilder: (context, index) {
                            MessageResponse coursItem = currentPageCours[index];
                            return Container(
                              height: 100.0, // Réduit la hauteur si nécessaire
                              child: Card(
                                  margin: EdgeInsets.symmetric(
                                      vertical: 4.0,
                                      horizontal:
                                          15), // Réduit l'espace autour du Card
                                  child: ListTile(
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: 2.0,
                                        horizontal:
                                            10.0), // Reduce padding inside the ListTile
                                    title: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        // Left section: objet and body (stacked)
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              // Display the object (subject) at the top
                                              Text(
                                                '${coursItem.messageEntity.objet[0].toUpperCase()}${coursItem.messageEntity.objet.substring(1)}',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 14,
                                                ),
                                              ),
                                              SizedBox(
                                                  height:
                                                      4), // Add some space between the object and body
                                              // Display the body (below the object)
                                              Text(
                                                '${coursItem.messageEntity.body[0].toUpperCase()}${coursItem.messageEntity.body.substring(1)}',
                                                style: TextStyle(
                                                  fontSize: 13,
                                                  color: Colors.black
                                                      .withOpacity(0.5),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        // Right section: confirmed status above file download
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            // Display "Validée" or "Non validée" status
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.circle,
                                                  color: coursItem.messageEntity
                                                          .confirmed
                                                      ? Colors
                                                          .green // Green for "Validée"
                                                      : Colors
                                                          .red, // Red for "Non validée"
                                                  size: 10,
                                                ),
                                                SizedBox(width: 5),
                                                Text(
                                                  coursItem.messageEntity
                                                          .confirmed
                                                      ? 'Validée'
                                                      : 'Non validée',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: coursItem
                                                            .messageEntity
                                                            .confirmed
                                                        ? Colors
                                                            .green // Text color for "Validée"
                                                        : Colors
                                                            .red, // Text color for "Non validée"
                                                  ),
                                                ),
                                              ],
                                            ),
                                            // Add some space between the status and the file information
                                            SizedBox(height: 10),
                                            // Display file information and download icon (only if files exist)
                                            if (coursItem
                                                .messageEntity.existFile)
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    '${coursItem.messageEntity.numberOfFile} fichiers',
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.black
                                                          .withOpacity(0.5),
                                                    ),
                                                  ),
                                                  Tooltip(
                                                    message: 'Télécharger',
                                                    child: IconButton(
                                                      icon: Icon(
                                                        Icons
                                                            .file_download_outlined,
                                                        color:
                                                            Colors.lightGreen,
                                                        size: 30,
                                                      ),
                                                      onPressed: () async {
                                                        await _downloadFiles(
                                                            coursItem
                                                                .messageEntity
                                                                .idMessage);
                                                      },
                                                    ),
                                                  ),
                                                ],
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  )),
                            );
                          },
                        ),
            ),
            Padding(
              padding: const EdgeInsets.all(4.0), // Adjust padding
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: Icon(Icons.arrow_back_ios,
                        color: _currentPageIndex > 0
                            ? Color(0xFF4099FF)
                            : Colors.grey,
                        size: 20),
                    onPressed: _currentPageIndex > 0
                        ? () {
                            _updatePageIndex(_currentPageIndex - 1);
                          }
                        : null,
                  ),
                  Text(
                    '${_currentPageIndex + 1} / $_totalPages',
                    style: TextStyle(color: Colors.grey, fontSize: 15),
                  ),
                  IconButton(
                    icon: Icon(Icons.arrow_forward_ios,
                        color: _currentPageIndex < _totalPages - 1
                            ? Color(0xFF4099FF)
                            : Colors.grey,
                        size: 20),
                    onPressed: _currentPageIndex < _totalPages - 1
                        ? () {
                            _updatePageIndex(_currentPageIndex + 1);
                          }
                        : null,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Color(0xFF4099FF),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => TCoursController(
                      onCoursAdded: () {
                        _fetchCours();
                      },
                    )),
          );
        },
        child: Icon(
          Icons.add,
          color: AppColors.light,
        ),
      ),
    );
  }
}
