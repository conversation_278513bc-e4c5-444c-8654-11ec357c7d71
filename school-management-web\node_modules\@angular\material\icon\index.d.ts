export { b as MAT_ICON_DEFAULT_OPTIONS, c as MAT_ICON_LOCATION, e as MAT_ICON_LOCATION_FACTORY, f as MatIcon, a as MatIconDefaultOptions, d as MatIconLocation, M as MatIconModule } from '../icon-module.d-COXCrhrh.js';
export { f as ICON_REGISTRY_PROVIDER, e as ICON_REGISTRY_PROVIDER_FACTORY, I as IconOptions, d as IconResolver, M as MatIconRegistry, S as SafeResourceUrlWithIconOptions, c as getMatIconFailedToSanitizeLiteralError, b as getMatIconFailedToSanitizeUrlError, g as getMatIconNameNotFoundError, a as getMatIconNoHttpProviderError } from '../icon-registry.d-BVwP8t9_.js';
import '@angular/core';
import '../common-module.d-C8xzHJDr.js';
import '@angular/cdk/bidi';
import '../palette.d-BSSFKjO6.js';
import '@angular/common/http';
import '@angular/platform-browser';
import 'rxjs';
