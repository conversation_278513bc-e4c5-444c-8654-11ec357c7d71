
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/models/extensionConfig.dart';
import 'package:NovaSchool/services/extensionConfigService.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui' as ui;
import '../../services/auth_service.dart';
import '../../services/eleve_services.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/images.dart';
import '../../utils/constants/text.dart';
import 'appBar_config.dart';
import 'package:shared_preferences/shared_preferences.dart';
class ChildProfile {
  final String name;
  final int id;
  final String firstName;
  final String nomClasse;
  final String? profileImage;
  ChildProfile(this.name, this.id, this.firstName, this.nomClasse,
      {this.profileImage});
}

class PAppHeadBar extends StatefulWidget {
  const PAppHeadBar({Key? key}) : super(key: key);

  @override
  _PAppHeadBarState createState() => _PAppHeadBarState();
}

class _PAppHeadBarState extends State<PAppHeadBar> {
  late ValueNotifier<ChildProfile?> _selectedProfileNotifier;
  late PNavigationController _navigationController;
  final int configId = 1;
  final ExtensionConfigService service = ExtensionConfigService();
  final FileDownloadService fileDownloadService = FileDownloadService();

  @override
  void initState() {
    super.initState();
    _selectedProfileNotifier = Get.find<ValueNotifier<ChildProfile?>>();
    _navigationController = Get.find<PNavigationController>();

    // Fetch the profiles and set the first child by default
    _fetchChildProfiles().then((profiles) {
      if (profiles.isNotEmpty &&
          _navigationController.selectedProfile.value == null) {
        // Select the first profile by default
        ChildProfile firstProfile = profiles.first;
        // _selectedProfileNotifier.value = firstProfile;
        //  _saveSelectedChildToLocalStorage(firstProfile);
        _navigationController.setSelectedProfile(firstProfile);
      }
    });
  }

  Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
      print('No user ID found in SharedPreferences');
      return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
      List<EleveInfoPersoDTO> eleves =
      await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
      return eleves.map((eleve) {
        return ChildProfile(
            eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
      }).toList();
    } catch (e) {
      print('Failed to fetch profiles: $e');
      return [];
    }
  }

  Future<void> _saveSelectedChildToLocalStorage(ChildProfile profile) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt('eleveId', profile.id);
    await prefs.setString('eleveName', profile.name);
    await prefs.setString('eleveFirstName', profile.firstName);
    await prefs.setString('nomClasse', profile.nomClasse);

    // Notify listeners to refresh UI
    _selectedProfileNotifier.notifyListeners();
  }

  void _showSnackbar(BuildContext context, String message, Color color,
      {String? actionLabel, VoidCallback? onPressed}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        action: actionLabel != null
            ? SnackBarAction(label: actionLabel, onPressed: onPressed ?? () {})
            : null,
      ),
    );
  }

  Future<void> _downloadFile(BuildContext context, String url) async {
    try {
      fileDownloadService.showLoadingDialog(
          context, 'Téléchargement en cours...');
      if (await canLaunch(url)) {
        await launch(url);
        _showSnackbar(
          context,
          'Téléchargement démarré. Vérifiez votre dossier Téléchargements.',
          Colors.green,
        );
      } else {
        _showSnackbar(
          context,
          'Impossible de lancer le téléchargement.',
          Colors.red,
        );
      }
    } catch (e) {
      _showSnackbar(
        context,
        'Une erreur s\'est produite pendant le téléchargement.',
        Colors.red,
      );
    } finally {
      Navigator.of(context).pop();
    }
  }


  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<ChildProfile>>(
      future: _fetchChildProfiles(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return AppBarConfig(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Image.asset(
                        'assets/logos/ecocode.png',
                        color: Colors.white.withOpacity(0.89),
                        colorBlendMode: BlendMode.srcIn,
                        width: 120, height: 120 // Ajuste selon ton design
                    ),
                    /*Text(
                      AppText.Apptitle,
                      style: GoogleFonts.dmSerifDisplay(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),*/
                  ],
                )
              ],
            ),
            actions: [
              /*Tooltip(
                message: 'Déconnexion',
                child: IconButton(
                  icon: Icon(Icons.logout),
                  color: Colors.white,
                  iconSize: 20,
                  onPressed: () async {
                    await AuthService.logoutKeycloak();
                  },
                ),
              ),*/
            ],
          );
        } else if (snapshot.hasError) {
          return AppBarConfig(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Image.asset(
                        'assets/logos/ecocode.png',
                        color: Colors.white.withOpacity(0.89),
                        colorBlendMode: BlendMode.srcIn,
                        width: 120, height: 120 // Ajuste selon ton design
                    ),
                    /*Text(
                      AppText.Apptitle,
                      style: GoogleFonts.dmSerifDisplay(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),*/
                  ],
                )
              ],
            ),
            actions: [
              /*Tooltip(
                message: 'Déconnexion',
                child: IconButton(
                  icon: Icon(Icons.logout),
                  color: Colors.white,
                  iconSize: 20,
                  onPressed: () async {
                    await AuthService.logoutKeycloak();
                  },
                ),
              ),*/
            ],
          );
        } else {
          List<ChildProfile> childProfiles = snapshot.data ?? [];

          return FutureBuilder<ExtensionConfig>(
            future: service.getById(configId), // Fetch the configuration
            builder: (context, snapshot) {
              final config = snapshot.data;

              return AppBarConfig(
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /*Row(
                      children: [*/
                    Padding(
                      padding: EdgeInsets.only(top: 30), // Ajuste la hauteur du logo
                      child:
                      Image.asset(
                          'assets/logos/ecocode.png',
                          color: Colors.white.withOpacity(0.89),
                          colorBlendMode: BlendMode.srcIn,
                          width: 120, height: 120 // Ajuste selon ton design
                      ),
                      /* Text(
                          AppText.Apptitle,
                          style: GoogleFonts.dmSerifDisplay(
                            fontSize: 20,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),*/


                      /* ],
                    ),*/),
                    SizedBox(height: 8),
                    if (snapshot.connectionState == ConnectionState.waiting)
                      CircularProgressIndicator(
                        color: Colors.white70,
                        strokeWidth: 2.0,
                      ), // Optional loader while fetching config
                    if (config != null &&
                        config.message != null &&
                        config.message!.isNotEmpty)
                      GestureDetector(
                        onTap: config.path != null && config.path!.isNotEmpty
                            ? () => _downloadFile(context, config.path!)
                            : null,
                        child: Row(
                          children: [
                            if (config.path != null && config.path!.isNotEmpty)
                              Icon(
                                Icons.download,
                                color: Color(0xFFC79113),
                                size: 23,
                              ),
                            SizedBox(width: 8),
                            Flexible(
                              child: Text(
                                config.message!,
                                style: GoogleFonts.dmSerifDisplay(
                                  fontSize: 23,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFFC79113),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                actions: [
              PopupMenuButton<ChildProfile>(
              color: Colors.white,
                icon: Tooltip(
                  message: 'Élève',
                  child: Image.asset(
                    AppImages.userIcon,
                    width: 40,
                    height: 40,
                  ),
                ),
                offset: Offset(-20, 40),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    bottomRight: Radius.circular(30),
                    bottomLeft: Radius.circular(30),
                  ),
                ),
                onSelected: (ChildProfile profile) async {

                  _navigationController.setSelectedProfile(profile);
                  print('Selected Profile: ${profile.firstName} ${profile.name}');

                  _navigationController.resetToHome();
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(builder: (context) => ParentAppNavBar()),
                        (route) => false, // Remove all previous routes
                  );
                },
                itemBuilder: (BuildContext context) {
                  return childProfiles.map((ChildProfile profile) {
                    return PopupMenuItem<ChildProfile>(
                      value: profile,
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 10),
                        child: Row(
                          children: [
                            CircleAvatar(
                              backgroundImage: AssetImage(AppImages.imageLogo),
                              backgroundColor: Color(0xF93361E7),
                            ),
                            SizedBox(width: 10),
                            Text(
                              '${profile.firstName ?? ''} ${profile.name ?? ''}',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList();
                },
              ),


                  /* Tooltip(
                    message: 'Déconnexion',
                    child: IconButton(
                      icon: Icon(Icons.logout),
                      color: Color(0xFFC79113),
                      iconSize: 20,
                      onPressed: () async {
                        await AuthService.logoutKeycloak();
                      },
                    ),
                  ),*/
                ],
              );
            },
          );
        }
      },
    );
  }
}


/***
 * import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
    import 'package:NovaSchool/models/extensionConfig.dart';
    import 'package:NovaSchool/services/extensionConfigService.dart';
    import 'package:NovaSchool/services/fileDownloadService.dart';
    import 'package:flutter/material.dart';
    import 'package:get/get.dart';
    import 'package:google_fonts/google_fonts.dart';
    import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
    import 'package:url_launcher/url_launcher.dart';
    import 'dart:ui' as ui;
    import '../../services/auth_service.dart';
    import '../../services/eleve_services.dart';
    import '../../utils/constants/colors.dart';
    import '../../utils/constants/images.dart';
    import '../../utils/constants/text.dart';
    import 'appBar_config.dart';
    import 'package:shared_preferences/shared_preferences.dart';

    class ChildProfile {
    final String name;
    final int id;
    final String firstName;
    final String nomClasse;
    final String? profileImage;
    ChildProfile(this.name, this.id, this.firstName, this.nomClasse,
    {this.profileImage});
    }

    class PAppHeadBar extends StatefulWidget {
    const PAppHeadBar({Key? key}) : super(key: key);

    @override
    _PAppHeadBarState createState() => _PAppHeadBarState();
    }

    class _PAppHeadBarState extends State<PAppHeadBar> {
    late ValueNotifier<ChildProfile?> _selectedProfileNotifier;
    late PNavigationController _navigationController;
    final int configId = 1;
    final ExtensionConfigService service = ExtensionConfigService();
    final FileDownloadService fileDownloadService = FileDownloadService();

    @override
    void initState() {
    super.initState();
    _selectedProfileNotifier = Get.find<ValueNotifier<ChildProfile?>>();
    _navigationController = Get.find<PNavigationController>();

    // Fetch the profiles and set the first child by default
    _fetchChildProfiles().then((profiles) {
    if (profiles.isNotEmpty &&
    _navigationController.selectedProfile.value == null) {
    // Select the first profile by default
    ChildProfile firstProfile = profiles.first;
    // _selectedProfileNotifier.value = firstProfile;
    //  _saveSelectedChildToLocalStorage(firstProfile);
    _navigationController.setSelectedProfile(firstProfile);
    }
    });
    }

    Future<List<ChildProfile>> _fetchChildProfiles() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? idTuteurKeyklock = prefs.getString('userId');

    if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
    print('No user ID found in SharedPreferences');
    return [];
    }

    EleveServices eleveServices = Get.find<EleveServices>();
    try {
    List<EleveInfoPersoDTO> eleves =
    await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
    return eleves.map((eleve) {
    return ChildProfile(
    eleve.nomEleve, eleve.idEleve, eleve.prenomEleve, eleve.nomClasse);
    }).toList();
    } catch (e) {
    print('Failed to fetch profiles: $e');
    return [];
    }
    }

    Future<void> _saveSelectedChildToLocalStorage(ChildProfile profile) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setInt('eleveId', profile.id);
    await prefs.setString('eleveName', profile.name);
    await prefs.setString('eleveFirstName', profile.firstName);
    await prefs.setString('nomClasse', profile.nomClasse);

    // Notify listeners to refresh UI
    _selectedProfileNotifier.notifyListeners();
    }

    void _showSnackbar(BuildContext context, String message, Color color,
    {String? actionLabel, VoidCallback? onPressed}) {
    ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
    content: Text(message),
    backgroundColor: color,
    action: actionLabel != null
    ? SnackBarAction(label: actionLabel, onPressed: onPressed ?? () {})
    : null,
    ),
    );
    }

    Future<void> _downloadFile(BuildContext context, String url) async {
    try {
    fileDownloadService.showLoadingDialog(
    context, 'Téléchargement en cours...');
    if (await canLaunch(url)) {
    await launch(url);
    _showSnackbar(
    context,
    'Téléchargement démarré. Vérifiez votre dossier Téléchargements.',
    Colors.green,
    );
    } else {
    _showSnackbar(
    context,
    'Imposssible de lancer le téléchargement.',
    Colors.red,
    );
    }
    } catch (e) {
    _showSnackbar(
    context,
    'Une erreur s\'est produite pendant le téléchargement.',
    Colors.red,
    );
    } finally {
    Navigator.of(context).pop();
    }
    }

    @override
    Widget build(BuildContext context) {
    return FutureBuilder<List<ChildProfile>>(
    future: _fetchChildProfiles(),
    builder: (context, snapshot) {
    if (snapshot.connectionState == ConnectionState.waiting) {
    return AppBarConfig(
    title: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
    Row(
    children: [
    Text(
    AppText.Apptitle,
    style: GoogleFonts.dmSerifDisplay(
    fontSize: 20,
    fontWeight: FontWeight.w500,
    color: Colors.white,
    ),
    ),
    ],
    )
    ],
    ),
    actions: [
    /* Tooltip(
    message: 'Déconnexion',
    child: IconButton(
    icon: Icon(Icons.logout),
    color: Colors.white,
    iconSize: 20,
    onPressed: () async {
    await AuthService.logoutKeycloak();
    },
    ),
    ),*/
    ],
    );
    } else if (snapshot.hasError) {
    return AppBarConfig(
    title: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
    Row(
    children: [
    Text(
    AppText.Apptitle,
    style: GoogleFonts.dmSerifDisplay(
    fontSize: 20,
    fontWeight: FontWeight.w500,
    color: Colors.white,
    ),
    ),
    ],
    )
    ],
    ),
    actions: [
    /*Tooltip(
    message: 'Déconnexion',
    child: IconButton(
    icon: Icon(Icons.logout),
    color: Colors.white,
    iconSize: 20,
    onPressed: () async {
    await AuthService.logoutKeycloak();
    },
    ),
    ),*/
    ],
    );
    } else {
    List<ChildProfile> childProfiles = snapshot.data ?? [];

    return FutureBuilder<ExtensionConfig>(
    future: service.getById(configId), // Fetch the configuration
    builder: (context, snapshot) {
    final config = snapshot.data;

    return AppBarConfig(
    title: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
    Row(
    children: [
    Text(
    AppText.Apptitle,
    style: GoogleFonts.dmSerifDisplay(
    fontSize: 20,
    fontWeight: FontWeight.w500,
    color: Colors.white,
    ),
    ),
    ],
    ),
    SizedBox(height: 8),
    if (snapshot.connectionState == ConnectionState.waiting)
    CircularProgressIndicator(
    color: Colors.white70,
    strokeWidth: 2.0,
    ), // Optional loader while fetching config
    if (config != null &&
    config.message != null &&
    config.message!.isNotEmpty)
    GestureDetector(
    onTap: config.path != null && config.path!.isNotEmpty
    ? () => _downloadFile(context, config.path!)
    : null,
    child: Row(
    children: [
    if (config.path != null && config.path!.isNotEmpty)
    Icon(
    Icons.download,
    color: Colors.yellow,
    size: 20,
    ),
    SizedBox(width: 8),
    Flexible(
    child: Text(
    config.message!,
    style: GoogleFonts.dmSerifDisplay(
    fontSize: 18,
    fontWeight: FontWeight.w400,
    color: Colors.yellow,
    ),
    ),
    ),
    ],
    ),
    ),
    ],
    ),
    actions: [
    PopupMenuButton<ChildProfile>(
    color: Colors.white,
    icon: Tooltip(
    message: 'Élève',
    child: Image.asset(
    AppImages.userIcon,
    width: 30,
    height: 30,
    ),
    ),
    offset: Offset(-20, 40),
    elevation: 0,
    shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.only(
    topLeft: Radius.circular(30),
    bottomRight: Radius.circular(30),
    bottomLeft: Radius.circular(30),
    ),
    ),
    onSelected: (ChildProfile profile) async {
    _navigationController.setSelectedProfile(profile);
    print(
    'Selected Profile: ${profile.firstName} ${profile.name}');
    },
    itemBuilder: (BuildContext context) {
    return childProfiles.map((ChildProfile profile) {
    return PopupMenuItem<ChildProfile>(
    value: profile,
    child: Container(
    padding: EdgeInsets.symmetric(vertical: 10),
    child: Row(
    children: [
    CircleAvatar(
    backgroundImage:
    AssetImage(AppImages.imageLogo),
    backgroundColor: Color(0xFF4099FF),
    ),
    SizedBox(width: 10),
    Text(
    '${profile.firstName ?? ''} ${profile.name ?? ''}',
    style: TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: Colors.black,
    ),
    ),
    ],
    ),
    ),
    );
    }).toList();
    },
    ),
    /*Tooltip(
    message: 'Déconnexion',
    child: IconButton(
    icon: Icon(Icons.logout),
    color: Colors.white,
    iconSize: 20,
    onPressed: () async {
    await AuthService.logoutKeycloak();
    },
    ),
    ),*/
    ],
    );
    },
    );
    }
    },
    );
    }
    }

 */