import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:NovaSchool/commun/teacher_app/app_TnavBar.dart';
import 'package:NovaSchool/services/eleve_services.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/features/admin/home/<USER>';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/features/welcome/welcome_contenu.dart';
import 'package:NovaSchool/features/welcome/welcome_language.dart';
import 'package:NovaSchool/services/auth_service.dart';
import '../../commun/app_background.dart';

// class WelcomeScreen extends StatelessWidget {
//   const WelcomeScreen({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       resizeToAvoidBottomInset: false,
//       body: AppBackground( /// background de l'interface
//         child: Stack(
//           children: [
//             // WelcomeLanguage(), /// Widget pour sélectionner la langue
//             WelcomeContenu(), /// Widget pour le contenu de la page d'accueil
//           ],
//         ),
//       ),
//     );
//   }
// }

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({Key? key}) : super(key: key);

  @override
  _WelcomeScreenState createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  final _loginService = Get.find<AuthService>();
  final _tuteurService = EleveServices();

  @override
  void initState() {
    super.initState();
    _checkAuthentication();
  }

  Future<void> _checkAuthentication() async {
    // Check if the user is authenticated
    if (await _loginService.isAuthenticated()) {
      // Check the user role and redirect accordingly
      if (await _loginService.isAdmin()) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => AdminAppNavBar()),
        );
      } else if (await _loginService.isParent()) {
        await _tuteurService.updateFirebaseToken(); 

        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => ParentAppNavBar()),
        );
      } else if (await _loginService.isEnseiganat()) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => TeacherAppNavBar())//THomeScreen()),
        );
      } else {
        _showErrorDialog('Rôle utilisateur inconnu ou non autorisé.');
      }
    }
  }

  Future<void> _updateFirebaseTokenAndNavigate(Widget destination) async {
    await _tuteurService.updateFirebaseToken(); 
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => destination),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Erreur'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: AppBackground(
        child: Stack(
          children: [
            WelcomeContenu(),
          ],
        ),
      ),
    );
  }
}
