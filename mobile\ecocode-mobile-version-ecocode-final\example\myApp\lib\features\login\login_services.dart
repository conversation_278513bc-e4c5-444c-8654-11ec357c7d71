import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
//import '../../commun/base_url.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';


class LoginService {
  Future<Map<String, dynamic>> login(String email, String password) async {
    String baseURL = dotenv.get('baseURL');
    final String loginUrl = '$baseURL/auth/login';

    try {
      final response = await http.post(
        Uri.parse(loginUrl),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(<String, String>{
          'email': email,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = json.decode(response.body);

        String token = responseData['token'];
        String role = responseData['role'];
        int userId = responseData['userId'];

        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', token);
        await prefs.setString('role', role);
        await prefs.setInt('userId', userId);

        return {'token': token, 'role': role, 'userId': userId};
      } else {
        throw Exception('Failed to login with status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to login: $e');
    }
  }
}
