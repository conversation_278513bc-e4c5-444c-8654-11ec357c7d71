import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class PHomeCategories extends StatelessWidget {
  final VoidCallback onPress;
  final String? icon;
  final IconData? iconData;
  final String title;
  final int unreadCount;
  const PHomeCategories({
    Key? key,
    required this.onPress,
    this.icon,
    this.iconData,
    required this.title,
    this.unreadCount = 0,

  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPress,
      child: Container(
        margin: const EdgeInsets.only(bottom: 30),
        width: MediaQuery.of(context).size.width / 2.5,//2.3,
        height: 100,//MediaQuery.of(context).size.height / 6,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.5),
              spreadRadius: 0,
              blurRadius: 5,
              offset: Offset(2, 4),
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [

            Column(
              mainAxisAlignment: MainAxisAlignment.center,//spaceAround,
              //crossAxisAlignment: CrossAxisAlignment.center,
              children: [

                icon != null 
                  ? Image(image: AssetImage(icon!), height: 60, width: 60)
                  : Icon(iconData, size: 55, color: Color(0xFF0993D7)),//Colors.lightBlue),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(//dmSerifDisplay(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1B2F38),
                  ),
                ),
              ],
            ),
            // Show badge only if unreadCount > 0
            if (unreadCount > 0)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    '$unreadCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
