import 'dart:convert';
import 'package:http/http.dart' as http;
//import '../commun/base_url.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class ClassService {

  final String ClassUrl = dotenv.get('baseURL');

  Future<List<String>> getNiveaux() async {
    final response = await http.get(Uri.parse('$ClassUrl/classes'));
    if (response.statusCode == 200) {
      List<dynamic> data = json.decode(response.body);
      return data.map((item) => item['niveau'] as String).toList();
    } else {
      throw Exception('Failed to load niveaux');
    }
  }

  Future<List<String>> getClassesByNiveau(String niveau) async {
    final response = await http.get(Uri.parse('$ClassUrl/classes/$niveau'));
    if (response.statusCode == 200) {
      Map<String, dynamic> data = json.decode(response.body);
      List<dynamic> classes = data['classes']; // Assuming 'classes' is the key in your JSON response
      return classes.map((item) => item['classe'] as String).toList();
    } else {
      throw Exception('Failed to load classes');
    }
  }
}
