import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Define the Message model class
class Message {
  final String sender;
  final String receiver; // Add receiver field
  final String text;

  Message({required this.sender, required this.receiver, required this.text});

  // Deserialize JSON to Message object
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      sender: json['sender'],
      receiver: json['receiver'],
      text: json['text'],
    );
  }

  // Serialize Message object to JSON
  Map<String, dynamic> toJson() {
    return {
      'sender': sender,
      'receiver': receiver,
      'text': text,
    };
  }
}

// Define the MessageService for storing and retrieving messages
class MessageService {
  static const _key = 'messages';

  static Future<List<Message>> getMessages(String currentUser, String selectedUser) async {
    final prefs = await SharedPreferences.getInstance();
    final messages = prefs
        .getStringList(_key)
        ?.map((e) => Message.fromJson(Map<String, dynamic>.from(json.decode(e))))
        .where((msg) => (msg.sender == currentUser && msg.receiver == selectedUser) || (msg.sender == selectedUser && msg.receiver == currentUser))
        .toList() ?? [];
    return messages;
  }

  static Future<void> addMessage(Message message) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonMessage = json.encode(message.toJson());
    List<String>? messages = prefs.getStringList(_key);
    if (messages == null) {
      messages = [jsonMessage];
    } else {
      messages.add(jsonMessage);
    }
    await prefs.setStringList(_key, messages);
  }
}

// Define the ChatScreen widget
class ChatScreen extends StatefulWidget {
  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final List<Message> _messages = [];
  final String currentUser = 'Admin'; // Set the current user here
  String selectedUser = 'User1'; // Default selected user, change as per your logic

  @override
  void initState() {
    super.initState();
    _loadMessages(selectedUser);
  }

  void _loadMessages(String selectedUser) async {
    try {
      final messages = await MessageService.getMessages(currentUser, selectedUser);
      setState(() {
        _messages.clear();
        _messages.addAll(messages);
      });
    } catch (e) {
      print('Error loading messages: $e');
    }
  }

  void _sendMessage(String text) async {
    try {
      if (text.isNotEmpty) {
        final message = Message(sender: currentUser, receiver: selectedUser, text: text);
        setState(() {
          _messages.add(message);
        });
        await MessageService.addMessage(message);
        _controller.clear();
      }
    } catch (e) {
      print('Error sending message: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Chat with $selectedUser'),
        actions: <Widget>[
          PopupMenuButton<String>(
            onSelected: (String user) {
              setState(() {
                selectedUser = user;
              });
              _loadMessages(selectedUser);
            },
            itemBuilder: (BuildContext context) {
              return <PopupMenuEntry<String>>[
                PopupMenuItem<String>(
                  value: 'mohamed charfi', // Change values as per your user list
                  child: Text('MohamedCharfi'),
                ),
                PopupMenuItem<String>(
                  value: 'zouari islem',
                  child: Text('zouari islem'),
                ),
                // Add more users as required
              ];
            },
          ),
        ],
      ),
      body: Column(
        children: <Widget>[
          Expanded(
            child: ListView.builder(
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return ListTile(
                  title: Text(message.sender),
                  subtitle: Text(message.text),
                  // Differentiate messages sent by the current user
                  // You can customize the appearance here
                  tileColor: message.sender == currentUser ? Colors.blueGrey[100] : null,
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: InputDecoration(
                      hintText: 'Enter message...',
                    ),
                    onSubmitted: _sendMessage,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send),
                  onPressed: () => _sendMessage(_controller.text),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    home: ChatScreen(),
  ));
}
