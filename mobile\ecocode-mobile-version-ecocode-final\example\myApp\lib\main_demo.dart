import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/commun/teacher_app/app_TnavBar.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/eleve_services.dart';
import 'package:NovaSchool/utils/theme/theme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'features/welcome/welcome_screen.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

final navigatorKey = GlobalKey<NavigatorState>();

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("Background message received: ${message.notification?.title}");
  // if (message.notification != null) {
  //   // showNotification(message);
  // }
}

FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

void setupFlutterNotifications() async {
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/demo');
  const InitializationSettings initializationSettings =
      InitializationSettings(android: initializationSettingsAndroid);

  await flutterLocalNotificationsPlugin.initialize(initializationSettings);

  // const AndroidNotificationChannel channel = AndroidNotificationChannel(
  //   'high_importance_channel', 
  //   'High Importance Notifications',
  //   description: 'Channel for important notifications',
  //   importance: Importance.high,
  //   playSound: true,
  //   sound: RawResourceAndroidNotificationSound('notification'),
  //   enableVibration: true,
  // );

  // final AndroidFlutterLocalNotificationsPlugin? androidPlatform =
  //     flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
  //         AndroidFlutterLocalNotificationsPlugin>();

  // await androidPlatform?.createNotificationChannel(channel);
}


Future<void> showNotification(RemoteMessage message) async {
  int notificationId = DateTime.now().millisecondsSinceEpoch ~/ 1000;
  const AndroidNotificationDetails androidPlatformChannelSpecifics =
      AndroidNotificationDetails(
    'high_importance_channel', // Change this if needed
    'High Importance Notifications',
    importance: Importance.max,
    priority: Priority.high,
    showWhen: false,
    playSound: true,
    sound: RawResourceAndroidNotificationSound('notification'),
    enableVibration: true,
  );

  const NotificationDetails platformChannelSpecifics =
      NotificationDetails(android: androidPlatformChannelSpecifics);

  await flutterLocalNotificationsPlugin.show(
    notificationId,
    message.notification?.title,
    message.notification?.body,
    platformChannelSpecifics,
  );
}

Future<void> main() async {
  await dotenv.load(fileName: ".env.demo");
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  setupFlutterNotifications();
  Get.put(AuthService());
  final notifier = ValueNotifier<ChildProfile?>(null);
  Get.put(notifier);
  Get.put(EleveServices());

  SharedPreferences prefs = await SharedPreferences.getInstance();
  final storedName = prefs.getString('eleveName');
  final storedFirstName = prefs.getString('eleveFirstName');
  final storedId = prefs.getInt('eleveId');
  final nomClasse = prefs.getString('nomClasse') ?? '';

  if (storedName != null && storedId != null && storedFirstName != null) {
    notifier.value = ChildProfile(
      storedName,
      storedId,
      storedFirstName,
      nomClasse,
    );
  }
  runApp(const App());
}



class App extends StatefulWidget {
  const App({Key? key}) : super(key: key);

  @override
  _AppState createState() => _AppState();
}

class _AppState extends State<App> {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  @override
  void initState() {
    super.initState();
    _initFirebaseMessaging();
  }

  void _initFirebaseMessaging() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print("Foreground message received: ${message.notification?.title}");
      showNotification(message);
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print("User tapped on notification: ${message.notification?.title}");
    });

    _getToken();
  }

  void _getToken() async {
    String? token = await _firebaseMessaging.getToken();
    print("FCM Token------------------------: $token");
  }

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      themeMode: ThemeMode.system,
      theme: AppTheme.lightTheme,
      home: WelcomeScreen(),
    );
  }
}
