import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../../commun/base_url.dart';
import 'classe_observation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class ObservationService {
  // String baseURL = dotenv.get('baseURL');
  // final String baseUrl = "$baseURL/observations";

  late final String baseURL;
  late final String baseUrl;

  ObservationService(){
    baseURL = dotenv.get('baseURL');
    baseUrl = '$baseURL/observations';
  }

  Future<String> _getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('token') ?? '';
  }

  Future<List<Observation>> getAllObservations() async {
    final token = await _getToken();

    final response = await http.get(
      Uri.parse('$baseUrl/getAllObservations'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      List<dynamic> jsonData = json.decode(response.body);
      return jsonData.map((e) => Observation.fromJson(e)).toList();
    } else {
      throw Exception('Failed to load observations');
    }
  }

  Future<void> addObservation(Observation observation) async {
    final token = await _getToken();

    final response = await http.post(
      Uri.parse('$baseUrl/createObservation'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(observation.toJson()),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to add observation');
    }
  }

  Future<void> updateObservation(Observation observation) async {
    final token = await _getToken();

    final response = await http.put(
      Uri.parse('$baseUrl/modifierObservation/${observation.id}'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(observation.toJson()),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to update observation');
    }
  }

  Future<void> deleteObservation(String observationId) async {
    final token = await _getToken();

    final response = await http.delete(
      Uri.parse('$baseUrl/supprimerObservation/$observationId'),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to delete observation');
    }
  }
}
