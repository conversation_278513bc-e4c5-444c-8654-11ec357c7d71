import 'package:flutter/material.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:NovaSchool/utils/theme/appBarTheme.dart';
import 'package:NovaSchool/utils/theme/outlinedButtonTheme.dart';
import 'package:NovaSchool/utils/theme/textFieldTheme.dart';
import 'package:NovaSchool/utils/theme/textTheme.dart';
import 'bottomSheetTheme.dart';
import 'elevatedButtonTheme.dart';

class AppTheme {
  AppTheme._();

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    fontFamily: 'Poppins',
    brightness: Brightness.light,
    primaryColor: AppColors.light,
    scaffoldBackgroundColor: AppColors.light,
    textTheme: appTextTheme.lightTextTheme,
    appBarTheme: appBarTheme.lightAppBarTheme,
    bottomSheetTheme: BottomSheetTheme.lightBottomSheetTheme,
    elevatedButtonTheme: appElevatedButtonTheme.lightElevatedButtonTheme,
    outlinedButtonTheme: appOutlinedButtonTheme.lightOutlinedButtonTheme,
    inputDecorationTheme: TextFormFieldTheme.lightInputDecorationTheme,
  );
  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    fontFamily: 'Poppins',
    brightness: Brightness.dark,
    primaryColor: Colors.lightBlue,
    scaffoldBackgroundColor: Colors.black,
    textTheme: appTextTheme.darkTextTheme,
    appBarTheme: appBarTheme.darkAppBarTheme,
    bottomSheetTheme: BottomSheetTheme.darkBottomSheetTheme,
    elevatedButtonTheme: appElevatedButtonTheme.darkElevatedButtonTheme,
    outlinedButtonTheme: appOutlinedButtonTheme.darkOutlinedButtonTheme,
    inputDecorationTheme: TextFormFieldTheme.darkInputDecorationTheme,
  );
}
