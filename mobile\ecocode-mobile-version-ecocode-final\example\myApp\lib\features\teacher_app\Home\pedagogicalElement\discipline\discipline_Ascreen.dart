import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/discipline/discipline_Aajout.dart';
import 'package:NovaSchool/models/messageResponse.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TDisciplineScreen extends StatefulWidget {
  @override
  _TDisciplineScreenState createState() => _TDisciplineScreenState();
}

class _TDisciplineScreenState extends State<TDisciplineScreen> {
  List<MessageResponse> disciplines = [];
  bool _isLoading = true;
  int _itemsPerPage = 0;
  int _currentPageIndex = 0;
  final int _pageSize = 5;
  int _totalPages = 0;
  MessageServices msg = MessageServices();
  final FileDownloadService _fileDownloadService = FileDownloadService();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateItemsPerPage();
    });
    _fetchDisciplines();
  }

  Future<void> _fetchDisciplines({int page = 0}) async {
    setState(() {
      _isLoading = true;
    });

    List<MessageResponse> fetchedDisciplines = await msg.fetchMessages(
      type: "Discipline",
      page: page,
      pageSize: _pageSize,
      isSender: true,
    );

    if (mounted) {
      setState(() {
        disciplines = fetchedDisciplines;
        _totalPages = (fetchedDisciplines.length / _pageSize).ceil();
        _isLoading = false;
      });
    }
  }

  Future<void> _downloadFiles(int messageId) async {
    await _fileDownloadService.downloadFiles(context, messageId);
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true;
      });
      _fetchDisciplines(page: newIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    List<MessageResponse> currentPageDisciplines = disciplines.isEmpty
        ? []
        : disciplines
            .skip(_currentPageIndex * _pageSize)
            .take(_pageSize)
            .toList();

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: AppBackground(
        child: Column(
          children: [
            //SecondHeadBar1 (title: 'Discilines',icon: Icons.add_box_outlined,),

            //SecondHeadBar(title: 'disciplines'),
            /*PrimaryHeaderContainer(
              child: Container(
                height: 100, // Adjust the height here
                child: SecondHeadBar(
                  title: 'Discipline',
                  titleColor: Colors.white,
                  iconColor: Colors.white,
                ),
              ),
            ),*/
            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : disciplines.isEmpty
                      ? Center(
                          child: Text('Aucun disciplines disponible'),
                        )

                      : ListView.builder(
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          itemCount: currentPageDisciplines.length,
                          itemBuilder: (context, index) {
                            MessageResponse disciplineItem =
                                currentPageDisciplines[index];
                            return Container(

                              height: 100.0, // Réduit la hauteur si nécessaire
                              child: Card(

                                  margin: EdgeInsets.symmetric(

                                      vertical: 4.0,
                                      horizontal:
                                          15),
                                  color: Colors.white ,// Réduit l'espace autour du Card
                                  child: ListTile(
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: 2.0,
                                        horizontal:
                                            10.0),
                                    // Reduce padding inside the ListTile
                                    title: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        // Left section: objet and body (stacked)
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              // Display the object (subject) at the top
                                              Text(
                                                '${disciplineItem.messageEntity.objet[0].toUpperCase()}${disciplineItem.messageEntity.objet.substring(1)}',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 14,
                                                ),
                                              ),
                                              SizedBox(
                                                  height:
                                                      4), // Add some space between the object and body
                                              // Display the body (below the object)
                                              Text(
                                                '${disciplineItem.messageEntity.body[0].toUpperCase()}${disciplineItem.messageEntity.body.substring(1)}',
                                                style: TextStyle(
                                                  fontSize: 13,
                                                  color: Colors.black
                                                      .withOpacity(0.5),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        // Right section: confirmed status above file download
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            // Display "Validée" or "Non validée" status
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.circle,
                                                  color: disciplineItem
                                                          .messageEntity
                                                          .confirmed
                                                      ? Colors
                                                          .green // Green for "Validée"
                                                      : Colors
                                                          .red, // Red for "Non validée"
                                                  size: 10,
                                                ),
                                                SizedBox(width: 5),
                                                Text(
                                                  disciplineItem.messageEntity
                                                          .confirmed
                                                      ? 'Validée'
                                                      : 'Non validée',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                    color: disciplineItem
                                                            .messageEntity
                                                            .confirmed
                                                        ? Colors
                                                            .green // Text color for "Validée"
                                                        : Colors
                                                            .red, // Text color for "Non validée"
                                                  ),
                                                ),
                                              ],
                                            ),
                                            // Add some space between the status and the file information
                                            SizedBox(height: 10),
                                            // Display file information and download icon (only if files exist)
                                            if (disciplineItem
                                                .messageEntity.existFile)
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    '${disciplineItem.messageEntity.numberOfFile} fichiers',
                                                    style: TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.black
                                                          .withOpacity(0.5),
                                                    ),
                                                  ),
                                                  Tooltip(
                                                    message: 'Télécharger',
                                                    child: IconButton(
                                                      icon: Icon(
                                                        Icons
                                                            .file_download_outlined,
                                                        color:
                                                            Colors.lightGreen,
                                                        size: 30,
                                                      ),
                                                      onPressed: () async {
                                                        await _downloadFiles(
                                                            disciplineItem
                                                                .messageEntity
                                                                .idMessage);
                                                      },
                                                    ),
                                                  ),
                                                ],
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  )),
                            );
                          },
                        ),
            ),
            Padding(
              padding: const EdgeInsets.all(4.0), // Adjust padding
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: Icon(Icons.arrow_back_ios,
                        color: _currentPageIndex > 0
                            ? Color(0xFF4099FF)
                            : Colors.grey,
                        size: 20),
                    onPressed: _currentPageIndex > 0
                        ? () {
                            _updatePageIndex(_currentPageIndex - 1);
                          }
                        : null,
                  ),
                  Text(
                    '${_currentPageIndex + 1} / $_totalPages',
                    style: TextStyle(color: Colors.grey, fontSize: 15),
                  ),
                  IconButton(
                    icon: Icon(Icons.arrow_forward_ios,
                        color: _currentPageIndex < _totalPages - 1
                            ? Color(0xFF4099FF)
                            : Colors.grey,
                        size: 20),
                    onPressed: _currentPageIndex < _totalPages - 1
                        ? () {
                            _updatePageIndex(_currentPageIndex + 1);
                          }
                        : null,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Color(0xFF4099FF),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => TAjoutDiscipline(
                      onDisciplinesAdded: () {
                        _fetchDisciplines();
                      },
                    )),
          );
        },
        child: Icon(
          Icons.add,
          color: AppColors.light,
        ),
      ),
    );
  }
}
