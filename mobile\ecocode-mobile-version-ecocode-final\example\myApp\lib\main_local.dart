// import 'dart:io';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
// import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
// import 'package:NovaSchool/features/admin/home/<USER>';
// import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
// import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
// import 'package:NovaSchool/services/eleve_services.dart';
// import 'package:NovaSchool/utils/constants/images.dart';
// import 'package:logger/logger.dart';
// import 'package:openid_client/openid_client.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'error_scrren.dart';
// import 'openid_io.dart' if (dart.library.html) 'openid_browser.dart';
// import 'package:get/get.dart';
// import 'package:NovaSchool/utils/theme/theme.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:NovaSchool/features/welcome/welcome_contenu.dart';
// import '../../commun/app_background.dart';
// import 'package:jose/jose.dart';
// import 'package:NovaSchool/utils/constants/colors.dart';
// import 'package:NovaSchool/utils/constants/text.dart';
// import 'package:NovaSchool/services/cookies_management.dart';

// const keycloakUri = 'baseUrl';  /// from baseUrl
// const scopes = ['profile', 'roles'];
// final log = Logger();
// Credential? credential;
// late final Client client;
// Future<Client> getClient() async {
//   var uri = Uri.parse(keycloakUri);
//   if (!kIsWeb && Platform.isAndroid) uri = uri.replace(host: 'ipaddress');  /// from baseUrl
//   var clientId = 'ishrakschool-back';  /// from baseUrl

//   try {
//     var issuer = await Issuer.discover(uri);
//     return Client(issuer, clientId);
//   } catch (e) {
//     throw Exception('Failed to connect to Keycloak: $e');
//   }
// }

// Future<void> main() async {
//   WidgetsFlutterBinding.ensureInitialized();

//   final notifier = ValueNotifier<ChildProfile?>(null);
//   Get.put(notifier);
//   Get.put(EleveServices());

//   SharedPreferences prefs = await SharedPreferences.getInstance();
//   final storedName = prefs.getString('eleveName');
//   final storedFirstName = prefs.getString('eleveFirstName');
//   final storedId = prefs.getInt('eleveId');

//   if (storedName != null && storedId != null && storedFirstName != null) {
//     notifier.value = ChildProfile(
//       storedName,
//       storedId,
//       storedFirstName,
//     );
//   }

//   try {
//     client = await getClient(); // Await the Future<Client>
//     credential = await getRedirectResult(client,
//         scopes: scopes); // Await the Future<Credential?>
//     runApp(const MyApp());
//   } catch (e) {
//     log.e('Initialization failed: $e');
//     runApp(GetMaterialApp(
//       debugShowCheckedModeBanner: false,
//       themeMode: ThemeMode.system,
//       theme: AppTheme.lightTheme,
//       home: WelcomeScreen(),
//     ));
//   }
// }

// class MyApp extends StatelessWidget {
//   const MyApp({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return GetMaterialApp(
//       debugShowCheckedModeBanner: false,
//       themeMode: ThemeMode.system,
//       theme: AppTheme.lightTheme, // light mode
//       // darkTheme: AppTheme.darkTheme,
//       home: WelcomeScreen(), // page de bienvenue
//     );
//   }
// }

// class WelcomeScreen extends StatefulWidget {
//   const WelcomeScreen({Key? key}) : super(key: key);

//   @override
//   State<WelcomeScreen> createState() => _WelcomeScreenState();
// }

// class _WelcomeScreenState extends State<WelcomeScreen> {
//   UserInfo? userInfo;
//   List<String>? roles;
//   String? name;
//   String? lastname;
//   String? userId;
//   bool isLoading = false;

//   @override
//   void initState() {
//     super.initState();

//     if (credential != null) {
//       remove('AUTH_SESSION_ID');
//       remove('AUTH_SESSION_ID_LEGACY');
//       remove('KEYCLOAK_IDENTITY');
//       remove('KEYCLOAK_IDENTITY_LEGACY');
//       remove('KEYCLOAK_SESSION');
//       remove('KEYCLOAK_SESSION_LEGACY');
//       setState(() {
//         isLoading = true;
//       });
//       credential!.getUserInfo().then((userInfo) {
//         setState(() {
//           this.userInfo = userInfo;
//           this.name = userInfo.givenName;
//           this.lastname = userInfo.familyName;
//           this.userId = userInfo.subject;
//           this.roles = extractRolesFromToken(credential!.accessToken!);
//           isLoading = false;
//           WidgetsBinding.instance.addPostFrameCallback((_) {
//             if (userInfo != null) {
//               navigateBasedOnRoles();
//             }
//           });
//           storeTokenAndRoles(
//               credential!.accessToken!, roles!, name!, lastname!, userId!);
//         });
//       });
//     }
//   }

//   Future<List<ChildProfile>> _fetchChildProfiles() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String? idTuteurKeyklock = prefs.getString('userId');

//     if (idTuteurKeyklock == null || idTuteurKeyklock.isEmpty) {
//       print('No user ID found in SharedPreferences');
//       return [];
//     }

//     EleveServices eleveServices = Get.find<EleveServices>();
//     try {
//       List<EleveInfoPersoDTO> eleves =
//           await eleveServices.getEleveByTuteurKeyklock(idTuteurKeyklock);
//       return eleves.map((eleve) {
//         return ChildProfile(
//           eleve.nomEleve,
//           eleve.idEleve,
//           eleve.prenomEleve,
//         );
//       }).toList();
//     } catch (e) {
//       print('Failed to fetch profiles: $e');
//       return [];
//     }
//   }

//   Future<void> navigateBasedOnRoles() async {
//     if (roles!.contains(adminRole)) {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => AHomeScreen()),
//       );
//     } else if (roles!.contains(enseignantRole)) {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => THomeScreen()),
//       );
//     } else if (roles!.contains(parentRole)) {
//       List<ChildProfile> childProfiles = await _fetchChildProfiles();

//       if (childProfiles.isNotEmpty) {
//         // Set the first child's eleveId in local storage
//         SharedPreferences prefs = await SharedPreferences.getInstance();
//         await prefs.setInt('eleveId', childProfiles[0].id);
//         await prefs.setString('eleveName', childProfiles[0].name);
//         await prefs.setString('eleveFirstName', childProfiles[0].firstName);

//         print('Set first eleveId: ${childProfiles[0].id} in local storage');
//       } else {
//         print('No child profiles found for parent role.');
//       }
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => ParentAppNavBar()),
//       );
//     } else {
//       Navigator.pushReplacement(
//         context,
//         MaterialPageRoute(builder: (context) => AHomeScreen()),
//       );
//     }
//   }

//   Future<void> storeTokenAndRoles(String token, List<String> roles, String name,
//       String lastname, String userId) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     await prefs.setString('token', token);
//     await prefs.setStringList('roles', roles);
//     await prefs.setString('name', name);
//     await prefs.setString('lastname', lastname);
//     await prefs.setString('userId', userId);
//   }

//   List<String> extractRolesFromToken(String accessToken) {
//     log.i('Access Token: $accessToken'); // Log the access token string

//     final jwt = JsonWebToken.unverified(accessToken);
//     final claims = jwt.claims.toJson();

//     log.i('Claims: $claims'); // Log the entire claims including resource_access

//     final resourceAccess = claims['resource_access'] as Map<String, dynamic>?;

//     final roles = <String>[];
//     if (resourceAccess != null) {
//       final clientRoles =
//           resourceAccess['ishrakschool-back']?['roles'] as List<dynamic>?;
//       if (clientRoles != null) {
//         roles.addAll(clientRoles.map((role) => role.toString()));
//       }
//     }

//     return roles;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       resizeToAvoidBottomInset: false,
//       body: AppBackground(
//         child: Stack(
//           children: [
//             Center(
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: <Widget>[
//                   Flexible(
//                     child: const Image(
//                       image: AssetImage(AppImages.schoolLogo),
//                       width: 280,
//                     ),
//                   ),
//                   if (isLoading)
//                     Center(
//                       child: CircularProgressIndicator(),
//                     )
//                   else if (userInfo == null)
//                     Padding(
//                       padding: const EdgeInsets.symmetric(horizontal: 20.0),
//                       child: OutlinedButton(
//                         style: OutlinedButton.styleFrom(
//                           side: BorderSide.none,
//                           padding: EdgeInsets.zero,
//                         ),
//                         onPressed: () async {
//                           setState(() {
//                             isLoading = true;
//                           });
//                           try {
//                             var credential =
//                                 await authenticate(client, scopes: scopes);
//                             var userInfo = await credential.getUserInfo();
//                             setState(() {
//                               this.userInfo = userInfo;
//                               this.roles = extractRolesFromToken(
//                                   credential.idToken.toCompactSerialization());
//                               isLoading = false;
//                               navigateBasedOnRoles();
//                             });
//                           } catch (e) {
//                             setState(() {
//                               isLoading = false;
//                             });
//                             Navigator.pushReplacement(
//                               context,
//                               MaterialPageRoute(
//                                 builder: (context) => ErrorScreen(
//                                   errorMessage:
//                                       'Échec de la connexion. Veuillez vérifier votre connexion internet.',
//                                 ),
//                               ),
//                             );
//                           }
//                         },
//                         child: Container(
//                           height: 50,
//                           decoration: BoxDecoration(
//                             // gradient: const LinearGradient(
//                             color: Color(0xFF4099FF),
//                             // Color(0xFF4099FF),
//                             // ],
//                             // begin: Alignment.topLeft,
//                             // end: Alignment.bottomRight,
//                             // ),
//                             borderRadius:
//                                 const BorderRadius.all(Radius.circular(30)),
//                           ),
//                           child: const Center(
//                             child: Text(
//                               AppText.WelcomeButtonText,
//                               style: TextStyle(
//                                 fontSize: 18,
//                                 fontWeight: FontWeight.w500,
//                                 color: Color(0xFFFFFFFF),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/eleve_services.dart';
import 'package:NovaSchool/utils/theme/theme.dart';
import 'package:shared_preferences/shared_preferences.dart'; 
import 'features/welcome/welcome_screen.dart';

Future<void> main() async {
  await dotenv.load(fileName: ".env.local");
  // await dotenv.load(fileName: ".env.test");
  // await dotenv.load(fileName: ".env.demo");
  // await dotenv.load(fileName: ".env.ichrak");
  // await dotenv.load(fileName: ".env.horizon");
  // await dotenv.load(fileName: ".env.amana");
  // await dotenv.load(fileName: ".env.loujayn");
  // await dotenv.load(fileName: ".env.nova");
  WidgetsFlutterBinding.ensureInitialized();
  // await Firebase.initializeApp();
  // FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  Get.put(AuthService());
  final notifier = ValueNotifier<ChildProfile?>(null);
  Get.put(notifier);
  Get.put(EleveServices());

  SharedPreferences prefs = await SharedPreferences.getInstance();
  final storedName = prefs.getString('eleveName');
  final storedFirstName = prefs.getString('eleveFirstName');
  final storedId = prefs.getInt('eleveId');
  final nomClasse = prefs.getString('nomClasse') ?? '';

  if (storedName != null && storedId != null && storedFirstName != null) {
    notifier.value = ChildProfile(
      storedName,
      storedId,
      storedFirstName,
      nomClasse,
    );
  }
  runApp(const App());
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('Handling a background message: ${message.messageId}');
}

class App extends StatelessWidget {
  const App({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      themeMode: ThemeMode.system,
      theme: AppTheme.lightTheme,

      /// light mode
      // darkTheme: AppTheme.darkTheme,
      home: WelcomeScreen(),

      /// page de bienvenue
    );
  }
}
