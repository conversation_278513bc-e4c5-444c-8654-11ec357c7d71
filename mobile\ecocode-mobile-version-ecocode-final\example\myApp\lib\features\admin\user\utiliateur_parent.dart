import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:file_picker/file_picker.dart';

import 'parent_class.dart';
import 'parent_service.dart';

class ParentManagementWidget extends StatefulWidget {
  @override
  _ParentManagementWidgetState createState() => _ParentManagementWidgetState();
}

class _ParentManagementWidgetState extends State<ParentManagementWidget> {
  List<Parent> parents = [];
  bool isLoading = true;
  int currentPage = 0;
  int totalPages = 0;
  final int pageSize = 5;

  @override
  void initState() {
    super.initState();
    loadParents(currentPage);
  }

  Future<void> loadParents(int page) async {
    try {
      ParentService parentService = ParentService();
      Map<String, dynamic> result =
          await parentService.getParentsByPage(page, pageSize);

      // Extract data from the result
      List<dynamic> content = result['content'] ?? [];
      List<Parent> fetchedParents =
          content.map((data) => Parent.fromJson(data)).toList();
      int total =
          result['totalPages'] ?? 0; // Adjust based on your API response

      setState(() {
        parents = fetchedParents;
        totalPages = total;
        currentPage = page;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('Failed to load teachers: $e');
    }
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < totalPages) {
      loadParents(newIndex);
    }
  }

  Future<void> addParent(Parent parent) async {
    try {
      ParentService parentService = ParentService();
      await parentService.addParent(parent);

      setState(() {
        currentPage = 0;
        // parents.add(parent);
      });
      loadParents(currentPage);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Parent ajouté avec succès!'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );
    } catch (e) {
      print('Failed to add parent: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Échec de l\'ajout de ce parent: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  void showAddParentDialog() {
    final _formKey = GlobalKey<FormState>();
    final _nomTuteurController = TextEditingController();
    final _prenomTuteurController = TextEditingController();
    final _cinTuteurController = TextEditingController();
    final _numTelTuteurController = TextEditingController();
    EtatTuteur? _selectedEtat = EtatTuteur.Principale;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: Text('Ajouter un Parent',style: TextStyle(color: Colors.blue.shade800)),
          content:
          Theme(
            data: Theme.of(context).copyWith(
              inputDecorationTheme: InputDecorationTheme(
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue.shade100),
                ),
                labelStyle: TextStyle(color: Colors.black.withOpacity(0.6)),//blue.shade800),
              ),
            ),
        child:Container(
            width: double.maxFinite,
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(height: 10),
                    TextFormField(
                      controller: _cinTuteurController,
                      decoration: InputDecoration(labelText: 'CIN'),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez entrer un cin';
                        }
                        if (value.length != 8) {
                          return 'Le numéro CIN doit contenir 8 chiffres';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 10),
                    TextFormField(
                      controller: _nomTuteurController,
                      decoration:
                          InputDecoration(labelText: 'Nom d\'utilisateur'),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez entrer un nom d\'utilisateur';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 10),
                    TextFormField(
                      controller: _prenomTuteurController,
                      decoration: InputDecoration(labelText: 'Prénom'),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez entrer un prénom';
                        }
                        return null;
                      },
                    ),

                    SizedBox(height: 10),
                    TextFormField(
                      controller: _numTelTuteurController,
                      decoration: InputDecoration(labelText: 'Téléphone'),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez entrer un numéro téléphone';
                        }
                        if (value.length != 8) {
                          return 'Le numéro de téléphone doit contenir 8 chiffres';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 10),
                Theme(
                  data: Theme.of(context).copyWith(
                    canvasColor: Colors.white,
                    inputDecorationTheme: InputDecorationTheme(
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue.shade100),
                      ),
                      labelStyle: TextStyle(color: Colors.black.withOpacity(0.6)),//blue.shade800),
                    ),
                  ),

                  child:
                    DropdownButtonFormField<EtatTuteur>(
//changer la couleur du background backgroundColor: Colors.white,
                      decoration: InputDecoration(labelText: 'Etat'),
                      value: _selectedEtat,
                      items: EtatTuteur.values.map((EtatTuteur etat) {
                        return DropdownMenuItem<EtatTuteur>(


                          value: etat,
                          child: Text(etat.toString().split('.').last),
                        );
                      }).toList(),
                      onChanged: (EtatTuteur? value) {
                        setState(() {
                          _selectedEtat = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Veuillez sélectionner un état';
                        }
                        return null;
                      },

                    ),),
                    SizedBox(height: 10),
                  ],
                ),
              ),
            ),
          ),),
          actions: [
            OutlinedButton(

              onPressed: () {
                Navigator.of(context).pop();
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Color(0xFF4099FF), // Texte bleu
                side: BorderSide(color: Color(0xFF4099FF)), // Bordure bleue
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  final newParent = Parent(
                    nomTuteur: _nomTuteurController.text,
                    prenomTuteur: _prenomTuteurController.text,
                    cinTuteur: _cinTuteurController.text,
                    numTelTuteur: _numTelTuteurController.text,
                    etatTuteur: _selectedEtat,
                  );

                  addParent(newParent).then((_) {
                    Navigator.of(context).pop();
                  });
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF4099FF), // Bleu un peu désaturé
                foregroundColor: Colors.white, // Texte blanc
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: Text('Ajouter'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Liste des Parents',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 10),
          isLoading
              ? Center(child: CircularProgressIndicator())
              : parents.isEmpty
                  ? Center(
                      child: Text('Aucun parent disponible',
                          style: TextStyle(fontSize: 18, color: Colors.grey)))
                  : Column(
                      children: parents.map((parent) {
                        return Container(
                          margin: EdgeInsets.symmetric(vertical: 5),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(5),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.5),
                                spreadRadius: 2,
                                blurRadius: 3,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ListTile(
                            title: Text(
                                '${parent.nomTuteur} ${parent.prenomTuteur}'),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // IconButton(
                                //   icon: Icon(Icons.edit, color: Color(0xFF8FE178)),
                                //   onPressed: () {
                                //     // Logique pour modifier l'parent
                                //     // Peut-être afficher une boîte de dialogue ou une nouvelle page
                                //   },
                                // ),
                                // IconButton(
                                //   icon:
                                //       Icon(Icons.delete, color: Color(0xFFE17878)),
                                //   onPressed: () {
                                //     showDialog(
                                //       context: context,
                                //       builder: (BuildContext context) {
                                //         return AlertDialog(
                                //           title: Text("Confirmation"),
                                //           content: Text(
                                //               "Voulez-vous vraiment supprimer ce parent ?"),
                                //           actions: [
                                //             TextButton(
                                //               onPressed: () {
                                //                 Navigator.of(context).pop();
                                //               },
                                //               child: Text("Annuler",  style: TextStyle(color: Color(0xFF4099FF)),),
                                //             ),
                                //             TextButton(
                                //               onPressed: () {
                                //                 // Logique pour supprimer le parent
                                //                 // Peut-être appeler une méthode de service
                                //                 Navigator.of(context).pop();
                                //               },
                                //               child: Text("Supprimer",  style: TextStyle(color: Color(0xFF4099FF)),),
                                //             ),
                                //           ],
                                //         );
                                //       },
                                //     );
                                //   },
                                // ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
          SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.all(4.0), // Réduit le padding du bas
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(Icons.arrow_back_ios,
                      color: currentPage > 0 ? Color(0xFF4099FF) : Colors.grey,
                      size: 15),
                  onPressed: currentPage > 0
                      ? () {
                          _updatePageIndex(currentPage - 1);
                        }
                      : null,
                ),
                Text(
                  '${currentPage + 1} / $totalPages',
                  style: TextStyle(color: Colors.grey, fontSize: 15),
                ),
                IconButton(
                  icon: Icon(Icons.arrow_forward_ios,
                      color: currentPage < totalPages - 1
                          ? Color(0xFF4099FF)
                          : Colors.grey,
                      size: 15),
                  onPressed: currentPage < totalPages - 1
                      ? () {
                          _updatePageIndex(currentPage + 1);
                        }
                      : null,
                ),
              ],
            ),
          ),
          SizedBox(height: 10),
          ElevatedButton(
            onPressed: showAddParentDialog,
            child: Text('Ajouter un Parent'),
          ),
        ],
      ),
    );
  }
}
