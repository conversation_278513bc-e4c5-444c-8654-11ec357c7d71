import 'package:flutter/material.dart';
import 'package:NovaSchool/features/parent_app/profil/student_form.dart';
import 'package:NovaSchool/models/EleveInfoPersoDTO.dart';
import 'package:NovaSchool/utils/constants/colors.dart';

class PProfilForm extends StatelessWidget {
  final EleveInfoPersoDTO profile;
  final String nomClasse;

  const PProfilForm({
    Key? key,
    required this.profile,
    required this.nomClasse,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
            color: AppColors.light,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                spreadRadius: 0,
                blurRadius: 5,
                offset: Offset(0, 1),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                buildRowWithIconAndText(Icons.person, "Nom", profile.nomEleve),
                buildSeparator(),
                buildRowWithIconAndText(Icons.person_outline, "Prénom", profile.prenomEleve),
                buildSeparator(),
                buildRowWithIconAndText(Icons.person_outline, "Identifiant", profile.identifiantUnique),
                buildSeparator(),
               // buildRowWithIconAndText(Icons.person, "Nom en Arabe", profile.prenomArabeEleve),
                buildSeparator(),
                // buildRowWithIconAndText(Icons.person_outline, "Prénom en Arabe", profile.prenomArabeEleve),
                // buildSeparator(),
                buildRowWithIconAndText(Icons.calendar_month, "Date de naissance", profile.dateNaissanceEleve?.toLocal().toString().split(' ')[0] ?? 'Non spécifiée'),
                buildSeparator(),
                //buildRowWithIconAndText(
                 // (profile.sexeEleve?.toLowerCase().startsWith('g') ?? false) ? Icons.male : Icons.female,
                 // "Sexe",
                 // profile.sexeEleve ?? 'Non spécifié',
               // ),
                buildSeparator(),
                buildRowWithIconAndText(Icons.school, "Classe", profile.niveauClasse+ ' '+ profile.nomClasse),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
