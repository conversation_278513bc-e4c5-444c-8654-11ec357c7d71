.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

// Header Section
.dashboard-header {
  margin-bottom: 30px;
}

.welcome-section {
  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }

  .page-subtitle {
    color: #666;
    margin: 0;
    font-size: 16px;
  }
}

// Student Info Card
.student-info-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;

  .student-avatar {
    .avatar-img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #e9ecef;
    }
  }

  .student-details {
    flex: 1;

    .student-name {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    .student-class {
      color: #666;
      margin: 0;
      font-size: 16px;
    }
  }

  .student-actions {
    .btn {
      padding: 10px 20px;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.3s ease;
      cursor: pointer;
      border: none;

      &.btn-outline {
        background: transparent;
        border: 2px solid #007bff;
        color: #007bff;

        &:hover {
          background: #007bff;
          color: white;
        }
      }
    }
  }
}

// Stats Grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
  }

  .stat-content {
    flex: 1;

    .stat-value {
      font-size: 24px;
      font-weight: 700;
      color: #333;
      margin: 0 0 4px 0;
    }

    .stat-title {
      color: #666;
      margin: 0 0 4px 0;
      font-size: 14px;
    }

    .stat-trend {
      font-size: 12px;
      font-weight: 500;
    }
  }

  &.stat-success {
    .stat-icon {
      background: linear-gradient(135deg, #28a745, #20c997);
    }
    .stat-trend {
      color: #28a745;
    }
  }

  &.stat-info {
    .stat-icon {
      background: linear-gradient(135deg, #17a2b8, #007bff);
    }
    .stat-trend {
      color: #17a2b8;
    }
  }

  &.stat-warning {
    .stat-icon {
      background: linear-gradient(135deg, #ffc107, #fd7e14);
    }
    .stat-trend {
      color: #ffc107;
    }
  }

  &.stat-primary {
    .stat-icon {
      background: linear-gradient(135deg, #007bff, #6f42c1);
    }
    .stat-trend {
      color: #007bff;
    }
  }
}

// Content Grid
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .card-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .view-all-link {
      color: #007bff;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .card-content {
    padding: 0;
  }
}

// Activity List
.activity-list {
  .activity-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 24px;
    border-bottom: 1px solid #f8f9fa;

    &:last-child {
      border-bottom: none;
    }

    .activity-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      font-size: 18px;
      flex-shrink: 0;
    }

    .activity-content {
      flex: 1;

      .activity-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin: 0 0 4px 0;
      }

      .activity-description {
        color: #666;
        margin: 0 0 4px 0;
        font-size: 13px;
      }

      .activity-time {
        color: #999;
        font-size: 12px;
      }
    }
  }
}

// Event List
.event-list {
  .event-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 24px;
    border-bottom: 1px solid #f8f9fa;

    &:last-child {
      border-bottom: none;
    }

    .event-date {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 80px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 8px;

      .event-day {
        font-size: 12px;
        font-weight: 600;
        color: #333;
      }

      .event-time {
        font-size: 11px;
        color: #666;
      }
    }

    .event-content {
      flex: 1;

      .event-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin: 0 0 4px 0;
      }

      .event-type {
        font-size: 12px;
        color: #666;
        text-transform: capitalize;
      }
    }
  }
}

// Quick Actions
.quick-actions {
  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 20px 0;
  }
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    color: #007bff;
  }

  .action-icon {
    font-size: 32px;
    color: #007bff;
  }

  .action-label {
    font-weight: 500;
    text-align: center;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .student-info-card {
    flex-direction: column;
    text-align: center;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
