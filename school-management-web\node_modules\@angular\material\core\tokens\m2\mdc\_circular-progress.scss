@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, circular-progress);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // The thickness of the progress spinner's active indicator.
    active-indicator-width: 4px,
    // The diameter of the spinner
    size: 48px,
    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    // Angular Material does not support four color spinners
    four-color-active-indicator-one-color: null,
    // Angular Material does not support four color spinners
    four-color-active-indicator-two-color: null,
    // Angular Material does not support four color spinners
    four-color-active-indicator-three-color: null,
    // Angular Material does not support four color spinners
    four-color-active-indicator-four-color: null
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: primary) {
  @return (
    // The color of the progress spinner's active indicator.
    active-indicator-color: inspection.get-theme-color($theme, $palette-name)
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
