@use 'sass:map';
@use '../../../style/sass-utils';
@use '../../token-definition';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, paginator);

/// Generates custom tokens for the mat-paginator.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-paginator
@function get-tokens($systems, $exclude-hardcoded, $token-slots) {
  $tokens: sass-utils.merge-all(
    token-definition.generate-typography-tokens($systems, container-text, body-small),
    (
      container-text-color: map.get($systems, md-sys-color, on-surface),
      container-background-color: map.get($systems, md-sys-color, surface),
      enabled-icon-color: map.get($systems, md-sys-color, on-surface-variant),
      disabled-icon-color: sass-utils.safe-color-change(
          map.get($systems, md-sys-color, on-surface), $alpha: 0.38),
      select-trigger-text-size: map.get($systems, md-sys-typescale, body-small-size),
    )
  );

  @return token-definition.namespace-tokens($prefix, $tokens, $token-slots);
}
