
import 'dart:convert';

//import '../../../commun/base_url.dart';
import 'admin_class.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AdminService {
  Future<Admin> getAdminProfile(int userId, String token) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/adminstrateur/profil/$userId';

    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = json.decode(response.body);
        return Admin.fromJson(responseData);
      } else {
        throw Exception('Failed to get admin profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get admin profile: $e');
    }
  }
}
