{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/menu/testing/menu-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessLoader,\n  HarnessPredicate,\n  TestElement,\n} from '@angular/cdk/testing';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {MenuHarnessFilters, MenuItemHarnessFilters} from './menu-harness-filters';\n\n/** Harness for interacting with a mat-menu in tests. */\nexport class MatMenuHarness extends ContentContainerComponentHarness<string> {\n  private _documentRootLocator = this.documentRootLocatorFactory();\n\n  /** The selector for the host element of a `MatMenu` instance. */\n  static hostSelector = '.mat-mdc-menu-trigger';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a menu with specific attributes.\n   * @param options Options for filtering which menu instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatMenuHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: MenuHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption(\n      'triggerText',\n      options.triggerText,\n      (harness, text) => HarnessPredicate.stringMatches(harness.getTriggerText(), text),\n    );\n  }\n\n  /** Whether the menu is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const disabled = (await this.host()).getAttribute('disabled');\n    return coerceBooleanProperty(await disabled);\n  }\n\n  /** Whether the menu is open. */\n  async isOpen(): Promise<boolean> {\n    return !!(await this._getMenuPanel());\n  }\n\n  /** Gets the text of the menu's trigger element. */\n  async getTriggerText(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Focuses the menu. */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /** Blurs the menu. */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the menu is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /** Opens the menu. */\n  async open(): Promise<void> {\n    if (!(await this.isOpen())) {\n      return (await this.host()).click();\n    }\n  }\n\n  /** Closes the menu. */\n  async close(): Promise<void> {\n    const panel = await this._getMenuPanel();\n    if (panel) {\n      return panel.click();\n    }\n  }\n\n  /**\n   * Gets a list of `MatMenuItemHarness` representing the items in the menu.\n   * @param filters Optionally filters which menu items are included.\n   */\n  async getItems(\n    filters?: Omit<MenuItemHarnessFilters, 'ancestor'>,\n  ): Promise<MatMenuItemHarness[]> {\n    const panelId = await this._getPanelId();\n    if (panelId) {\n      return this._documentRootLocator.locatorForAll(\n        MatMenuItemHarness.with({\n          ...(filters || {}),\n          ancestor: `#${panelId}`,\n        } as MenuItemHarnessFilters),\n      )();\n    }\n    return [];\n  }\n\n  /**\n   * Clicks an item in the menu, and optionally continues clicking items in subsequent sub-menus.\n   * @param itemFilter A filter used to represent which item in the menu should be clicked. The\n   *     first matching menu item will be clicked.\n   * @param subItemFilters A list of filters representing the items to click in any subsequent\n   *     sub-menus. The first item in the sub-menu matching the corresponding filter in\n   *     `subItemFilters` will be clicked.\n   */\n  async clickItem(\n    itemFilter: Omit<MenuItemHarnessFilters, 'ancestor'>,\n    ...subItemFilters: Omit<MenuItemHarnessFilters, 'ancestor'>[]\n  ): Promise<void> {\n    await this.open();\n    const items = await this.getItems(itemFilter);\n    if (!items.length) {\n      throw Error(`Could not find item matching ${JSON.stringify(itemFilter)}`);\n    }\n\n    if (!subItemFilters.length) {\n      return await items[0].click();\n    }\n\n    const menu = await items[0].getSubmenu();\n    if (!menu) {\n      throw Error(`Item matching ${JSON.stringify(itemFilter)} does not have a submenu`);\n    }\n    return menu.clickItem(...(subItemFilters as [Omit<MenuItemHarnessFilters, 'ancestor'>]));\n  }\n\n  protected override async getRootHarnessLoader(): Promise<HarnessLoader> {\n    const panelId = await this._getPanelId();\n    return this.documentRootLocatorFactory().harnessLoaderFor(`#${panelId}`);\n  }\n\n  /** Gets the menu panel associated with this menu. */\n  private async _getMenuPanel(): Promise<TestElement | null> {\n    const panelId = await this._getPanelId();\n    return panelId ? this._documentRootLocator.locatorForOptional(`#${panelId}`)() : null;\n  }\n\n  /** Gets the id of the menu panel associated with this menu. */\n  private async _getPanelId(): Promise<string | null> {\n    const panelId = await (await this.host()).getAttribute('aria-controls');\n    return panelId || null;\n  }\n}\n\nexport class MatMenuItemHarness extends ContentContainerComponentHarness<string> {\n  /** The selector for the host element of a `MatMenuItem` instance. */\n  static hostSelector = '.mat-mdc-menu-item';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a menu item with specific attributes.\n   * @param options Options for filtering which menu item instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatMenuItemHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: MenuItemHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('text', options.text, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getText(), text),\n      )\n      .addOption(\n        'hasSubmenu',\n        options.hasSubmenu,\n        async (harness, hasSubmenu) => (await harness.hasSubmenu()) === hasSubmenu,\n      );\n  }\n\n  /** Whether the menu is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const disabled = (await this.host()).getAttribute('disabled');\n    return coerceBooleanProperty(await disabled);\n  }\n\n  /** Gets the text of the menu item. */\n  async getText(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Focuses the menu item. */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /** Blurs the menu item. */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the menu item is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /** Clicks the menu item. */\n  async click(): Promise<void> {\n    return (await this.host()).click();\n  }\n\n  /** Whether this item has a submenu. */\n  async hasSubmenu(): Promise<boolean> {\n    return (await this.host()).matchesSelector(MatMenuHarness.hostSelector);\n  }\n\n  /** Gets the submenu associated with this menu item, or null if none. */\n  async getSubmenu(): Promise<MatMenuHarness | null> {\n    if (await this.hasSubmenu()) {\n      return new MatMenuHarness(this.locatorFactory);\n    }\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;AAkBA;AACM,MAAO,cAAe,SAAQ,gCAAwC,CAAA;AAClE,IAAA,oBAAoB,GAAG,IAAI,CAAC,0BAA0B,EAAE;;AAGhE,IAAA,OAAO,YAAY,GAAG,uBAAuB;AAE7C;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA8B,EAAE,EAAA;AAEhC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAClD,aAAa,EACb,OAAO,CAAC,WAAW,EACnB,CAAC,OAAO,EAAE,IAAI,KAAK,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,CAClF;;;AAIH,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC;AAC7D,QAAA,OAAO,qBAAqB,CAAC,MAAM,QAAQ,CAAC;;;AAI9C,IAAA,MAAM,MAAM,GAAA;QACV,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;;;AAIvC,IAAA,MAAM,cAAc,GAAA;QAClB,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;AAIxC,IAAA,MAAM,IAAI,GAAA;QACR,IAAI,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE;YAC1B,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;;AAKtC,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE;QACxC,IAAI,KAAK,EAAE;AACT,YAAA,OAAO,KAAK,CAAC,KAAK,EAAE;;;AAIxB;;;AAGG;IACH,MAAM,QAAQ,CACZ,OAAkD,EAAA;AAElD,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE;QACxC,IAAI,OAAO,EAAE;YACX,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAC5C,kBAAkB,CAAC,IAAI,CAAC;AACtB,gBAAA,IAAI,OAAO,IAAI,EAAE,CAAC;gBAClB,QAAQ,EAAE,CAAI,CAAA,EAAA,OAAO,CAAE,CAAA;aACE,CAAC,CAC7B,EAAE;;AAEL,QAAA,OAAO,EAAE;;AAGX;;;;;;;AAOG;AACH,IAAA,MAAM,SAAS,CACb,UAAoD,EACpD,GAAG,cAA0D,EAAA;AAE7D,QAAA,MAAM,IAAI,CAAC,IAAI,EAAE;QACjB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC7C,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,MAAM,KAAK,CAAC,CAAA,6BAAA,EAAgC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAE,CAAA,CAAC;;AAG3E,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;YAC1B,OAAO,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;QAG/B,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;QACxC,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,KAAK,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAA0B,wBAAA,CAAA,CAAC;;AAEpF,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAI,cAA6D,CAAC;;AAGvE,IAAA,MAAM,oBAAoB,GAAA;AAC3C,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE;QACxC,OAAO,IAAI,CAAC,0BAA0B,EAAE,CAAC,gBAAgB,CAAC,CAAI,CAAA,EAAA,OAAO,CAAE,CAAA,CAAC;;;AAIlE,IAAA,MAAM,aAAa,GAAA;AACzB,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE;AACxC,QAAA,OAAO,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAI,CAAA,EAAA,OAAO,EAAE,CAAC,EAAE,GAAG,IAAI;;;AAI/E,IAAA,MAAM,WAAW,GAAA;AACvB,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC;QACvE,OAAO,OAAO,IAAI,IAAI;;;AAIpB,MAAO,kBAAmB,SAAQ,gCAAwC,CAAA;;AAE9E,IAAA,OAAO,YAAY,GAAG,oBAAoB;AAE1C;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAkC,EAAE,EAAA;AAEpC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;aACtC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;aAExD,SAAS,CACR,YAAY,EACZ,OAAO,CAAC,UAAU,EAClB,OAAO,OAAO,EAAE,UAAU,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,UAAU,CAC3E;;;AAIL,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC;AAC7D,QAAA,OAAO,qBAAqB,CAAC,MAAM,QAAQ,CAAC;;;AAI9C,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;AAIxC,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC;;;AAIzE,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE;AAC3B,YAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC;;AAEhD,QAAA,OAAO,IAAI;;;;;;"}