import 'dart:io';
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/models/messageResponseDTO.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/models/messageEntity.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../../commun/app_background.dart';
import '../../../../../commun/parent_app/app_secondheadBar.dart';

class ExerciceDetailPage extends StatefulWidget {
  final List<MessageresponseDTO> exercicesList;
  ExerciceDetailPage({Key? key, required this.exercicesList}) : super(key: key);

  @override
  _ExerciceDetailPageState createState() => _ExerciceDetailPageState();
}

class _ExerciceDetailPageState extends State<ExerciceDetailPage> {
  FileDownloadService _fileDownloadService = FileDownloadService();
  TextEditingController _replyController = TextEditingController();
  List<Map<String, dynamic?>> _selectedFiles = [];
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  AuthService auth = AuthService();
  MessageServices msg = MessageServices();

  @override
  void initState() {
    super.initState();
    widget.exercicesList.sort((a, b) => b.idMessage.compareTo(a.idMessage));
  }

  Future<void> _pickFiles() async {
    List<Map<String, dynamic>> selectedFiles =
        await _fileDownloadService.pickFiles(context);
    setState(() {
      _selectedFiles = selectedFiles;
    });
  }

  Future<void> _createExercices() async {
    if (!_formKey.currentState!.validate()) return;

    if (_replyController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Veuillez remplir le champ'),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 3),
      ));
      return;
    }

    _fileDownloadService.showLoadingDialog(context, 'Réponse en Exercices...');
    setState(() {
      _isLoading = true;
    });

    try {
      final senderDetails = await auth.getSenderDetails();
      senderDetails.remove("email");
      senderDetails.remove("profile");

      final addExercicesDTO = {
        "MessageEntity": {
          "type": "Exercices",
          "body": _replyController.text.trim(),
          "readStatus": false,
          "confirmed": true,
          "numberOfFile": _selectedFiles.length,
          "repondreA": {"idMessage": widget.exercicesList.first.idMessage},
          ...senderDetails,
        },
        "niveauList": [],
        "classeList": [],
        "eleveList": [],
        "ListofUsers": [
          {"idReceiverUser": widget.exercicesList.first.senderId}
        ],
      };

      await msg.addMessage(context, addExercicesDTO, _selectedFiles);

      final userName = await auth.getUsername();
      setState(() {
        widget.exercicesList.insert(
            0,
            MessageresponseDTO(
              idMessage: 0,
              senderUsername: userName,
              body: _replyController.text.trim(),
              readStatus: false,
              confirmed: true,
              existFile: _selectedFiles.isNotEmpty,
              numberOfFile: _selectedFiles.length,
              dateEnvoie: DateTime.now().add(Duration(hours: -1)),
              archived: false,
            ));
      });

      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Réponse ajoutée avec succès'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ));
      Navigator.pop(context);
    } catch (error) {
      _replyController.clear();
      _selectedFiles = [];
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Échec de l\'ajout de l\'exercice'),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 3),
      ));
    } finally {
      setState(() {
        _isLoading = false;
      });
      // Navigator.of(context).pop();
    }
  }

  IconData getFileIcon(String fileName) {
    String extension = fileName.split('.').last.toLowerCase();
    return fileIcons[extension] ?? Icons.picture_as_pdf;
  }

  Map<String, IconData> fileIcons = {
    'pdf': Icons.picture_as_pdf,
    'doc': Icons.description,
    'docx': Icons.description,
    'xls': Icons.table_chart,
    'xlsx': Icons.table_chart,
    'jpg': Icons.image,
    'jpeg': Icons.image,
    'png': Icons.image,
    'txt': Icons.text_fields,
    'zip': Icons.folder_zip,
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      //drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            leading:Tooltip(
              message: 'Retour',
              child: IconButton(
                onPressed: () => Get.back(),
                icon: Icon(Icons.arrow_back_ios, color: Colors.white), // Icône de retour
              ),
            ),
            // ✅ Assure le centrage du titre
            title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<PNavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => ParentAppNavBar());
              },
              child: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: FutureBuilder<String>(
            future: auth.getUsername(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(child: Text('Error: ${snapshot.error}'));
              } else {
                final userName = snapshot.data ?? '';

                return Column(
                  children: [
                    SecondHeadBar1(title: 'Détails de l\'exercice',icon: Icons.assignment_late,),

                    /* PrimaryHeaderContainer(
                      child: Container(
                        height: 100,
                        child: SecondHeadBar(
                          title: 'Détails de l\'exercice',
                          titleColor: Colors.white,
                          iconColor: Colors.white,
                        ),
                      ),
                    ),*/
                    Expanded(
                      child: ListView.builder(
                        itemCount: widget.exercicesList.length,
                        itemBuilder: (context, index) {
                          final exercices = widget.exercicesList[index];

                          DateTime localDate = exercices.dateEnvoie.toLocal().add(Duration(hours: 1));
                          String formattedDate =
                              DateFormat('dd MMM yyyy HH:mm').format(localDate);
                          String senderFullName =
                              (exercices.senderUsername ?? '') == userName
                                  ? 'Moi'
                                  : (exercices.senderUsername ?? '');

                          String formattedCorrectionDate = exercices
                                      .dateCorrectionExercice !=
                                  null
                              ? '${DateFormat('dd MMM yyyy').format(exercices.dateCorrectionExercice!)}'
                              : '';
                          return Card(
                            margin: EdgeInsets.all(15.0),
                            elevation: 4,
                            color: Colors.white,
                            child: Padding(
                              padding: EdgeInsets.all(15.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Text(formattedDate,
                                          style: TextStyle(fontSize: 15.0)),
                                    ],
                                  ),
                                  Divider(color: Color(0xFFF2F2F2)),
                                  SizedBox(height: 15.0),
                                  RichText(
                                    text: TextSpan(
                                      children: [
                                        TextSpan(
                                          text: 'Expéditeur : ',
                                          style: TextStyle(
                                              fontSize: 18.0,
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF4099FF)),
                                        ),
                                        TextSpan(
                                          text: senderFullName,
                                          style: TextStyle(
                                              fontSize: 18.0,
                                              color: Colors.black),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 15.0),
                                  if (exercices.objet != null &&
                                      exercices.objet != '')
                                    RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: 'Titre : ',
                                            style: TextStyle(
                                                fontSize: 18.0,
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFF4099FF)),
                                          ),
                                          TextSpan(
                                            text: exercices.objet,
                                            style: TextStyle(
                                                fontSize: 18.0,
                                                color: Colors.black),
                                          ),
                                        ],
                                      ),
                                    ),
                                  SizedBox(height: 15.0),
                                  if (exercices.body != null)
                                    RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: 'Description : ',
                                            style: TextStyle(
                                                fontSize: 18.0,
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFF4099FF)),
                                          ),
                                          TextSpan(
                                            text: exercices.body!,
                                            style: TextStyle(
                                                fontSize: 18.0,
                                                color: Colors.black),
                                          ),
                                        ],
                                      ),
                                    ),
                                  SizedBox(height: 15.0),
                                  if (exercices.dateCorrectionExercice !=
                                          null &&
                                      exercices.dateCorrectionExercice != '')
                                    RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: 'Date Correction : ',
                                            style: TextStyle(
                                                fontSize: 18.0,
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFF4099FF)),
                                          ),
                                          TextSpan(
                                            text: formattedCorrectionDate,
                                            style: TextStyle(
                                                fontSize: 18.0,
                                                color: Colors.black),
                                          ),
                                        ],
                                      ),
                                    ),
                                  SizedBox(height: 15.0),
                                  if (exercices.existFile)
                                    Row(
                                      children: [
                                        Text(
                                          'Fichiers : ',
                                          style: TextStyle(
                                              fontSize: 18.0,
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF4099FF)),
                                        ),
                                        Tooltip(
                                          message: 'Télécharger',
                                          child: IconButton(
                                            icon: Icon(
                                                Icons.file_download_outlined,
                                                color: Colors.lightGreen,
                                                size: 50),
                                            onPressed: () async {
                                              await _fileDownloadService
                                                  .downloadFiles(context,
                                                      exercices.idMessage!);
                                            },
                                          ),
                                        ),
                                        Text(
                                          '${exercices.numberOfFile} fichiers',
                                          style: TextStyle(
                                              fontSize: 18.0,
                                              color: Colors.black
                                                  .withOpacity(0.5)),
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    if (widget.exercicesList.length < 2)
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10.0, vertical: 10.0),
                        child: Column(
                          children: [
                            Form(
                              key: _formKey,
                              child: Row(
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      controller: _replyController,
                                      decoration: InputDecoration(
                                        labelText: 'Répondre à ce message...',
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(30.0),
                                        ),
                                        floatingLabelBehavior:
                                            FloatingLabelBehavior.never,
                                        contentPadding: EdgeInsets.symmetric(
                                            vertical: 10.0, horizontal: 15.0),
                                      ),
                                      maxLines: 1,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Veuillez remplir ce champ';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(Icons.attach_file,
                                        color: Colors.blue),
                                    onPressed: _pickFiles, // Open file picker
                                  ),
                                  IconButton(
                                    icon: Icon(Icons.send, color: Colors.blue),
                                    onPressed: _createExercices,
                                  ),
                                ],
                              ),
                            ),
                            if (_selectedFiles.isNotEmpty)
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: _selectedFiles.map((file) {
                                  String fileName =
                                      file['name'] as String; // Cast to String
                                  return Card(
                                    margin: EdgeInsets.symmetric(vertical: 8.0),
                                    child: ListTile(
                                      leading: Icon(
                                        getFileIcon(fileName),
                                        color: Colors.blue,
                                      ),
                                      title: Text(fileName),
                                      trailing: IconButton(
                                        icon: Icon(Icons.delete,
                                            color: Colors.red),
                                        onPressed: () {
                                          setState(() {
                                            // Remove the file from the list
                                            _selectedFiles.removeWhere(
                                                (f) => f['name'] == fileName);
                                          });
                                        },
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                          ],
                        ),
                      ),
                  ],
                );
              }
            }),
      ),
    );
  }
}
