// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../../features/teacher_app/Home/home_Tscreen.dart';
// import '../../features/teacher_app/Profil/profil_Tscreen.dart';
// import '../../features/teacher_app/chat/chat_Tscreen.dart';
// import '../../utils/constants/colors.dart';
//
// class TAppNavBar extends StatelessWidget {
//   const TAppNavBar({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.put(TNavigationController ());
//     return Scaffold(
//       bottomNavigationBar: Obx(() => BottomNavigationBar(
//         currentIndex: controller.selectedIndex.value,
//         selectedItemColor: Color(0xFF4099FF),
//         backgroundColor: AppColors.light,
//         elevation: 30.0,
//         onTap: (index) => controller.changePage(index),
//         items: [
//           BottomNavigationBarItem(
//             icon: Icon(Icons.home),
//             label: 'Accueil',
//           ),
//           BottomNavigationBarItem(
//               icon: Icon(Icons.chat),
//               label: 'Chat'
//           ),
//           BottomNavigationBarItem(
//               icon: Icon(Icons.person),
//               label: 'Profil'
//           ),
//         ],
//       )),
//       body: Obx(() => controller.screens[controller.selectedIndex.value]),
//     );
//   }
// }
//
// class TNavigationController extends GetxController {
//   final Rx<int> selectedIndex = 0.obs;
//   final screens = [
//     THomeScreen(),
//     TChatScreen(),
//     TProfilScreen(),
//   ];
//
//   void changePage(int index) {
//     selectedIndex.value = index;
//   }
// }

import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:NovaSchool/features/parent_app/chat/chat_Pscreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:NovaSchool/features/teacher_app/chat/chat_Tscreen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
class TNavigationController extends GetxController {
  final Rx<int> selectedIndex = 0.obs;
  Map<String, String>? userInfo;

  TNavigationController({required this.userInfo});

  void changeTab(int index) => selectedIndex.value = index;
  void resetToHome() => selectedIndex.value = 0;

  List<Widget> get screens => [
    THomeScreen(),
    TProfilScreen(
      firstName: userInfo?['senderUsername'] ?? '',
      lastName: userInfo?['senderUserSurname'] ?? '',
      email: userInfo?['email'] ?? '',
      profile: userInfo?['profile'] ?? '',
    ),
    TChatScreen(),
  ];
}
class TeacherAppNavBar extends StatefulWidget {
  final int initialIndex;

  TeacherAppNavBar({this.initialIndex = 0}); // par défaut à 0

  @override
  _TeacherAppNavBarState createState() => _TeacherAppNavBarState();
}

class _TeacherAppNavBarState extends State<TeacherAppNavBar> {

  TNavigationController? controller;
  Map<String, String>? userInfo;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    try {
      userInfo = await AuthService().getSenderDetails();
      controller = Get.put(TNavigationController(userInfo: userInfo!), permanent: true);
      //
      controller!.changeTab(widget.initialIndex);
    } catch (e) {
      print("Erreur de chargement du profil: $e");
    }

    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (controller == null) {
      return Scaffold(body: Center(child: Text("Erreur de chargement du profil")));
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ModalRoute.of(context)?.isCurrent == true) {
        controller!.resetToHome();
      }
    });


    return WillPopScope(
      onWillPop: () async {
        if (controller!.selectedIndex.value != 0) {
          controller!.resetToHome();
          return false;
        }
        return true;
      },
      child: Scaffold(
        bottomNavigationBar: CurvedNavigationBar(
          backgroundColor: Color(0xFFF2F2F2),
          color: Colors.lightBlueAccent.shade400,
          buttonBackgroundColor: Colors.blueAccent.withOpacity(0.3),
          height: 65,
          animationDuration: Duration(milliseconds: 300),
          items: [
            Icon(Icons.home, size: 30, color: Colors.white),
            Icon(Icons.person, size: 30, color: Colors.white),
            Icon(Icons.chat, size: 30, color: Colors.white),
          ],
          onTap: (index) {
            controller!.changeTab(index);
          },
        ),
        body: Obx(() {
          final currentIndex = controller!.selectedIndex.value;
          return controller!.screens[currentIndex];
        }),
      ),
    );
  }
}

/****
class TNavigationController extends GetxController {
  final Rx<int> selectedIndex = 0.obs;
  //final RxInt selectedIndex = 0.obs;
  void changeTab(int index) => selectedIndex.value = index;

  void resetToHome() {
    selectedIndex.value = 0;
  }

  /*Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }*/
  /*Map<String, String> userInfo = await _getUserInfo();

  List<Widget> get screens => [

  THomeScreen(),

    TProfilScreen(
  firstName: userInfo['senderUsername'] ?? '',
      lastName: userInfo['senderUserSurname'] ?? '',
      email: userInfo['email'] ?? '',
      profile: userInfo['profile'] ?? '',
    ),
    PChatScreen(), // si ce screen existe pour les enseignants
  ];*/
}

class TeacherAppNavBar extends StatefulWidget {
  @override
  _TeacherAppNavBarState createState() => _TeacherAppNavBarState();
}

class _TeacherAppNavBarState extends State<TeacherAppNavBar> {
  final TNavigationController controller = Get.put(TNavigationController(), permanent: true);
  Map<String, String>? userInfo;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    try {
      userInfo = await AuthService().getSenderDetails();
    } catch (e) {
      print("Erreur de chargement du profil: $e");
    }
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (userInfo == null) {
      return Scaffold(body: Center(child: Text("Erreur de chargement du profil")));
    }

    final List<Widget> screens = [
      THomeScreen(),
      TProfilScreen(
        firstName: userInfo!['senderUsername'] ?? '',
        lastName: userInfo!['senderUserSurname'] ?? '',
        email: userInfo!['email'] ?? '',
        profile: userInfo!['profile'] ?? '',
      ),
      TChatScreen(),
    ];

    return Obx(() {
      final currentIndex = controller.selectedIndex.value;

      return WillPopScope(
        onWillPop: () async {
          if (currentIndex != 0) {
            controller.resetToHome();
            return false;
          }
          return true;
        },
        child: Scaffold(
          body: IndexedStack(
            index: currentIndex,
            children: screens,
          ),
          bottomNavigationBar: CurvedNavigationBar(
            backgroundColor: Color(0xFFF2F2F2),
            color: Colors.lightBlueAccent.shade400,
            buttonBackgroundColor: Colors.blueAccent.withOpacity(0.3),
            height: 65,
            animationDuration: Duration(milliseconds: 300),
            items: [
              Icon(Icons.home, size: 30, color: Colors.white),
              Icon(Icons.person, size: 30, color: Colors.white),
              Icon(Icons.chat, size: 30, color: Colors.white),
            ],
            onTap: controller.changeTab,
          ),
        ),
      );
    });
  }
}
*/
/*
class TeacherAppNavBar extends StatefulWidget {
  @override
  _TeacherAppNavBarState createState() => _TeacherAppNavBarState();
}

class _TeacherAppNavBarState extends State<TeacherAppNavBar> {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final TNavigationController controller = Get.put(TNavigationController());

  //final TNavigationController controller = Get.put(TNavigationController());
  //final TNavigationController controller = Get.find<TNavigationController>();

  late Future<Map<String, String>> _userInfoFuture;
  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _userInfoFuture = AuthService().getSenderDetails();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, String>>(
      future: _userInfoFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(body: Center(child: CircularProgressIndicator()));
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return Scaffold(body: Center(child: Text("Erreur de chargement du profil")));
        }

        final userInfo = snapshot.data!;

        _screens = [
          THomeScreen(),
          TProfilScreen(
            firstName: userInfo['senderUsername'] ?? '',
            lastName: userInfo['senderUserSurname'] ?? '',
            email: userInfo['email'] ?? '',
            profile: userInfo['profile'] ?? '',
          ),
          TChatScreen(),
        ];

        return Obx(() {
          final currentIndex = controller.selectedIndex.value;

          /*return WillPopScope(
            onWillPop: () async {
              if (currentIndex != 0) {
                controller.selectedIndex.value = 0;
                return false;
              }
              return true;
            },*/
          return WillPopScope(
            onWillPop: () async {
              if (currentIndex != 0) {
                controller.resetToHome();
                return false;
              }
              return true;
            },
            child: Scaffold(
              body: _screens[currentIndex],
              bottomNavigationBar: CurvedNavigationBar(
                backgroundColor: Color(0xFFF2F2F2),
                color: Colors.lightBlueAccent.shade400,
                buttonBackgroundColor: Colors.blueAccent.withOpacity(0.3),
                height: 65,
                animationDuration: Duration(milliseconds: 300),
                items: [
                  Icon(Icons.home, size: 30, color: Colors.white),
                  Icon(Icons.person, size: 30, color: Colors.white),
                  Icon(Icons.chat, size: 30, color: Colors.white),
                ],
                onTap: (index) {
                  controller.selectedIndex.value = index;
                },
              ),
            ),
          );
        });
      },
    );
  }
}*/
/*
class TeacherAppNavBar extends StatefulWidget {
  @override
  _TeacherAppNavBarState createState() => _TeacherAppNavBarState();
}

class _TeacherAppNavBarState extends State<TeacherAppNavBar> with SingleTickerProviderStateMixin {
  final TNavigationController controller = Get.put(TNavigationController());

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedIndex.value != 0) {
          controller.resetToHome();
          return false;
        } else {
          return true;
        }
      },
      child: Scaffold(
        bottomNavigationBar: CurvedNavigationBar(
          backgroundColor: Color(0xFFF2F2F2),
          color: Colors.lightBlueAccent.shade400,
          buttonBackgroundColor: Colors.blueAccent.withOpacity(0.3),
          height: 65,
          animationDuration: Duration(milliseconds: 300),
          items: [
            Icon(Icons.home, size: 30, color: Colors.white),
            Icon(Icons.person, size: 30, color: Colors.white),
            Icon(Icons.chat, size: 30, color: Colors.white),
          ],
          onTap: (index) {
            controller.selectedIndex.value = index;
          },
        ),
        body: Obx(() {
          final currentIndex = controller.selectedIndex.value;
          if (currentIndex < controller.screens.length) {
            return controller.screens[currentIndex];
          }
          return Container();
        }),
      ),
    );
  }
}
*/
/*class _ParentAppNavBarState extends State<ParentAppNavBar> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final PNavigationController controller = Get.put(PNavigationController());

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this, // Using TickerProvider
      duration: Duration(milliseconds: 200),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedIndex.value != 0) {
          controller.selectedIndex.value = 0; // Retour à PHomeScreen
          return false;
        } else {
          return true;
        }
      },
      ////code1
      child: Scaffold(
        bottomNavigationBar: CurvedNavigationBar(

          backgroundColor: Color(0xFFF2F2F2), // Fond transparent pour effet flottant
          color:Colors.lightBlueAccent.shade400, // Couleur principale
          buttonBackgroundColor: AppColors.primary.withOpacity(0.3),//AppColors.accent, // Couleur du bouton sélectionné
          height: 65,
          animationDuration: Duration(milliseconds: 300),
          items: [
            Icon(Icons.home, size: 30, color: Colors.white),
            Icon(Icons.person, size: 30, color: Colors.white),
            //Icon(Icons.contact_page, size: 30, color: Colors.white),
            Icon(Icons.chat, size: 30, color: Colors.white),
          ],
          onTap: (index) {
            controller.selectedIndex.value = index;
          },
        ),
        body: Obx(() {
          final currentIndex = controller.selectedIndex.value;
          if (currentIndex < controller.screens.length) {
            return controller.screens[currentIndex];
          }
          return Container();
        }),
      ),);
  }
}
*/