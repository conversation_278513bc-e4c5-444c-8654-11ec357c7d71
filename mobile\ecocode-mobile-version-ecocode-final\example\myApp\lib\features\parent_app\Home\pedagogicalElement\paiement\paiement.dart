import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/models/historiquePaiementEleve.dart';
import 'package:NovaSchool/models/ligneArticleDTO.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/ligneArticleService.dart';
import 'package:NovaSchool/services/paiementService.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:get/get.dart';
// class PPaiementScreen extends StatelessWidget {
//   const PPaiementScreen({Key? key}) : super(key: key);

//   Future<void> _launchPaiementURL(BuildContext context) async {
//     const String paiement = '$dashURL';
//     if (await canLaunch(paiement)) {
//       await launch(paiement);
//     } else {
//       _showErrorDialog(context, 'Impossible d\'ouvrir la page de paiement.');
//     }
//   }

//   void _showErrorDialog(BuildContext context, String message) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: const Text('Erreur'),
//           content: Text(message),
//           actions: <Widget>[
//             TextButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//               child: const Text('OK'),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     double screenWidth = MediaQuery.of(context).size.width;
//     return Scaffold(
//         body: AppBackground(
//       child: SingleChildScrollView(
//         child: Column(
//           children: [
//             PrimaryHeaderContainer(
//               child: Container(
//                 height: 100,
//                 child: SecondHeadBar(
//                   title: 'Paiement',
//                   titleColor: Colors.white,
//                   iconColor: Colors.white,
//                 ),
//               ),
//             ),
//             const SizedBox(height: 20),
//             Container(
//               margin: EdgeInsets.symmetric(
//                 horizontal: screenWidth * 0.05,
//                 vertical: 10,
//               ),
//               child: ElevatedButton(
//                 style: ElevatedButton.styleFrom(
//                   elevation: 5,
//                   padding: EdgeInsets.all(20),
//                   backgroundColor: Colors.blue,
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(15),
//                   ),
//                 ),
//                 onPressed: () => _launchPaiementURL(context),
//                 child: Row(
//                   children: [
//                     Icon(Icons.web, color: Colors.white, size: 30),
//                     const SizedBox(width: 10),
//                     Expanded(
//                       child: Text(
//                         'Cliquez ici pour accéder à vos paiements',
//                         style: TextStyle(fontSize: 16, color: Colors.white),
//                         softWrap: true,
//                       ),
//                     ),
//                     const SizedBox(width: 10),
//                     Container(
//                       decoration: BoxDecoration(
//                         shape: BoxShape.circle,
//                         color: Colors.white,
//                       ),
//                       padding: const EdgeInsets.all(8.0),
//                       child: Icon(Icons.arrow_forward,
//                           color: Colors.blue, size: 20),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//             Padding(
//               padding: EdgeInsets.symmetric(
//                 horizontal: screenWidth * 0.05,
//                 vertical: 10,
//               ),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: const [
//                   Text(
//                     'Page en cours de développement',
//                     style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//                   ),
//                   SizedBox(height: 10),
//                   Text(
//                     'Cette page est actuellement en cours de développement. '
//                     'Les paiements sera disponible ici très bientôt. Merci pour votre patience.',
//                     style: TextStyle(fontSize: 16),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     ));
//   }
// }

class PPaiementScreen extends StatefulWidget {
  const PPaiementScreen({Key? key}) : super(key: key);

  @override
  _PPaiementScreenState createState() => _PPaiementScreenState();
}

class _PPaiementScreenState extends State<PPaiementScreen> {
   List<HistoriquePaiementEleveDTO> paiementList = [];
  bool _isLoading = true;
  int _itemsPerPage = 0;
  int _currentPageIndex = 0;
  final int _pageSize = 5;
  int _totalPages = 0;
  late ScrollController _scrollController;
  PaiementService paiementService = PaiementService();
  AuthService auth = AuthService();

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _fetchPaiements();
  }

  Future<void> _fetchPaiements({int page = 0}) async {
    setState(() {
      _isLoading = true;
    });

    final eleveId = await auth.getEleveId();
    Map<String, dynamic>? result =
        await paiementService.getPaiementByIdElevePage(
      eleveId: eleveId,
      page: page,
      size: _pageSize,
      // sortBy: "datePaiementEleve",
      // sortDirection: "desc",
    );

    if (mounted && result != null) {
      setState(() {
        if (page == 0) {
          paiementList = result['paiements'] ?? [];
        } else {
          paiementList.addAll(result['paiements'] ?? []);
        }
        _totalPages = result['totalPages'] ?? 0;
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true;
      });
      _fetchPaiements(page: newIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    List<HistoriquePaiementEleveDTO> currentPagePaiements = paiementList.isEmpty
        ? []
        : paiementList
            .skip(_currentPageIndex * _pageSize)
            .take(_pageSize)
            .toList();

    return Scaffold(
      body: AppBackground(
        child: Column(
          children: [
            // Container(
            //   child: Column(
            //     children: [
            //       PrimaryHeaderContainer(
            //         child: Container(
            //           height: 100, // Adjust the height here
            //           child: SecondHeadBar(
            //             title: 'Liste des paiements',
            //             titleColor: Colors.white,
            //             iconColor: Colors.white,
            //           ),
            //         ),
            //       ),
            //       Padding(
            //         padding: EdgeInsets.all(15.0),
            //         child: Center(
            //           child: Container(
            //             padding: EdgeInsets.all(10.0),
            //             decoration: BoxDecoration(
            //               borderRadius: BorderRadius.circular(15.0),
            //             ),
            //             child: ClipRRect(
            //               borderRadius: BorderRadius.circular(15.0),
            //               child: Image.asset(
            //                 AppImages.paiement,
            //                 width: 200,
            //                 height: 100,
            //                 fit: BoxFit.contain,
            //               ),
            //             ),
            //           ),
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                      height: 510,
                      child: paiementList.isEmpty
                          ? Center(
                              child: Text("Aucun Paiement trouvé.",
                                  style: TextStyle(
                                      fontSize: 18, color: Colors.grey)))
                          : RefreshIndicator(
                              onRefresh: () => _fetchPaiements(),
                              child: ListView.builder(
                                shrinkWrap: true,
                                padding: EdgeInsets.zero,
                                controller: _scrollController,
                                itemCount: currentPagePaiements.length +
                                    1, // Add 1 for the pagination button
                                itemBuilder: (context, index) {
                                  if (index < currentPagePaiements.length) {
                                    HistoriquePaiementEleveDTO paiementEleveDTO =
                                        currentPagePaiements[index];

                                    return GestureDetector(
                                      onTap: () async {},
                                      child: Column(
                                        children: [
                                          SizedBox(height: 8.0),
                                          Container(
                                            padding: EdgeInsets.all(14.0),
                                            margin: EdgeInsets.only(
                                                bottom: 10.0,
                                                left: 10.0,
                                                right: 10.0),
                                            decoration: BoxDecoration(
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.5),
                                                  spreadRadius: 0,
                                                  blurRadius: 5,
                                                  offset: Offset(0, 1),
                                                ),
                                              ],
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Column(
                                                      children: [
                                                        Text(
                                                          "${paiementEleveDTO.montantPaiementEleve?.toStringAsFixed(2)} TND",
                                                          style: TextStyle(
                                                              fontSize: 16.0,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 3.0),
                                                Row(
                                                  children: [
                                                    Text(
                                                      "Date: ${paiementEleveDTO.datePaiementEleve?.toLocal().add(Duration(hours: 1)).toString().split(' ')[0]}",
                                                      style: TextStyle(
                                                          color: Colors.grey),
                                                    ),

                                                  ],
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    return Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          IconButton(
                                            icon: Icon(Icons.arrow_back_ios,
                                                color: _currentPageIndex > 0
                                                    ? Color(0xFF4099FF)
                                                    : Colors.grey,
                                                size: 20),
                                            onPressed: _currentPageIndex > 0
                                                ? () {
                                                    _updatePageIndex(
                                                        _currentPageIndex - 1);
                                                  }
                                                : null,
                                          ),
                                          Text(
                                            '${_currentPageIndex + 1} / $_totalPages',
                                            style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: 15),
                                          ),
                                          IconButton(
                                            icon: Icon(Icons.arrow_forward_ios,
                                                color: _currentPageIndex <
                                                        _totalPages - 1
                                                    ? Color(0xFF4099FF)
                                                    : Colors.grey,
                                                size: 20),
                                            onPressed: _currentPageIndex <
                                                    _totalPages - 1
                                                ? () {
                                                    _updatePageIndex(
                                                        _currentPageIndex + 1);
                                                  }
                                                : null,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                },
                              ),
                            ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
