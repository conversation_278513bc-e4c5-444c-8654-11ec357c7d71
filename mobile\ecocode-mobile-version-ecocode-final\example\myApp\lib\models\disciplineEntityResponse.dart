class DisciplineEntityResponse {
  final int id;
  final String typeDiscipline;
  final String motifDiscipline;
  final DateTime dateDiscipline;
  final int eleveId;
  final String? nomEleve;
  final String? prenomEleve;
  final String? nomClasse;
  final String? niveauClasse;

  DisciplineEntityResponse({
    required this.id,
    required this.typeDiscipline,
    required this.motifDiscipline,
    required this.dateDiscipline,
    required this.eleveId,
    this.nomEleve,
    this.prenomEleve,
    this.nomClasse,
    this.niveauClasse,
  });

  factory DisciplineEntityResponse.fromJson(Map<String, dynamic> json) {
    return DisciplineEntityResponse(
      id: json['id'] ?? 0,
      typeDiscipline: json['typeDiscipline'] ?? '',
      motifDiscipline: json['motifDiscipline'] ?? '',
      dateDiscipline: json['dateDiscipline'] != null
          ? DateTime.parse(json['dateDiscipline'])
          : DateTime.now(), // Default to current time if null
      eleveId: json['eleveId'] ?? 0,
      nomEleve: json['nomEleve'] ?? '',
      prenomEleve: json['prenomEleve'] ?? '',
      nomClasse: json['nomClasse'] ?? '',
      niveauClasse: json['niveauClasse'] ?? '',
    );
  }

  /// Method to convert the entity to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'typeDiscipline': typeDiscipline,
      'motifDiscipline': motifDiscipline,
      'dateDiscipline': dateDiscipline.toIso8601String(),
      'eleveId': eleveId,
      'nomEleve': nomEleve ?? '', // Provide empty string if null
      'prenomEleve': prenomEleve ?? '',
      'nomClasse': nomClasse ?? '',
      'niveauClasse': niveauClasse ?? '',
    };
  }
}
