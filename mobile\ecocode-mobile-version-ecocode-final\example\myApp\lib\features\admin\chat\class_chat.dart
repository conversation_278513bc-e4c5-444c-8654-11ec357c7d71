class User {
  final String username;
  final String email;

  User({required this.username, required this.email});

  // Deserialize JSON to User object
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      username: json['username'],
      email: json['email'],
    );
  }

  // Serialize User object to JSON
  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'email': email,
    };
  }
}
