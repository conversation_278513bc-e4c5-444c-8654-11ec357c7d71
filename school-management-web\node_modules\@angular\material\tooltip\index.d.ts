export { h as MAT_TOOLTIP_DEFAULT_OPTIONS, f as MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, c as MAT_TOOLTIP_SCROLL_STRATEGY, d as MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, e as MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, k as Mat<PERSON>ooltip, i as MatTooltipDefaultOptions, M as MatTooltipModule, S as SCROLL_THROTTLE_MS, j as TOOLTIP_PANEL_CLASS, l as TooltipComponent, T as TooltipPosition, a as TooltipTouchGestures, b as TooltipVisibility, g as getMatTooltipInvalidPositionError } from '../module.d-C9bwr5Wr.js';
import '@angular/core';
import '@angular/cdk/a11y';
import '@angular/cdk/overlay';
import '../common-module.d-C8xzHJDr.js';
import '@angular/cdk/bidi';
import '@angular/cdk/coercion';
import 'rxjs';
import '@angular/cdk/scrolling';

/**
 * Animations used by <PERSON><PERSON><PERSON>tip.
 * @docs-private
 * @deprecated No longer being used, to be removed.
 * @breaking-change 21.0.0
 */
declare const matTooltipAnimations: {
    readonly tooltipState: any;
};

export { matTooltipAnimations };
