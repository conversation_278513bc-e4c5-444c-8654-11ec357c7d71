
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/features/admin/A_appDrawer.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/utils/constants/colors.dart';
import 'package:NovaSchool/utils/helpers/helper_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class AChatScreen extends StatefulWidget {
  const AChatScreen({Key? key}) : super(key: key);

  @override
  _AChatScreenState createState() => _AChatScreenState();
}


class _AChatScreenState extends State<AChatScreen> {
  TextEditingController _messageController = TextEditingController();
  List<String> messages = ['Salut'];
  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      messages.insert(0, _messageController.text.trim());
      _messageController.clear();
    }
  }
  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AdminDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: Image.asset(
              'assets/logos/ecocode.png', // Remplace avec ton chemin
              height: 150, // Ajuste la taille
              color: Colors
                  .white, // Applique du blanc au logo (si PNG avec transparence)
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: Column(
          children: [/*
            SizedBox(
              height: 90, // Réduction de la hauteur du header
              child: PHeadHomeScreen(),
            ),*/
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      height: AppHelperFunction.screenHeight(context) * 0.8, // Ajustement de la hauteur
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          double innerHeight = constraints.maxHeight;
                          double innerWidth = constraints.maxWidth;
                          return Stack(
                            fit: StackFit.expand,
                            children: [
                              Positioned(
                                bottom: 10,
                                left: 15,
                                right: 15,
                                child: Container(
                                  height: innerHeight * 0.85, // Ajustement de la hauteur du container
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20), // Coins plus arrondis
                                    color: AppColors.light,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.3), // Ombre plus légère
                                        spreadRadius: 0,
                                        blurRadius: 5,
                                        offset: Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      // Liste de messages
                                      Expanded(
                                        child: ListView.builder(
                                          itemCount: messages.length,
                                          reverse: true,
                                          itemBuilder: (context, index) {
                                            final reversedIndex = messages.length - 1 - index;
                                            return Padding(
                                              padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15.0),
                                              child: Align(
                                                alignment: reversedIndex % 2 == 0 ? Alignment.centerLeft : Alignment.centerRight,
                                                child: Container(
                                                  padding: EdgeInsets.all(12),
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.only(
                                                      topLeft: Radius.circular(30),
                                                      topRight: Radius.circular(30),
                                                      bottomLeft: Radius.circular(reversedIndex % 2 == 0 ? 5 : 30),
                                                      bottomRight: Radius.circular(reversedIndex % 2 == 0 ? 25 : 5),
                                                    ),
                                                    color: reversedIndex % 2 == 0 ? AppColors.dark.withOpacity(0.1) : Color(0xFF4099FF),
                                                  ),
                                                  child: Text(
                                                    messages[reversedIndex],
                                                    style: TextStyle(
                                                      color: reversedIndex % 2 == 0 ? Colors.black : Colors.white,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                      // Champ de saisie de texte
                                      Padding(
                                        padding: const EdgeInsets.all(10.0),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: TextField(
                                                controller: _messageController,
                                                decoration: InputDecoration(
                                                  hintText: 'Entrez votre message...',
                                                  filled: true,
                                                  fillColor: AppColors.light,
                                                  border: OutlineInputBorder(
                                                    borderRadius: BorderRadius.circular(10.0),
                                                    borderSide: BorderSide.none,
                                                  ),
                                                  contentPadding: EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
                                                ),
                                              ),
                                            ),
                                            IconButton(
                                              icon: Icon(
                                                Iconsax.send_2,
                                                color: Color(0xFF4099FF),
                                              ),
                                              onPressed: () {
                                                if (_messageController.text.isNotEmpty) {
                                                  setState(() {
                                                    messages.add(_messageController.text);
                                                    _messageController.clear();
                                                  });
                                                }
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 15,
                                left: 0,
                                right: 0,
                                child: Center(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(50),
                                      color: Color(0xFF4099FF),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.3),
                                          spreadRadius: 0,
                                          blurRadius: 5,
                                          offset: Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                    width: innerWidth * 0.6,
                                    height: 45,
                                    child: Align(
                                      alignment: Alignment.center,
                                      child: Text(
                                        'Support & Assistance', // Nouveau titre
                                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                          fontSize: 15,
                                          color: AppColors.light,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
/*
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AppBackground(child: Column(
        children: [
        SizedBox(
        height: 100, // Fixe la hauteur de ton header
        child: PHeadHomeScreen(),
      ),
        Expanded(
        child: SingleChildScrollView(
          child: Column(
            children: [
              PHeadHomeScreen(),
              Container(
                height: AppHelperFunction.screenHeight(context) * 0.82,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    double innerHeight = constraints.maxHeight;
                    double innerWidth = constraints.maxWidth;
                    return Stack(
                      fit: StackFit.expand,
                      children: [
                        Positioned(
                          bottom: 10,
                          left: 15,
                          right: 15,
                          child: Container(
                            height: innerHeight * 0.9,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              color: AppColors.light,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.5),
                                  spreadRadius: 0,
                                  blurRadius: 5,
                                  offset: Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                // Liste de messages
                                Expanded(
                                  child: ListView.builder(
                                    itemCount: messages.length,
                                    reverse: true,
                                    itemBuilder: (context, index) {
                                      final reversedIndex = messages.length - 1 - index;
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 15.0),
                                        child: Align(
                                          alignment: reversedIndex % 2 == 0 ? Alignment.centerLeft : Alignment.centerRight,
                                          child: Container(
                                            padding: EdgeInsets.all(12),
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(30),
                                                topRight: Radius.circular(30),
                                                bottomLeft: Radius.circular(reversedIndex % 2 == 0 ? 5 : 30),
                                                bottomRight: Radius.circular(reversedIndex % 2 == 0 ? 25 : 5),
                                              ),
                                              color: reversedIndex % 2 == 0 ? AppColors.dark.withOpacity(0.1) : Color(0xFF4099FF),
                                            ),
                                            child: Text(
                                              messages[reversedIndex],
                                              style: TextStyle(
                                                color: reversedIndex % 2 == 0 ? Colors.black : Colors.white,
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),

                                // Champ de saisie de texte
                                Padding(
                                  padding: const EdgeInsets.all(15.0),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: TextField(
                                          controller: _messageController,
                                          decoration: InputDecoration(
                                            hintText: 'Entrez votre message...',
                                            filled: true,
                                            fillColor: AppColors.light,
                                            border: OutlineInputBorder(
                                              borderRadius: BorderRadius.circular(10.0),
                                              borderSide: BorderSide.none,
                                            ),
                                            contentPadding: EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
                                          ),
                                        ),
                                      ),
                                      IconButton(
                                        icon: Icon(
                                          Iconsax.send_2,
                                          color: Color(0xFF4099FF),
                                        ),
                                        onPressed: () {
                                          if (_messageController.text.isNotEmpty) {
                                            setState(() {
                                              messages.add(_messageController.text);
                                              _messageController.clear();
                                            });
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          top: 25,
                          left: 0,
                          right: 0,
                          child: Center(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(50),
                                color: Color(0xFF4099FF),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.5),
                                    spreadRadius: 0,
                                    blurRadius: 5,
                                    offset: Offset(0, 1),
                                  ),
                                ],
                              ),
                              width: innerWidth * 0.6,
                              height: 50,
                              child: Align(
                                alignment: Alignment.center,
                                child: Text(
                                  'Messagerie administration',
                                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                    fontSize: 15,
                                    color: AppColors.light,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    ],),
    ),
    );
  }
}
*/