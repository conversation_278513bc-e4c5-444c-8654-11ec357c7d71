import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/models/ligneArticleDTO.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/ligneArticleService.dart';
import 'package:flutter/material.dart';

class PServicesScreen extends StatefulWidget {
  const PServicesScreen({Key? key}) : super(key: key);

  @override
  _PServicesScreenState createState() => _PServicesScreenState();
}

class _PServicesScreenState extends State<PServicesScreen> {
   List<GetLigneArticleDTO> ligneArticle = [];
  bool _isLoading = true;
  int _itemsPerPage = 0;
  int _currentPageIndex = 0;
  final int _pageSize = 5;
  int _totalPages = 0;
  late ScrollController _scrollController;
  LigneArticleService ligneArticleService = LigneArticleService();
  AuthService auth = AuthService();

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _fetchLignArticle();
  }

  Future<void> _fetchLignArticle({int page = 0}) async {
    setState(() {
      _isLoading = true;
    });

    final eleveId = await auth.getEleveId();
    Map<String, dynamic>? result =
        await ligneArticleService.getLigneArticlesByIdElevePage(
      eleveId: eleveId,
      page: page,
      size: _pageSize,
      // sortBy: "dateAchat",
      // sortDirection: "desc",
    );

    if (mounted && result != null) {
      setState(() {
        // Append new articles to the existing list
        if (page == 0) {
          // If it's the first page, replace the articles
          ligneArticle = result['articles'] ?? [];
        } else {
          // Otherwise, append the new articles
          ligneArticle.addAll(result['articles'] ?? []);
        }
        _totalPages = result['totalPages'] ?? 0;
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true;
      });
      _fetchLignArticle(page: newIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    List<GetLigneArticleDTO> currentPageArticles = ligneArticle.isEmpty
        ? []
        : ligneArticle
            .skip(_currentPageIndex * _pageSize)
            .take(_pageSize)
            .toList();

    return Scaffold(
      body: AppBackground(
        child: Column(
          children: [
            // Container(
            //   child: Column(
            //     children: [
            //       PrimaryHeaderContainer(
            //         child: Container(
            //           height: 100, // Adjust the height here
            //           child: SecondHeadBar(
            //             title: 'Liste des Services',
            //             titleColor: Colors.white,
            //             iconColor: Colors.white,
            //           ),
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                      height: 510,
                      child: ligneArticle.isEmpty
                          ? Center(
                              child: Text("Aucun Service trouvé.",
                                  style: TextStyle(
                                      fontSize: 18, color: Colors.grey)))
                          : RefreshIndicator(
                              onRefresh: () => _fetchLignArticle(),
                              child: ListView.builder(
                                shrinkWrap: true,
                                padding: EdgeInsets.zero,
                                controller: _scrollController,
                                itemCount: currentPageArticles.length +
                                    1, // Add 1 for pagination
                                itemBuilder: (context, index) {
                                  if (index < currentPageArticles.length) {
                                    GetLigneArticleDTO ligneArticleDTO =
                                        currentPageArticles[index];

                                    Color dateTextColor = Colors.grey;
                                    IconData? iconData;
                                    if (ligneArticleDTO.etat == true) {
                                      dateTextColor = Colors.green;
                                      iconData = Icons
                                          .check_circle_outline; // "Payee" icon
                                    } else if (ligneArticleDTO
                                                .dateAlertpaiement !=
                                            null &&
                                        ligneArticleDTO.dateAlertpaiement!
                                            .isBefore(DateTime.now())) {
                                      dateTextColor = Colors.red;
                                      iconData = Icons
                                          .error_outline; // "Non-payee" icon
                                    }

                                    return GestureDetector(
                                      onTap: () async {},
                                      child: Column(
                                        children: [
                                          SizedBox(height: 8.0),
                                          Container(
                                            padding: EdgeInsets.all(14.0),
                                            margin: EdgeInsets.only(
                                                bottom: 10.0,
                                                left: 10.0,
                                                right: 10.0),
                                            decoration: BoxDecoration(
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.5),
                                                  spreadRadius: 0,
                                                  blurRadius: 5,
                                                  offset: Offset(0, 1),
                                                ),
                                              ],
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        ligneArticleDTO
                                                            .nomArticle!,
                                                        style: TextStyle(
                                                          fontSize: 18.0,
                                                          color:
                                                              Color(0xFF4099FF),
                                                          fontWeight:
                                                              FontWeight.normal,
                                                        ),
                                                      ),
                                                    ),
                                                    Column(
                                                      children: [
                                                        Row(
                                                          children: [
                                                            Icon(
                                                              Icons.circle,
                                                              color: ligneArticleDTO
                                                                      .etat!
                                                                  ? Colors
                                                                      .green 
                                                                  : Colors
                                                                      .red, 
                                                              size: 10,
                                                            ),
                                                            SizedBox(width: 5),
                                                            Text(
                                                              ligneArticleDTO
                                                                      .etat!
                                                                  ? 'Payeé'
                                                                  : 'Non Payeé',
                                                              style: TextStyle(
                                                                fontSize: 12,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: ligneArticleDTO
                                                                        .etat!
                                                                    ? Colors
                                                                        .green 
                                                                    : Colors
                                                                        .red, 
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        SizedBox(height: 5),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 3.0),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        "Date: ${ligneArticleDTO.dateAlertpaiement?.toLocal().add(Duration(hours: 1)).toString().split(' ')[0]}",
                                                        style: TextStyle(
                                                            color:
                                                                dateTextColor),
                                                      ),
                                                    ),
                                                    Column(
                                                      children: [
                                                        Row(
                                                          children: [
                                                            Text(
                                                              "${ligneArticleDTO.montantTranche?.toStringAsFixed(2)} TND",
                                                              style: TextStyle(
                                                                fontSize: 16.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        SizedBox(height: 5),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    return Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          IconButton(
                                            icon: Icon(Icons.arrow_back_ios,
                                                color: _currentPageIndex > 0
                                                    ? Color(0xFF4099FF)
                                                    : Colors.grey,
                                                size: 20),
                                            onPressed: _currentPageIndex > 0
                                                ? () {
                                                    _updatePageIndex(
                                                        _currentPageIndex - 1);
                                                  }
                                                : null,
                                          ),
                                          Text(
                                            '${_currentPageIndex + 1} / $_totalPages',
                                            style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: 15),
                                          ),
                                          IconButton(
                                            icon: Icon(Icons.arrow_forward_ios,
                                                color: _currentPageIndex <
                                                        _totalPages - 1
                                                    ? Color(0xFF4099FF)
                                                    : Colors.grey,
                                                size: 20),
                                            onPressed: _currentPageIndex <
                                                    _totalPages - 1
                                                ? () {
                                                    _updatePageIndex(
                                                        _currentPageIndex + 1);
                                                  }
                                                : null,
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                },
                              ),
                            ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}