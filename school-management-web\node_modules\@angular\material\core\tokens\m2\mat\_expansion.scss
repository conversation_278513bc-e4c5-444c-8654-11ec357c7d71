@use 'sass:map';
@use '../../token-definition';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, expansion);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    container-shape: 4px,
    legacy-header-indicator-display: inline-block,
    header-indicator-display: none,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    container-background-color: inspection.get-theme-color($theme, background, card),
    container-text-color: inspection.get-theme-color($theme, foreground, text),
    actions-divider-color: inspection.get-theme-color($theme, foreground, divider),
    header-hover-state-layer-color: inspection.get-theme-color($theme, background, hover),
    header-focus-state-layer-color: inspection.get-theme-color($theme, background, hover),
    header-disabled-state-text-color: inspection.get-theme-color(
        $theme, foreground, disabled-button),
    header-text-color: inspection.get-theme-color($theme, foreground, text),
    header-description-color: inspection.get-theme-color($theme, foreground, secondary-text),
    header-indicator-color: inspection.get-theme-color($theme, foreground, secondary-text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    header-text-font: inspection.get-theme-typography($theme, subtitle-2, font-family),
    header-text-size: inspection.get-theme-typography($theme, subtitle-2, font-size),
    header-text-weight: inspection.get-theme-typography($theme, subtitle-2, font-weight),

    // TODO(crisbeto): these two properties weren't set at all before the introduction of tokens,
    // but it's inconsistent not to provide them since the container sets all of them. Eventually
    // we should change the values to use come from `subtitle-2`.
    header-text-line-height: inherit,
    header-text-tracking: inherit,

    container-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    container-text-line-height: inspection.get-theme-typography($theme, body-2, line-height),
    container-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    container-text-tracking: inspection.get-theme-typography($theme, body-2, letter-spacing),
    container-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);
  $collapsed-scale: (
    0: 48px,
    -1: 44px,
    -2: 40px,
    -3: 36px,
  );
  $expanded-scale: (
    0: 64px,
    -1: 60px,
    -2: 56px,
    -3: 48px,
  );

  @return (
    header-collapsed-state-height: map.get($collapsed-scale, $scale),
    header-expanded-state-height: map.get($expanded-scale, $scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-definition.$placeholder-color-config),
      get-typography-tokens(token-definition.$placeholder-typography-config),
      get-density-tokens(token-definition.$placeholder-density-config)
  );
}
