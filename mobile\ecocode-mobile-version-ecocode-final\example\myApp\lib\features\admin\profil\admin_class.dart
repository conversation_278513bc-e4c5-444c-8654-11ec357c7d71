class Admin {
  final String? username;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? telephone;
  final int? matricule;
  final String? profilePicture; // Champ pour la photo de profil

  Admin({
    this.username,
    this.firstname,
    this.lastname,
    this.email,
    this.matricule,
    this.telephone,
    this.profilePicture,
  });

  factory Admin.fromJson(Map<String, dynamic> json) {
    return Admin(
      username: json['username'],
      firstname: json['firstname'],
      lastname: json['lastname'],
      email: json['email'],
      matricule: json['matricule'] != null && json['matricule'] != '' ? int.parse(json['matricule']) : null,
      telephone: json['telephone'],
      profilePicture: json['profilePicture'],
    );
  }

}
