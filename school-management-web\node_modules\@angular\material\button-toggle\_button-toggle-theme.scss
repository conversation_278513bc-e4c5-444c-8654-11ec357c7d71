@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/typography/typography';
@use '../core/tokens/m2/mat/legacy-button-toggle' as tokens-mat-legacy-button-toggle;
@use '../core/tokens/m2/mat/standard-button-toggle' as tokens-mat-standard-button-toggle;
@use '../core/tokens/token-utils';
@use '../core/style/sass-utils';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-button-toggle.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mat-legacy-button-toggle.$prefix,
        tokens-mat-legacy-button-toggle.get-unthemable-tokens()
      );
      @include token-utils.create-token-values(
        tokens-mat-standard-button-toggle.$prefix,
        tokens-mat-standard-button-toggle.get-unthemable-tokens()
      );
    }
  }
}

/// Outputs color theme styles for the mat-button-toggle.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the button toggle: primary, secondary, tertiary,
///      or error (If not specified, default secondary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mat-legacy-button-toggle.$prefix,
        tokens-mat-legacy-button-toggle.get-color-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-standard-button-toggle.$prefix,
        tokens-mat-standard-button-toggle.get-color-tokens($theme)
      );
    }
  }
}

/// Outputs typography theme styles for the mat-button-toggle.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mat-legacy-button-toggle.$prefix,
        tokens-mat-legacy-button-toggle.get-typography-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-standard-button-toggle.$prefix,
        tokens-mat-standard-button-toggle.get-typography-tokens($theme)
      );
    }
  }
}

/// Outputs density theme styles for the mat-button-toggle.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values(
        tokens-mat-legacy-button-toggle.$prefix,
        tokens-mat-legacy-button-toggle.get-density-tokens($theme)
      );
      @include token-utils.create-token-values(
        tokens-mat-standard-button-toggle.$prefix,
        tokens-mat-standard-button-toggle.get-density-tokens($theme)
      );
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  $legacy-tokens: tokens-mat-legacy-button-toggle.get-token-slots();
  $standard-tokens: tokens-mat-standard-button-toggle.get-token-slots();

  @return (
    (
      namespace: tokens-mat-legacy-button-toggle.$prefix,
      tokens: $legacy-tokens,
      prefix: 'legacy-',
    ),
    (
      namespace: tokens-mat-standard-button-toggle.$prefix,
      tokens: $standard-tokens,
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values($tokens, _define-overrides()...);
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-button-toggle.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the button toggle: primary, secondary, tertiary,
///      or error (If not specified, default secondary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-button-toggle') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
    'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector'
  );
  $mat-standard-button-toggle-tokens: token-utils.get-tokens-for(
    $tokens,
    tokens-mat-standard-button-toggle.$prefix,
    $options...
  );
  @include token-utils.create-token-values(
    tokens-mat-standard-button-toggle.$prefix,
    $mat-standard-button-toggle-tokens
  );
}
