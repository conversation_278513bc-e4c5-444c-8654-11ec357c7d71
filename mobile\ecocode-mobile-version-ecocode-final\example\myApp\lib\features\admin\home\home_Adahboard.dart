/***import 'package:NovaSchool/features/admin/home/<USER>/discipline/discipline_Ascreen.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/features/admin/home/<USER>/cours/cours_Ascreen.dart';
import 'package:NovaSchool/features/admin/home/<USER>/exercice/exercice_Ascreen.dart';
import 'package:NovaSchool/features/admin/home/<USER>/observation/observation_Ascreen.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import '../../../utils/constants/colors.dart';
import '../user/utilisateur_screen.dart';

class AHomePedagogicalElement extends StatelessWidget {
  const AHomePedagogicalElement({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => UserManagementPage()),
                    );
                  },
                  title: 'Utilisateurs', iconimage: AppImages.utilisateur,
                ),
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => AObservationScreen()),
                    );
                  },
                  title: 'Observations', iconimage: AppImages.observation,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => AExerciceScreen()),
                    );
                  },
                  title: 'Exercices', iconimage: AppImages.exercice,
                ),
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => ACoursScreen()),
                    );
                  },
                  title: 'Cours', iconimage: AppImages.cours,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => ADisciplineScreen()),
                    );
                  },
                  title: 'Disciplines', iconimage: AppImages.discipline,
                ),
                // PHomeCategories(
                //   onPress: () {
                //     Navigator.push(
                //       context,
                //       MaterialPageRoute(builder: (context) => ACoursScreen()),
                //     );
                //   },
                //   title: 'Cours', iconimage: AppImages.cours,
                // ),
              ],
            ),
          ),
          // Padding(
          //   padding: const EdgeInsets.symmetric(horizontal: 20),
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       PHomeCategories(
          //         onPress: () {
          //           Navigator.push(
          //             context,
          //             MaterialPageRoute(builder: (context) => ChatScreen(),
          //           );
          //         },
          //         title: 'Chat', iconimage: AppImages.AChat,
          //       ),
          //     ],
          //   ),
          // ),

        ],
      ),
    );
  }
}



*/import 'package:NovaSchool/features/admin/home/<USER>/discipline/discipline_Ascreen.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/features/admin/home/<USER>/cours/cours_Ascreen.dart';
import 'package:NovaSchool/features/admin/home/<USER>/exercice/exercice_Ascreen.dart';
import 'package:NovaSchool/features/admin/home/<USER>/observation/observation_Ascreen.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import '../../../utils/constants/colors.dart';
import '../user/utilisateur_screen.dart';
/*class AHomePedagogicalElement extends StatelessWidget {
  const AHomePedagogicalElement({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 50, left: 15, right: 15),
      child: Column(
        children: [
          /// Bouton Refresh
          Align(
            alignment: Alignment.centerRight,
            child: IconButton(
              onPressed: () {
                // Action de refresh
              },
              icon: Icon(Icons.refresh, color: Colors.blueAccent, size: 28),
              tooltip: 'Actualiser',
            ),
          ),
          const SizedBox(height: 10),

          /// Grille de boutons
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 15,
              mainAxisSpacing: 20,
              childAspectRatio: 1.3,
              children: [
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => UserManagementPage()),
                    );
                  },
                  title: 'Utilisateurs',
                  iconData: Icons.supervised_user_circle_rounded,
                ),
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => AObservationScreen()),
                    );
                  },
                  title: 'Observations',
                  iconData: Icons.remove_red_eye,
                ),
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => ACoursScreen()),
                    );
                  },
                  title: 'Cours',
                  iconData: Icons.book,
                ),
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => AExerciceScreen()),
                    );
                  },
                  title: 'Exercices',
                  iconData: Icons.assignment,
                ),
                AHomeCategories(
                  onPress: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => ADisciplineScreen()),
                    );
                  },
                  title: 'Disciplines',
                  iconData: Icons.priority_high_outlined,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
*//****
class AHomePedagogicalElement extends StatelessWidget {
  const AHomePedagogicalElement({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 50, left: 15, right: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AHomeCategories(
            onPress: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => UserManagementPage()),
              );
            },
            title: 'Utilisateurs',
            //iconimage: AppImages.utilisateur,
            iconData: Icons.supervised_user_circle_rounded,
          ),
          const SizedBox(height: 20),
          AHomeCategories(
            onPress: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AObservationScreen()),
              );
            },
            title: 'Observations',
            //iconimage: AppImages.observation,
            iconData: Icons.remove_red_eye,
          ),
          const SizedBox(height: 20),
          AHomeCategories(
            onPress: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => ACoursScreen()),
              );
            },
            title: 'Cours',
            iconData:Icons.book,
            //iconimage: AppImages.cours,
          ),


          const SizedBox(height: 20),
          AHomeCategories(
            onPress: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AExerciceScreen()),
              );
            },
            title: 'Exercices',
            //iconimage: AppImages.exercice,
            iconData: Icons.assignment,
          ),

          const SizedBox(height: 20),

          AHomeCategories(
            onPress: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => ADisciplineScreen()),
              );
            },
            title: 'Disciplines',
            iconData:Icons.warning,
            //iconimage: AppImages.discipline,
          ),
          const SizedBox(height: 20),

        ],
      ),
    );
  }
}**/
class AHomePedagogicalElement extends StatefulWidget {
  const AHomePedagogicalElement({Key? key}) : super(key: key);

  @override
  _AHomePedagogicalElementState createState() => _AHomePedagogicalElementState();
}

class _AHomePedagogicalElementState extends State<AHomePedagogicalElement> {
  bool isLoading = false; // Variable pour indiquer si le chargement est en cours

  // Méthode pour rafraîchir les données
  Future<void> _fetchUnreadCounts() async {
    setState(() {
      isLoading = true; // Démarre le chargement
    });

    // Simule un délai de chargement (remplace ceci par la logique réelle de récupération de données)
    await Future.delayed(Duration(seconds: 2));

    setState(() {
      isLoading = false; // Arrête le chargement
    });
  }
  @override
  Widget build(BuildContext context) {
    Align(
      alignment: Alignment.centerRight,
      child:   IconButton(
        icon: isLoading
            ? CircularProgressIndicator()
            : Icon(
          Icons.refresh,
          color: Color(0xFF4099FF),
        ),
        onPressed: isLoading ? null : _fetchUnreadCounts,
      ),
      // SizedBox(height: 20,),
    );
    final List<_AdminHomeItem> items = [
      _AdminHomeItem(
        title: 'Utilisateurs',
        iconimage: AppImages.utilisateur,
        //icon: Icons.supervised_user_circle_rounded,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => UserManagementPage()),
          );
        },
      ),
      _AdminHomeItem(
        title: 'Observations',
        iconimage: AppImages.observation,
        //icon: Icons.remove_red_eye,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AObservationScreen()),
          );
        },
      ),
      _AdminHomeItem(
        title: 'Cours',
        iconimage: AppImages.cours,
        //icon: Icons.book,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => ACoursScreen()),
          );
        },
      ),
      _AdminHomeItem(
        title: 'Exercices',
        //icon: Icons.assignment,
        iconimage: AppImages.exercice,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AExerciceScreen()),
          );
        },
      ),
      _AdminHomeItem(
        title: 'Disciplines',
        //icon: Icons.priority_high_outlined,
        iconimage: AppImages.discipline,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => ADisciplineScreen()),
          );
        },
      ),
      // Tu peux en ajouter d'autres ici si nécessaire
    ];

    return Padding(
      padding: const EdgeInsets.only(top: 50.0),
      child: GridView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: items.length,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // Deux colonnes
          crossAxisSpacing: 12,
          mainAxisSpacing: 5,
          childAspectRatio: 0.9,
        ),
        itemBuilder: (context, index) {
          final item = items[index];
          return AHomeCategories(
            onPress: item.onTap,
            title: item.title,
            iconimage: item.iconimage,

            //iconData: item.icon,
          );
        },
      ),
    );
  }
}

/// Classe utilitaire pour organiser les boutons
class _AdminHomeItem {
  final String title;
  //final IconData icon;
  final VoidCallback onTap;
  final String? iconimage;



  _AdminHomeItem({
    required this.title,
    //this.icon,
    required this.onTap,
    this.iconimage,
  });
}


class AHomeCategories extends StatelessWidget {
  const AHomeCategories({
    Key? key,
    this.iconimage,
    required this.title,
    this.iconData,
    required this.onPress,
  }) : super(key: key);

  final String? iconimage;
  final String title;
  final IconData? iconData;
  final VoidCallback onPress;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPress,
      child: Container(
        margin: const EdgeInsets.only(bottom: 30),
        width: MediaQuery.of(context).size.width / 2.5,//2.3,
        height: 100,//MediaQuery.of(context).size.height / 6,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.5),
              spreadRadius: 0,
              blurRadius: 5,
              offset: Offset(2, 4),
            ),
          ],
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [

            Column(
              mainAxisAlignment: MainAxisAlignment.center,//spaceAround,
              //crossAxisAlignment: CrossAxisAlignment.center,
              children: [

                iconimage != null
                    ? Image(image: AssetImage(iconimage!), height: 60, width: 60)
                    : Icon(iconData, size: 55, color: Color(0xFF0993D7)),//Colors.lightBlue),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(//dmSerifDisplay(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1B2F38),
                  ),
                ),
              ],
            ),
            // Show badge only if unreadCount > 0

          ],
        ),
      ),
    );
  }
}

/*
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 10),
      child: GestureDetector(
        onTap: onPress,
        child: Container(
          height: 100.0,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                spreadRadius: 0,
                blurRadius: 5,
                offset: Offset(0, 1),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(15.0),
            child: Column(
              children: [
                iconimage != null
                    ? Image(image: AssetImage(iconimage!), height: 45, width: 45)
                    : Icon(iconData, size: 45, color: Color(0xFF0993D7)),//Color(0xFF0ECDF1)),//Colors.lightBlue),
                /*Color(0xFF0679AB) bleu foncé
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.dmSerifDisplay(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1B2F38),
                  ),
                )*/
                /*Image(
                  image: AssetImage(iconimage),
                  width: 50.0,
                  height: 50.0,
                ),*/
                //SizedBox(width: 50.0),
                Expanded(
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      fontWeight: FontWeight.w500,
                      color: Colors.black.withOpacity(0.8),
                    ),
                  ),
                ),
                /*Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.blueGrey,
                  size: 25,
                ),*/
              ],
            ),
          ),
        ),
      ),
    );
  }
}*/

/****
class AHomeCategories extends StatelessWidget {
  const AHomeCategories({
    Key? key,
    required this.onPress,
    required this.title,
    required this.iconimage, // Ajout de l'icône
  }) : super(key: key);

  final VoidCallback onPress;
  final String title;
  final String iconimage; // Définition de l'icône

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPress,
      child: Container(
        margin: const EdgeInsets.only(bottom: 30),
        width: MediaQuery.of(context).size.width / 2.3,
        height: MediaQuery.of(context).size.height / 5,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.5),
              spreadRadius: 0,
              blurRadius: 5,
              offset: Offset(0, 1), // changes position of shadow
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              iconimage, // Utilisation de l'image
              height: 50,
            ),
            const SizedBox(height: 10), // Espacement entre l'icône et le texte
            Text(
              title,
              textAlign: TextAlign.center,
              style: GoogleFonts.dmSerifDisplay(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Color(0xFF4099FF),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
*/