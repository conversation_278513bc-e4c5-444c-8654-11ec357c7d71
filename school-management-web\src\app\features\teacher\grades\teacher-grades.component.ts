import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-teacher-grades',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-grades">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>📊 Grade Management</h1>
            <p>Enter, track, and analyze student grades and performance</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="showGradeModal = true">
              <span class="icon">➕</span>
              Add Grades
            </button>
            <button class="action-btn secondary" (click)="exportGrades()">
              <span class="icon">📊</span>
              Export
            </button>
          </div>
        </div>
      </header>

      <!-- Class Selection -->
      <section class="class-selection">
        <div class="selection-tabs">
          <button 
            class="selection-btn" 
            [class.active]="selectedClass === class.id"
            (click)="selectClass(class.id)"
            *ngFor="let class of classes">
            {{class.name}} ({{class.students.length}} students)
          </button>
        </div>
      </section>

      <!-- Grades Table -->
      <section class="grades-section" *ngIf="getSelectedClass()">
        <div class="grades-container">
          <div class="grades-header">
            <h2>{{getSelectedClass()?.name}} - Grade Book</h2>
            <div class="grade-filters">
              <select [(ngModel)]="selectedAssignment" class="filter-select">
                <option value="">All Assignments</option>
                <option *ngFor="let assignment of assignments" [value]="assignment.id">
                  {{assignment.name}}
                </option>
              </select>
              <select [(ngModel)]="gradeFilter" class="filter-select">
                <option value="">All Grades</option>
                <option value="excellent">Excellent (90-100)</option>
                <option value="good">Good (80-89)</option>
                <option value="average">Average (70-79)</option>
                <option value="poor">Poor (0-69)</option>
              </select>
            </div>
          </div>

          <div class="grades-table-container">
            <table class="grades-table">
              <thead>
                <tr>
                  <th>Student</th>
                  <th *ngFor="let assignment of assignments">
                    {{assignment.name}}
                    <span class="assignment-date">({{assignment.date | date:'shortDate'}})</span>
                  </th>
                  <th>Average</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let student of getFilteredStudents()">
                  <td class="student-cell">
                    <div class="student-info">
                      <div class="student-avatar">{{student.name.charAt(0)}}</div>
                      <div class="student-details">
                        <div class="student-name">{{student.name}}</div>
                        <div class="student-id">ID: {{student.id}}</div>
                      </div>
                    </div>
                  </td>
                  <td *ngFor="let assignment of assignments" class="grade-cell">
                    <div class="grade-input-container">
                      <input 
                        type="number" 
                        min="0" 
                        max="100" 
                        [value]="getGrade(student.id, assignment.id)"
                        (change)="updateGrade(student.id, assignment.id, $event)"
                        class="grade-input"
                        [class]="getGradeClass(getGrade(student.id, assignment.id))">
                      <span class="grade-status" [class]="getGradeClass(getGrade(student.id, assignment.id))">
                        {{getGradeStatus(getGrade(student.id, assignment.id))}}
                      </span>
                    </div>
                  </td>
                  <td class="average-cell">
                    <span class="average-grade" [class]="getGradeClass(student.average)">
                      {{student.average}}%
                    </span>
                  </td>
                  <td class="actions-cell">
                    <button class="action-btn small" (click)="viewStudentDetails(student)">
                      <span class="icon">👁️</span>
                    </button>
                    <button class="action-btn small secondary" (click)="sendFeedback(student)">
                      <span class="icon">💬</span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- Grade Statistics -->
      <section class="stats-section" *ngIf="getSelectedClass()">
        <h2>📈 Class Performance Statistics</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
              <div class="stat-number">{{getClassAverage()}}%</div>
              <div class="stat-label">Class Average</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
              <div class="stat-number">{{getTopPerformers()}}</div>
              <div class="stat-label">Top Performers</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">📈</div>
            <div class="stat-content">
              <div class="stat-number">{{getImprovementRate()}}%</div>
              <div class="stat-label">Improvement Rate</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">⚠️</div>
            <div class="stat-content">
              <div class="stat-number">{{getAtRiskStudents()}}</div>
              <div class="stat-label">At Risk Students</div>
            </div>
          </div>
        </div>
      </section>

      <!-- Add Grades Modal -->
      <div class="modal-overlay" *ngIf="showGradeModal" (click)="showGradeModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>➕ Add New Assignment Grades</h2>
            <button class="close-btn" (click)="showGradeModal = false">✕</button>
          </div>
          <form class="grade-form">
            <div class="form-row">
              <div class="form-group">
                <label>Assignment Name</label>
                <input type="text" [(ngModel)]="newAssignment.name" name="assignmentName" placeholder="Enter assignment name">
              </div>
              <div class="form-group">
                <label>Assignment Date</label>
                <input type="date" [(ngModel)]="newAssignment.date" name="assignmentDate">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Type</label>
                <select [(ngModel)]="newAssignment.type" name="assignmentType">
                  <option value="">Select Type</option>
                  <option value="Quiz">Quiz</option>
                  <option value="Test">Test</option>
                  <option value="Homework">Homework</option>
                  <option value="Project">Project</option>
                </select>
              </div>
              <div class="form-group">
                <label>Total Points</label>
                <input type="number" [(ngModel)]="newAssignment.totalPoints" name="totalPoints" min="1" max="100" placeholder="100">
              </div>
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showGradeModal = false">Cancel</button>
              <button type="button" class="save-btn" (click)="addAssignment()">
                <span class="icon">💾</span>
                Add Assignment
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .teacher-grades {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .action-btn.secondary {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .action-btn.small {
      padding: 6px 12px;
      font-size: 12px;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 6px;
    }

    .action-btn.small.secondary {
      background: #6c757d;
    }

    .class-selection {
      margin-bottom: 30px;
    }

    .selection-tabs {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      padding: 0 4px;
    }

    .selection-btn {
      background: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      color: #64748b;
      transition: all 0.3s ease;
      white-space: nowrap;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .selection-btn.active {
      background: #667eea;
      color: white;
    }

    .grades-section {
      margin-bottom: 40px;
    }

    .grades-container {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .grades-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .grades-header h2 {
      margin: 0;
      color: #2d3748;
      font-size: 20px;
    }

    .grade-filters {
      display: flex;
      gap: 12px;
    }

    .filter-select {
      padding: 8px 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .grades-table-container {
      overflow-x: auto;
    }

    .grades-table {
      width: 100%;
      border-collapse: collapse;
      min-width: 800px;
    }

    .grades-table th {
      background: #f8fafc;
      padding: 12px;
      text-align: left;
      font-weight: 600;
      color: #2d3748;
      border-bottom: 2px solid #e2e8f0;
    }

    .grades-table td {
      padding: 12px;
      border-bottom: 1px solid #f1f3f4;
    }

    .student-cell {
      min-width: 200px;
    }

    .student-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .student-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
    }

    .student-name {
      font-weight: 600;
      color: #2d3748;
    }

    .student-id {
      font-size: 12px;
      color: #718096;
    }

    .assignment-date {
      font-size: 11px;
      color: #718096;
      display: block;
    }

    .grade-cell {
      min-width: 120px;
    }

    .grade-input-container {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .grade-input {
      width: 60px;
      padding: 6px 8px;
      border: 2px solid #e2e8f0;
      border-radius: 4px;
      text-align: center;
      font-weight: 600;
    }

    .grade-input.excellent { border-color: #10b981; }
    .grade-input.good { border-color: #3b82f6; }
    .grade-input.average { border-color: #f59e0b; }
    .grade-input.poor { border-color: #ef4444; }

    .grade-status {
      font-size: 10px;
      font-weight: 600;
      text-align: center;
    }

    .grade-status.excellent { color: #10b981; }
    .grade-status.good { color: #3b82f6; }
    .grade-status.average { color: #f59e0b; }
    .grade-status.poor { color: #ef4444; }

    .average-cell {
      text-align: center;
    }

    .average-grade {
      font-size: 16px;
      font-weight: 700;
      padding: 4px 8px;
      border-radius: 4px;
    }

    .average-grade.excellent { background: #d1fae5; color: #065f46; }
    .average-grade.good { background: #dbeafe; color: #1e40af; }
    .average-grade.average { background: #fef3c7; color: #92400e; }
    .average-grade.poor { background: #fee2e2; color: #991b1b; }

    .actions-cell {
      display: flex;
      gap: 8px;
    }

    .stats-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      margin-bottom: 40px;
    }

    .stats-section h2 {
      margin: 0 0 24px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }

    .stat-card {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #718096;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .modal-header h2 {
      margin: 0;
      color: #2d3748;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #718096;
    }

    .grade-form {
      padding: 24px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #2d3748;
    }

    .form-group input,
    .form-group select {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }

    .cancel-btn {
      background: #6c757d;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
    }

    .save-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .grades-header {
        flex-direction: column;
        gap: 16px;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .form-row {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class TeacherGradesComponent {
  selectedClass = 1;
  selectedAssignment = '';
  gradeFilter = '';
  showGradeModal = false;

  newAssignment = {
    name: '',
    date: '',
    type: '',
    totalPoints: 100
  };

  classes = [
    {
      id: 1,
      name: 'Mathematics 5A',
      students: [
        { id: 'S001', name: 'John Smith', average: 88 },
        { id: 'S002', name: 'Emma Johnson', average: 92 },
        { id: 'S003', name: 'Michael Brown', average: 78 }
      ]
    },
    {
      id: 2,
      name: 'Algebra 5B',
      students: [
        { id: 'S004', name: 'Sarah Davis', average: 85 },
        { id: 'S005', name: 'Alex Wilson', average: 72 }
      ]
    }
  ];

  assignments = [
    { id: 1, name: 'Quiz 1', date: new Date('2024-01-15'), type: 'Quiz' },
    { id: 2, name: 'Midterm', date: new Date('2024-01-20'), type: 'Test' },
    { id: 3, name: 'Homework 1', date: new Date('2024-01-25'), type: 'Homework' }
  ];

  grades: { [key: string]: number } = {
    'S001-1': 85, 'S001-2': 90, 'S001-3': 88,
    'S002-1': 95, 'S002-2': 88, 'S002-3': 94,
    'S003-1': 75, 'S003-2': 80, 'S003-3': 79,
    'S004-1': 88, 'S004-2': 82, 'S004-3': 85,
    'S005-1': 70, 'S005-2': 75, 'S005-3': 71
  };

  constructor(private router: Router) {}

  selectClass(classId: number): void {
    this.selectedClass = classId;
  }

  getSelectedClass() {
    return this.classes.find(c => c.id === this.selectedClass);
  }

  getFilteredStudents() {
    const selectedClass = this.getSelectedClass();
    if (!selectedClass) return [];
    return selectedClass.students;
  }

  getGrade(studentId: string, assignmentId: number): number {
    return this.grades[`${studentId}-${assignmentId}`] || 0;
  }

  updateGrade(studentId: string, assignmentId: number, event: any): void {
    const grade = parseInt(event.target.value) || 0;
    this.grades[`${studentId}-${assignmentId}`] = Math.min(100, Math.max(0, grade));
    this.updateStudentAverage(studentId);
  }

  updateStudentAverage(studentId: string): void {
    const studentGrades = this.assignments.map(a => this.getGrade(studentId, a.id)).filter(g => g > 0);
    if (studentGrades.length > 0) {
      const average = Math.round(studentGrades.reduce((sum, grade) => sum + grade, 0) / studentGrades.length);
      const student = this.getSelectedClass()?.students.find(s => s.id === studentId);
      if (student) student.average = average;
    }
  }

  getGradeClass(grade: number): string {
    if (grade >= 90) return 'excellent';
    if (grade >= 80) return 'good';
    if (grade >= 70) return 'average';
    return 'poor';
  }

  getGradeStatus(grade: number): string {
    if (grade >= 90) return 'A';
    if (grade >= 80) return 'B';
    if (grade >= 70) return 'C';
    if (grade >= 60) return 'D';
    return 'F';
  }

  getClassAverage(): number {
    const students = this.getSelectedClass()?.students || [];
    if (students.length === 0) return 0;
    return Math.round(students.reduce((sum, student) => sum + student.average, 0) / students.length);
  }

  getTopPerformers(): number {
    const students = this.getSelectedClass()?.students || [];
    return students.filter(s => s.average >= 90).length;
  }

  getImprovementRate(): number {
    return 85; // Mock data
  }

  getAtRiskStudents(): number {
    const students = this.getSelectedClass()?.students || [];
    return students.filter(s => s.average < 70).length;
  }

  viewStudentDetails(student: any): void {
    console.log('View student details:', student);
  }

  sendFeedback(student: any): void {
    console.log('Send feedback:', student);
    this.router.navigate(['/teacher/messages'], { queryParams: { student: student.id } });
  }

  addAssignment(): void {
    if (this.newAssignment.name && this.newAssignment.date && this.newAssignment.type) {
      const newAssignment = {
        id: this.assignments.length + 1,
        name: this.newAssignment.name,
        date: new Date(this.newAssignment.date),
        type: this.newAssignment.type
      };
      this.assignments.push(newAssignment);
      this.showGradeModal = false;
      this.newAssignment = { name: '', date: '', type: '', totalPoints: 100 };
    }
  }

  exportGrades(): void {
    console.log('Export grades');
  }
}
