package com.ecocode.service;


import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification; // Import correct
import org.springframework.stereotype.Service;
import java.util.concurrent.ExecutionException;

@Service
public class NotificationService {
    public String sendNotification(String token, String title, String body) throws ExecutionException, InterruptedException {
        if (token == null || token.trim().isEmpty()) {
            throw new IllegalArgumentException("Le token ne peut pas être vide.");
        }
        if (title == null || title.trim().isEmpty()) {
            title = "Titre par défaut"; // Valeur par défaut si vide
        }
        if (body == null || body.trim().isEmpty()) {
            body = "Message par défaut"; // Valeur par défaut si vide
        }

        Message message = Message.builder()
                .setToken(token)
                .setNotification(Notification.builder()
                        .setTitle(title)
                        .setBody(body)
                        .build()) // Correction de l'utilisation de Notification
                .putData("click_action", "FLUTTER_NOTIFICATION_CLICK")
                .build();

        return FirebaseMessaging.getInstance().sendAsync(message).get();
    }
}

/*

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import org.springframework.stereotype.Service;
import java.util.concurrent.ExecutionException;

@Service
public class NotificationService {
    public String sendNotification(String token, String title, String body) throws ExecutionException, InterruptedException {
        Message message = Message.builder()
                .setToken(token)
                .putData("title", title)
                .putData("body", body)
                .build();

 
        return FirebaseMessaging.getInstance().sendAsync(message).get();
    }
}
*/