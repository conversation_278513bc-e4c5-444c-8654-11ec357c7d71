import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'parent_class.dart';
//import '../../../commun/base_url.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class ParentService {
  // Obtenir le profil d'un parent
  Future<Parent> getProfilParent(int parentId, String token) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/parents/$parentId/profil';

    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> responseData = json.decode(response.body);
        return Parent.fromJson(responseData);
      } else {
        throw Exception('Failed to get profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get profile: $e');
    }
  }

  // Récupérer tous les parents
  // Future<List<Parent>> getAllParents() async {
  //   final String apiUrl = '$baseURL/tuteur/all';
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   String token = prefs.getString('token') ?? '';
  //   try {
  //     final response = await http.get(
  //       Uri.parse(apiUrl),
  //       headers: <String, String>{
  //         'Authorization': 'Bearer $token',
  //         'Content-Type': 'application/json; charset=UTF-8',
  //       },
  //     );

  //     if (response.statusCode == 200) {
  //       List<dynamic> responseData = json.decode(response.body);
  //       List<Parent> parents =
  //           responseData.map((data) => Parent.fromJson(data)).toList();
  //       return parents;
  //     } else {
  //       throw Exception('Failed to get all parents: ${response.statusCode}');
  //     }
  //   } catch (e) {
  //     throw Exception('Failed to get all parents: $e');
  //   }
  // }

   Future<Map<String, dynamic>> getParentsByPage(int page, int size) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/tuteur/pagination?page=$page&size=$size';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String token = prefs.getString('token') ?? '';

    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 200) {
        // Parse the response body as a Map
        Map<String, dynamic> jsonResponse = json.decode(response.body);

        // Return the entire jsonResponse to handle pagination
        return jsonResponse;
      } else {
        throw Exception('Failed to get parents: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get parents: $e');
    }
  }

  // Ajouter un nouveau parent
  Future<void> addParent(Parent parent) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/tuteur/add';
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String token = prefs.getString('token') ?? '';

    try {
      var request = http.MultipartRequest('POST', Uri.parse(apiUrl))
        ..headers['Authorization'] = 'Bearer $token';

      // Add the TuteurEntity as a string part
      request.fields['TuteurEntity'] = jsonEncode(parent.toJson());

    
      final response = await request.send();

      if (response.statusCode == 200) {
        // Parent added successfully
      } else {
        throw Exception('Failed to add parent: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to add parent: $e');
    }
  }

  // Mettre à jour le profil d'un parent
  Future<void> updateProfilParent(Parent parent, String token) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl =
        '$baseURL/parents/modifierParent/${parent.idTuteur}';

    try {
      final response = await http.put(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(parent.toJson()),
      );

      if (response.statusCode == 204) {
        // Profil du parent mis à jour avec succès
      } else {
        throw Exception('Failed to update profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to update profile: $e');
    }
  }

  // Supprimer le profil d'un parent
  Future<void> deleteProfilParent(int parentId, String token) async {
    String baseURL = dotenv.get('baseURL');
    final String apiUrl = '$baseURL/parents/supprimerParent/$parentId';

    try {
      final response = await http.delete(
        Uri.parse(apiUrl),
        headers: <String, String>{
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      if (response.statusCode == 204) {
        // Profil du parent supprimé avec succès
      } else {
        throw Exception('Failed to delete profile: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to delete profile: $e');
    }
  }
}
