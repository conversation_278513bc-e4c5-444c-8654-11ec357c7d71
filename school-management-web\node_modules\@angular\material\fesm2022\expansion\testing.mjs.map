{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/testing/expansion-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/expansion/testing/accordion-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ContentContainerComponentHarness,\n  HarnessLoader,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {ExpansionPanelHarnessFilters} from './expansion-harness-filters';\n\n/** Selectors for the various `mat-expansion-panel` sections that may contain user content. */\nexport enum MatExpansionPanelSection {\n  HEADER = '.mat-expansion-panel-header',\n  TITLE = '.mat-expansion-panel-header-title',\n  DESCRIPTION = '.mat-expansion-panel-header-description',\n  CONTENT = '.mat-expansion-panel-content',\n}\n\n/** Harness for interacting with a standard mat-expansion-panel in tests. */\nexport class MatExpansionPanelHarness extends ContentContainerComponentHarness<MatExpansionPanelSection> {\n  static hostSelector = '.mat-expansion-panel';\n\n  private _header = this.locatorFor(MatExpansionPanelSection.HEADER);\n  private _title = this.locatorForOptional(MatExpansionPanelSection.TITLE);\n  private _description = this.locatorForOptional(MatExpansionPanelSection.DESCRIPTION);\n  private _expansionIndicator = this.locatorForOptional('.mat-expansion-indicator');\n  private _content = this.locatorFor(MatExpansionPanelSection.CONTENT);\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for an expansion-panel\n   * with specific attributes.\n   * @param options Options for narrowing the search:\n   *   - `title` finds an expansion-panel with a specific title text.\n   *   - `description` finds an expansion-panel with a specific description text.\n   *   - `expanded` finds an expansion-panel that is currently expanded.\n   *   - `disabled` finds an expansion-panel that is disabled.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: ExpansionPanelHarnessFilters = {},\n  ): HarnessPredicate<MatExpansionPanelHarness> {\n    return new HarnessPredicate(MatExpansionPanelHarness, options)\n      .addOption('title', options.title, (harness, title) =>\n        HarnessPredicate.stringMatches(harness.getTitle(), title),\n      )\n      .addOption('description', options.description, (harness, description) =>\n        HarnessPredicate.stringMatches(harness.getDescription(), description),\n      )\n      .addOption('content', options.content, (harness, content) =>\n        HarnessPredicate.stringMatches(harness.getTextContent(), content),\n      )\n      .addOption(\n        'expanded',\n        options.expanded,\n        async (harness, expanded) => (await harness.isExpanded()) === expanded,\n      )\n      .addOption(\n        'disabled',\n        options.disabled,\n        async (harness, disabled) => (await harness.isDisabled()) === disabled,\n      );\n  }\n\n  /** Whether the panel is expanded. */\n  async isExpanded(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-expanded');\n  }\n\n  /**\n   * Gets the title text of the panel.\n   * @returns Title text or `null` if no title is set up.\n   */\n  async getTitle(): Promise<string | null> {\n    const titleEl = await this._title();\n    return titleEl ? titleEl.text() : null;\n  }\n\n  /**\n   * Gets the description text of the panel.\n   * @returns Description text or `null` if no description is set up.\n   */\n  async getDescription(): Promise<string | null> {\n    const descriptionEl = await this._description();\n    return descriptionEl ? descriptionEl.text() : null;\n  }\n\n  /** Whether the panel is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await (await this._header()).getAttribute('aria-disabled')) === 'true';\n  }\n\n  /**\n   * Toggles the expanded state of the panel by clicking on the panel\n   * header. This method will not work if the panel is disabled.\n   */\n  async toggle(): Promise<void> {\n    await (await this._header()).click();\n  }\n\n  /** Expands the expansion panel if collapsed. */\n  async expand(): Promise<void> {\n    if (!(await this.isExpanded())) {\n      await this.toggle();\n    }\n  }\n\n  /** Collapses the expansion panel if expanded. */\n  async collapse(): Promise<void> {\n    if (await this.isExpanded()) {\n      await this.toggle();\n    }\n  }\n\n  /** Gets the text content of the panel. */\n  async getTextContent(): Promise<string> {\n    return (await this._content()).text();\n  }\n\n  /**\n   * Gets a `HarnessLoader` that can be used to load harnesses for\n   * components within the panel's content area.\n   * @deprecated Use either `getChildLoader(MatExpansionPanelSection.CONTENT)`, `getHarness` or\n   *    `getAllHarnesses` instead.\n   * @breaking-change 12.0.0\n   */\n  async getHarnessLoaderForContent(): Promise<HarnessLoader> {\n    return this.getChildLoader(MatExpansionPanelSection.CONTENT);\n  }\n\n  /** Focuses the panel. */\n  async focus(): Promise<void> {\n    return (await this._header()).focus();\n  }\n\n  /** Blurs the panel. */\n  async blur(): Promise<void> {\n    return (await this._header()).blur();\n  }\n\n  /** Whether the panel is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this._header()).isFocused();\n  }\n\n  /** Whether the panel has a toggle indicator displayed. */\n  async hasToggleIndicator(): Promise<boolean> {\n    return (await this._expansionIndicator()) !== null;\n  }\n\n  /** Gets the position of the toggle indicator. */\n  async getToggleIndicatorPosition(): Promise<'before' | 'after'> {\n    // By default the expansion indicator will show \"after\" the panel header content.\n    if (await (await this._header()).hasClass('mat-expansion-toggle-indicator-before')) {\n      return 'before';\n    }\n    return 'after';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {MatExpansionPanelHarness} from './expansion-harness';\nimport {AccordionHarnessFilters, ExpansionPanelHarnessFilters} from './expansion-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a standard mat-accordion in tests. */\nexport class MatAccordionHarness extends ComponentHarness {\n  static hostSelector = '.mat-accordion';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for an accordion\n   * with specific attributes.\n   * @param options Options for narrowing the search.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: AccordionHarnessFilters = {}): HarnessPredicate<MatAccordionHarness> {\n    return new HarnessPredicate(MatAccordionHarness, options);\n  }\n\n  /** Gets all expansion panels which are part of the accordion. */\n  async getExpansionPanels(\n    filter: ExpansionPanelHarnessFilters = {},\n  ): Promise<MatExpansionPanelHarness[]> {\n    return this.locatorForAll(MatExpansionPanelHarness.with(filter))();\n  }\n\n  /** Whether the accordion allows multiple expanded panels simultaneously. */\n  async isMulti(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-accordion-multi');\n  }\n}\n"], "names": [], "mappings": ";;AAeA;IACY;AAAZ,CAAA,UAAY,wBAAwB,EAAA;AAClC,IAAA,wBAAA,CAAA,QAAA,CAAA,GAAA,6BAAsC;AACtC,IAAA,wBAAA,CAAA,OAAA,CAAA,GAAA,mCAA2C;AAC3C,IAAA,wBAAA,CAAA,aAAA,CAAA,GAAA,yCAAuD;AACvD,IAAA,wBAAA,CAAA,SAAA,CAAA,GAAA,8BAAwC;AAC1C,CAAC,EALW,wBAAwB,KAAxB,wBAAwB,GAKnC,EAAA,CAAA,CAAA;AAED;AACM,MAAO,wBAAyB,SAAQ,gCAA0D,CAAA;AACtG,IAAA,OAAO,YAAY,GAAG,sBAAsB;IAEpC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,MAAM,CAAC;IAC1D,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,KAAK,CAAC;IAChE,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,WAAW,CAAC;AAC5E,IAAA,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,0BAA0B,CAAC;IACzE,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,OAAO,CAAC;AAEpE;;;;;;;;;AASG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAAwC,EAAE,EAAA;AAE1C,QAAA,OAAO,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,OAAO;aAC1D,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;aAE1D,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,WAAW,KAClE,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC;aAEtE,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,KACtD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,OAAO,CAAC;aAElE,SAAS,CACR,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;aAEvE,SAAS,CACR,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CACvE;;;AAIL,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,cAAc,CAAC;;AAGrD;;;AAGG;AACH,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE;AACnC,QAAA,OAAO,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;;AAGxC;;;AAGG;AACH,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE;AAC/C,QAAA,OAAO,aAAa,GAAG,aAAa,CAAC,IAAI,EAAE,GAAG,IAAI;;;AAIpD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;AAGhF;;;AAGG;AACH,IAAA,MAAM,MAAM,GAAA;QACV,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE;;;AAItC,IAAA,MAAM,MAAM,GAAA;QACV,IAAI,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;AAC9B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE;;;;AAKvB,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE;AAC3B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE;;;;AAKvB,IAAA,MAAM,cAAc,GAAA;QAClB,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE;;AAGvC;;;;;;AAMG;AACH,IAAA,MAAM,0BAA0B,GAAA;QAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,OAAO,CAAC;;;AAI9D,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE;;;AAIvC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE;;;AAItC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE;;;AAI3C,IAAA,MAAM,kBAAkB,GAAA;QACtB,OAAO,CAAC,MAAM,IAAI,CAAC,mBAAmB,EAAE,MAAM,IAAI;;;AAIpD,IAAA,MAAM,0BAA0B,GAAA;;AAE9B,QAAA,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,QAAQ,CAAC,uCAAuC,CAAC,EAAE;AAClF,YAAA,OAAO,QAAQ;;AAEjB,QAAA,OAAO,OAAO;;;;ACpJlB;AACM,MAAO,mBAAoB,SAAQ,gBAAgB,CAAA;AACvD,IAAA,OAAO,YAAY,GAAG,gBAAgB;AAEtC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAmC,EAAE,EAAA;AAC/C,QAAA,OAAO,IAAI,gBAAgB,CAAC,mBAAmB,EAAE,OAAO,CAAC;;;AAI3D,IAAA,MAAM,kBAAkB,CACtB,MAAA,GAAuC,EAAE,EAAA;AAEzC,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAIpE,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,qBAAqB,CAAC;;;;;;"}