import 'dart:convert';
import 'dart:typed_data';

import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/discipline/discipline_Pscreen.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/emploi/emploi.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/emploi/imagePreviewScreen.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/paiement/paiement.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/paiement/paiementManagement.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/pointage/pointage.dart';
import 'package:NovaSchool/models/getNumberOfNotRead.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/fileDownloadService.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/observation/observation_Pscreen.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../../../../../utils/constants/images.dart';
import '../carnet/carnet_Pscreen.dart';
import 'cours/cours_Pscreen.dart';
import '../home-Pcategorie.dart';
import 'exercice/exercice_Pscreen.dart';

class PHomePedagogicalElement extends StatefulWidget {
  const PHomePedagogicalElement({Key? key}) : super(key: key);

  @override
  _PHomePedagogicalElementState createState() =>
      _PHomePedagogicalElementState();
}

class _PHomePedagogicalElementState extends State<PHomePedagogicalElement> {
  final carnet = dotenv.get('CARNET_NAME');
  int unreadExercices = 0;
  int unreadCours = 0;
  int unreadObservations = 0;
  int unreadDisciplines = 0;
  MessageServices msg = MessageServices();
  AuthService auth = AuthService();
  FileDownloadService fileDownloadService = FileDownloadService();
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
    _fetchUnreadCounts();
    // _requestNotificationPermission();
    // _setupFCM();
  }

  void _requestNotificationPermission() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    NotificationSettings settings = await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('User granted permission');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      print('User granted provisional permission');
    } else {
      print('User declined or has not accepted permission');
    }
  }

  void _setupFCM() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Got a message whilst in the foreground!');
      print('Message data: ${message.data}');

      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
        _showNotification(
            message.notification!.title!, message.notification!.body!);
      }
    });
  }

  void _registerFCMToken() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    String? token = await messaging.getToken();
    print('FCM Token: $token');
  }

  void _showNotification(String title, String body) {
    final snackBar = SnackBar(
      content: Text('$title: $body'),
      action: SnackBarAction(
        label: 'OK',
        onPressed: () {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
        },
      ),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  Future<void> _fetchUnreadCounts() async {
    setState(() {
      isLoading = true; // Start loading
    });
    await Future.delayed(Duration(seconds: 2));
    try {
      final userId = await auth.getUserId();
      final eleveId = await auth.getEleveId();

      // Fetch unread counts from API
      GetNumberOfNotRead unreadCounts = await msg.getUnreadCount(
        idParentKeycloak: userId,
        idEleve: eleveId,
      );

      // Update the state with the fetched counts
      if (mounted) {
        setState(() {
          unreadExercices = unreadCounts.notReadExercice;
          unreadCours = unreadCounts.notReadCour;
          unreadObservations = unreadCounts.notReadObservation;
          unreadDisciplines = unreadCounts.notReadDiscipline;
          isLoading = false;
        });
        _showUnreadNotification(context);
      }
    } catch (e) {
      print('Failed to fetch unread counts: $e');
      setState(() {
        isLoading = false; // Stop loading on error
      });
    }
  }

  void _showUnreadNotification(BuildContext context) {
    List<String> unreadNotifications = [];

    // Check for unread messages and add to the list
    if (unreadExercices > 0) {
      unreadNotifications.add('$unreadExercices exercice(s) non lu(s)');
    }
    if (unreadCours > 0) {
      unreadNotifications.add('$unreadCours cour(s) non lu(s)');
    }
    if (unreadObservations > 0) {
      unreadNotifications.add('$unreadObservations observation(s) non lu(s)');
    }
    if (unreadDisciplines > 0) {
      unreadNotifications.add('$unreadDisciplines discipline(s) non lu(s)');
    }

    // If there are any unread notifications, display them in the snackbar
    if (unreadNotifications.isNotEmpty) {
      // Join the list of notifications into a single string
      final notificationMessage = unreadNotifications.join(', ');

      // Display snackbar with all unread notifications
      /*final snackBar = SnackBar(
        content:
            Text('Vous avez des notifications non lues : $notificationMessage'),
        action: SnackBarAction(
          label: 'OK',
          onPressed: () {
            // Hide the snackbar when OK is pressed
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(
            days: 365), // Ensure it doesn't disappear until OK is pressed
      );

      // Show the snackbar
      ScaffoldMessenger.of(context).showSnackBar(snackBar);*/
    }
  }

  void _showSnackbarMessage(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // Schedule a delayed pop (to close the dialog) after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop(); // Automatically close after 3 seconds
          }
        });
        return AlertDialog(
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _onMessageRead(String type) async {
    setState(() {
      if (type == 'Cours' && unreadCours > 0) {
        unreadCours--;
      } else if (type == 'Exercices' && unreadExercices > 0) {
        unreadExercices--;
      } else if (type == 'Observation' && unreadObservations > 0) {
        unreadObservations--;
      }else if (type == 'Discipline' && unreadDisciplines > 0) {
        unreadDisciplines--;
      }
    });
  }

  Future<void> _onPressEmploi(BuildContext context) async {
    final eleveId = await auth.getEleveId();
    await fileDownloadService.displayImageByEleve(context, eleveId);
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _fetchUnreadCounts,
      child: LayoutBuilder(
        builder: (context, constraints) {
          int crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Column(
                children: [
                  // Refresh button
                  IconButton(
                    icon: isLoading
                        ? CircularProgressIndicator()
                        : Icon(
                            Icons.refresh,
                            color: Color(0xFF4099FF),
                          ),
                    onPressed: isLoading ? null : _fetchUnreadCounts,
                  ),
                 // SizedBox(height: 20,),
                  GridView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                    itemCount: 8,
                    shrinkWrap: true,
                    //physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2, // 2 colonnes pour bien organiser
                      crossAxisSpacing: 10, // Espacement horizontal entre les boutons
                      mainAxisSpacing: 0, // Espacement vertical entre les boutons
                      childAspectRatio: 1.2, // Ajuster le rapport largeur/hauteur

                    ),
                    itemBuilder: (context, index) {
                      switch (index) {
                        case 0:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PCoursScreen(
                                      onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.cours,
                            //iconData: Icons.book,
                            title: 'Cours',
                            unreadCount: unreadCours,
                          );

                        case 1:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PExerciceScreen(
                                      onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.exercice,
                            //iconData: Icons.assignment,
                            title: 'Exercices',
                            unreadCount: unreadExercices,
                          );
                        case 2:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PObservationScreen(
                                      onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.observation,
                            //iconData: Icons.visibility,
                            title: 'Observations',
                            unreadCount: unreadObservations,
                          );
                        case 3:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => PCarnetScreen()),
                              );
                            },
                            icon: AppImages.carnet,
                            //iconData: Icons.school,
                            title: 'Notes',
                          );
                        case 4:
                          return PHomeCategories(
                            onPress: () {
                              // Navigator.push(
                              //   context,
                              //   MaterialPageRoute(
                              //       builder: (context) => PEmploiScreen()),
                              // );
                              _onPressEmploi(context);
                            },
                            icon: AppImages.emploi,
                            //iconData: Icons.schedule,
                            title: 'Emploi',
                          );
                        case 5:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PDisciplineScreen(
                                      onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.discipline,
                            //iconData: Icons.assignment_turned_in, // Représente la discipline via des tâches complètes
                            iconData: Icons.priority_high, // Représente la discipline via des tâches complètes

                            title: 'Discipline',
                            unreadCount: unreadDisciplines,
                          );
                        case 6:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        PaiementManagementPage()),
                              );
                            },
                            icon: AppImages.paiement,
                            //iconData: Icons.payment,
                            title: 'Paiements',
                          );
                        case 7:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        PPointageScreen()),
                              );
                            },
                            icon: AppImages.pointage,
                            //iconData: Icons.fingerprint,
                            title: 'Pointage',
                          );
                        default:
                          return SizedBox.shrink();
                      }
                    },
                  ),
                  /*Column(
                    children: List.generate(8, (index) {
                      switch (index) {
                        case 0:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PExerciceScreen(onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.exercice,
                            title: 'Exercices',
                            unreadCount: unreadExercices,
                          );
                        case 1:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PCoursScreen(onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.cours,
                            title: 'Cours',
                            unreadCount: unreadCours,
                          );
                        case 2:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PObservationScreen(onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.observation,
                            title: 'Observations',
                            unreadCount: unreadObservations,
                          );
                        case 3:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PDisciplineScreen(onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.discipline,
                            title: 'Discipline',
                            unreadCount: unreadDisciplines,
                          );
                        case 4:
                          return PHomeCategories(
                            onPress: () => _onPressEmploi(context),
                            icon: AppImages.emploi,
                            title: 'Emploi',
                          );
                        case 5:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => PCarnetScreen()),
                              );
                            },
                            icon: AppImages.carnet,
                            title: 'Bulletin',
                          );
                        case 6:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => PaiementManagementPage()),
                              );
                            },
                            icon: AppImages.paiement,
                            title: 'Paiement',
                          );
                        case 7:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => PPointageScreen()),
                              );
                            },
                            icon: AppImages.pointage,
                            title: 'Pointage',
                          );
                        default:
                          return SizedBox.shrink();
                      }
                    }),
                  ),*/


                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
/**
  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _fetchUnreadCounts,
      child: LayoutBuilder(
        builder: (context, constraints) {
          int crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Column(
                children: [
                  // Refresh button
                  IconButton(
                    icon: isLoading
                        ? CircularProgressIndicator()
                        : Icon(
                            Icons.refresh,
                            color: Color(0xFF4099FF),
                          ),
                    onPressed: isLoading ? null : _fetchUnreadCounts,
                  ),
                  GridView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    itemCount: 8,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: crossAxisCount,
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      childAspectRatio: 1,
                    ),
                    itemBuilder: (context, index) {
                      switch (index) {
                        case 0:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PExerciceScreen(
                                      onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.exercice,
                            // iconData: Icons.assignment,
                            title: 'Exercices',
                            unreadCount: unreadExercices,
                          );
                        case 1:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PCoursScreen(
                                      onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.cours,
                            // iconData: Icons.book,
                            title: 'Cours',
                            unreadCount: unreadCours,
                          );
                        case 2:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PObservationScreen(
                                      onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.observation,
                            // iconData: Icons.visibility,
                            title: 'Observations',
                            unreadCount: unreadObservations,
                          );
                          case 3:
                          return PHomeCategories(
                            onPress: () async {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PDisciplineScreen(
                                      onMessageRead: _onMessageRead),
                                ),
                              );
                            },
                            icon: AppImages.discipline,
                            title: 'Discipline',
                            unreadCount: unreadDisciplines,
                          );
                        case 4:
                          return PHomeCategories(
                            onPress: () {
                              // Navigator.push(
                              //   context,
                              //   MaterialPageRoute(
                              //       builder: (context) => PEmploiScreen()),
                              // );
                              _onPressEmploi(context);
                            },
                            icon: AppImages.emploi,
                            // iconData: Icons.schedule,
                            title: 'Emploi',
                          );
                        case 5:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => PCarnetScreen()),
                              );
                            },
                            icon: AppImages.carnet,
                            // iconData: Icons.note,
                            title: carnet,
                          );
                        case 6:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        PaiementManagementPage()),
                              );
                            },
                            icon: AppImages.paiement,
                            // iconData: Icons.note,
                            title: 'Paiement',
                          );
                          case 7:
                          return PHomeCategories(
                            onPress: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        PPointageScreen()),
                              );
                            },
                            icon: AppImages.pointage,
                            // iconData: Icons.note,
                            title: 'Pointage',
                          );
                        default:
                          return SizedBox.shrink();
                      }
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
**/
