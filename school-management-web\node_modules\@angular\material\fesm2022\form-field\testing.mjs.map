{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/testing/error-harness.ts", "../../../../../../k8-fastbuild-ST-46c76129e412/bin/src/material/form-field/testing/form-field-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  BaseHarnessFilters,\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\n\n/** A set of criteria that can be used to filter a list of error harness instances. */\nexport interface ErrorHarnessFilters extends BaseHarnessFilters {\n  /** Only find instances whose text matches the given value. */\n  text?: string | RegExp;\n}\n\n/** Harness for interacting with a `mat-error` in tests. */\nexport class MatErrorHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-form-field-error';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for an error with specific\n   * attributes.\n   * @param options Options for filtering which error instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatErrorHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ErrorHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return MatErrorHarness._getErrorPredicate(this, options);\n  }\n\n  protected static _getErrorPredicate<T extends MatErrorHarness>(\n    type: ComponentHarnessConstructor<T>,\n    options: ErrorHarnessFilters,\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(type, options).addOption('text', options.text, (harness, text) =>\n      HarnessPredicate.stringMatches(harness.getText(), text),\n    );\n  }\n\n  /** Gets a promise for the error's label text. */\n  async getText(): Promise<string> {\n    return (await this.host()).text();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  HarnessQuery,\n  parallel,\n} from '@angular/cdk/testing';\nimport {ErrorHarnessFilters, MatErrorHarness} from './error-harness';\nimport {MatInputHarness} from '../../input/testing';\nimport {MatFormFieldControlHarness} from './control';\nimport {MatSelectHarness} from '../../select/testing';\nimport {MatDatepickerInputHarness, MatDateRangeInputHarness} from '../../datepicker/testing';\nimport {FormFieldHarnessFilters} from './form-field-harness-filters';\n\n/** Possible harnesses of controls which can be bound to a form-field. */\nexport type FormFieldControlHarness =\n  | MatInputHarness\n  | MatSelectHarness\n  | MatDatepickerInputHarness\n  | MatDateRangeInputHarness;\n\nexport class MatFormFieldHarness extends ComponentHarness {\n  private _prefixContainer = this.locatorForOptional('.mat-mdc-form-field-text-prefix');\n  private _suffixContainer = this.locatorForOptional('.mat-mdc-form-field-text-suffix');\n  private _label = this.locatorForOptional('.mdc-floating-label');\n  private _hints = this.locatorForAll('.mat-mdc-form-field-hint');\n  private _inputControl = this.locatorForOptional(MatInputHarness);\n  private _selectControl = this.locatorForOptional(MatSelectHarness);\n  private _datepickerInputControl = this.locatorForOptional(MatDatepickerInputHarness);\n  private _dateRangeInputControl = this.locatorForOptional(MatDateRangeInputHarness);\n  private _textField = this.locatorFor('.mat-mdc-text-field-wrapper');\n  private _errorHarness = MatErrorHarness;\n\n  static hostSelector = '.mat-mdc-form-field';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a form field with specific\n   * attributes.\n   * @param options Options for filtering which form field instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatFormFieldHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: FormFieldHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('floatingLabelText', options.floatingLabelText, async (harness, text) =>\n        HarnessPredicate.stringMatches(await harness.getLabel(), text),\n      )\n      .addOption(\n        'hasErrors',\n        options.hasErrors,\n        async (harness, hasErrors) => (await harness.hasErrors()) === hasErrors,\n      )\n      .addOption(\n        'isValid',\n        options.isValid,\n        async (harness, isValid) => (await harness.isControlValid()) === isValid,\n      );\n  }\n\n  /** Gets the appearance of the form-field. */\n  async getAppearance(): Promise<'fill' | 'outline'> {\n    const textFieldEl = await this._textField();\n    if (await textFieldEl.hasClass('mdc-text-field--outlined')) {\n      return 'outline';\n    }\n    return 'fill';\n  }\n\n  /** Whether the form-field has a label. */\n  async hasLabel(): Promise<boolean> {\n    return (await this._label()) !== null;\n  }\n\n  /** Whether the label is currently floating. */\n  async isLabelFloating(): Promise<boolean> {\n    const labelEl = await this._label();\n    return labelEl !== null ? await labelEl.hasClass('mdc-floating-label--float-above') : false;\n  }\n\n  /** Gets the label of the form-field. */\n  async getLabel(): Promise<string | null> {\n    const labelEl = await this._label();\n    return labelEl ? labelEl.text() : null;\n  }\n\n  /** Whether the form-field has errors. */\n  async hasErrors(): Promise<boolean> {\n    return (await this.getTextErrors()).length > 0;\n  }\n\n  /** Whether the form-field is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-form-field-disabled');\n  }\n\n  /** Whether the form-field is currently autofilled. */\n  async isAutofilled(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-form-field-autofilled');\n  }\n\n  /**\n   * Gets the harness of the control that is bound to the form-field. Only\n   * default controls such as \"MatInputHarness\" and \"MatSelectHarness\" are\n   * supported.\n   */\n  async getControl(): Promise<FormFieldControlHarness | null>;\n\n  /**\n   * Gets the harness of the control that is bound to the form-field. Searches\n   * for a control that matches the specified harness type.\n   */\n  async getControl<X extends MatFormFieldControlHarness>(\n    type: ComponentHarnessConstructor<X>,\n  ): Promise<X | null>;\n\n  /**\n   * Gets the harness of the control that is bound to the form-field. Searches\n   * for a control that matches the specified harness predicate.\n   */\n  async getControl<X extends MatFormFieldControlHarness>(\n    type: HarnessPredicate<X>,\n  ): Promise<X | null>;\n\n  // Implementation of the \"getControl\" method overload signatures.\n  async getControl<X extends MatFormFieldControlHarness>(type?: HarnessQuery<X>) {\n    if (type) {\n      return this.locatorForOptional(type)();\n    }\n    const [select, input, datepickerInput, dateRangeInput] = await parallel(() => [\n      this._selectControl(),\n      this._inputControl(),\n      this._datepickerInputControl(),\n      this._dateRangeInputControl(),\n    ]);\n\n    // Match the datepicker inputs first since they can also have a `MatInput`.\n    return datepickerInput || dateRangeInput || select || input;\n  }\n\n  /** Gets the theme color of the form-field. */\n  async getThemeColor(): Promise<'primary' | 'accent' | 'warn'> {\n    const hostEl = await this.host();\n    const [isAccent, isWarn] = await parallel(() => {\n      return [hostEl.hasClass('mat-accent'), hostEl.hasClass('mat-warn')];\n    });\n    if (isAccent) {\n      return 'accent';\n    } else if (isWarn) {\n      return 'warn';\n    }\n    return 'primary';\n  }\n\n  /** Gets error messages which are currently displayed in the form-field. */\n  async getTextErrors(): Promise<string[]> {\n    const errors = await this.getErrors();\n    return parallel(() => errors.map(e => e.getText()));\n  }\n\n  /** Gets all of the error harnesses in the form field. */\n  async getErrors(filter: ErrorHarnessFilters = {}): Promise<MatErrorHarness[]> {\n    return this.locatorForAll(this._errorHarness.with(filter))();\n  }\n\n  /** Gets hint messages which are currently displayed in the form-field. */\n  async getTextHints(): Promise<string[]> {\n    const hints = await this._hints();\n    return parallel(() => hints.map(e => e.text()));\n  }\n\n  /** Gets the text inside the prefix element. */\n  async getPrefixText(): Promise<string> {\n    const prefix = await this._prefixContainer();\n    return prefix ? prefix.text() : '';\n  }\n\n  /** Gets the text inside the suffix element. */\n  async getSuffixText(): Promise<string> {\n    const suffix = await this._suffixContainer();\n    return suffix ? suffix.text() : '';\n  }\n\n  /**\n   * Whether the form control has been touched. Returns \"null\"\n   * if no form control is set up.\n   */\n  async isControlTouched(): Promise<boolean | null> {\n    if (!(await this._hasFormControl())) {\n      return null;\n    }\n    return (await this.host()).hasClass('ng-touched');\n  }\n\n  /**\n   * Whether the form control is dirty. Returns \"null\"\n   * if no form control is set up.\n   */\n  async isControlDirty(): Promise<boolean | null> {\n    if (!(await this._hasFormControl())) {\n      return null;\n    }\n    return (await this.host()).hasClass('ng-dirty');\n  }\n\n  /**\n   * Whether the form control is valid. Returns \"null\"\n   * if no form control is set up.\n   */\n  async isControlValid(): Promise<boolean | null> {\n    if (!(await this._hasFormControl())) {\n      return null;\n    }\n    return (await this.host()).hasClass('ng-valid');\n  }\n\n  /**\n   * Whether the form control is pending validation. Returns \"null\"\n   * if no form control is set up.\n   */\n  async isControlPending(): Promise<boolean | null> {\n    if (!(await this._hasFormControl())) {\n      return null;\n    }\n    return (await this.host()).hasClass('ng-pending');\n  }\n\n  /** Checks whether the form-field control has set up a form control. */\n  private async _hasFormControl(): Promise<boolean> {\n    const hostEl = await this.host();\n    // If no form \"NgControl\" is bound to the form-field control, the form-field\n    // is not able to forward any control status classes. Therefore if either the\n    // \"ng-touched\" or \"ng-untouched\" class is set, we know that it has a form control\n    const [isTouched, isUntouched] = await parallel(() => [\n      hostEl.hasClass('ng-touched'),\n      hostEl.hasClass('ng-untouched'),\n    ]);\n    return isTouched || isUntouched;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAqBA;AACM,MAAO,eAAgB,SAAQ,gBAAgB,CAAA;AACnD,IAAA,OAAO,YAAY,GAAG,2BAA2B;AAEjD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA+B,EAAE,EAAA;QAEjC,OAAO,eAAe,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC;;AAGhD,IAAA,OAAO,kBAAkB,CACjC,IAAoC,EACpC,OAA4B,EAAA;AAE5B,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KACvF,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CACxD;;;AAIH,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;;ACpB/B,MAAO,mBAAoB,SAAQ,gBAAgB,CAAA;AAC/C,IAAA,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,iCAAiC,CAAC;AAC7E,IAAA,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,iCAAiC,CAAC;AAC7E,IAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC;AACvD,IAAA,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,0BAA0B,CAAC;AACvD,IAAA,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC;AACxD,IAAA,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC;AAC1D,IAAA,uBAAuB,GAAG,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,CAAC;AAC5E,IAAA,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC;AAC1E,IAAA,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,6BAA6B,CAAC;IAC3D,aAAa,GAAG,eAAe;AAEvC,IAAA,OAAO,YAAY,GAAG,qBAAqB;AAE3C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAmC,EAAE,EAAA;AAErC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;aACtC,SAAS,CAAC,mBAAmB,EAAE,OAAO,CAAC,iBAAiB,EAAE,OAAO,OAAO,EAAE,IAAI,KAC7E,gBAAgB,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC;aAE/D,SAAS,CACR,WAAW,EACX,OAAO,CAAC,SAAS,EACjB,OAAO,OAAO,EAAE,SAAS,KAAK,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,MAAM,SAAS;aAExE,SAAS,CACR,SAAS,EACT,OAAO,CAAC,OAAO,EACf,OAAO,OAAO,EAAE,OAAO,KAAK,CAAC,MAAM,OAAO,CAAC,cAAc,EAAE,MAAM,OAAO,CACzE;;;AAIL,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE;QAC3C,IAAI,MAAM,WAAW,CAAC,QAAQ,CAAC,0BAA0B,CAAC,EAAE;AAC1D,YAAA,OAAO,SAAS;;AAElB,QAAA,OAAO,MAAM;;;AAIf,IAAA,MAAM,QAAQ,GAAA;QACZ,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI;;;AAIvC,IAAA,MAAM,eAAe,GAAA;AACnB,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE;AACnC,QAAA,OAAO,OAAO,KAAK,IAAI,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,GAAG,KAAK;;;AAI7F,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE;AACnC,QAAA,OAAO,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;;;AAIxC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,GAAG,CAAC;;;AAIhD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,yBAAyB,CAAC;;;AAIhE,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,2BAA2B,CAAC;;;IA2BlE,MAAM,UAAU,CAAuC,IAAsB,EAAA;QAC3E,IAAI,IAAI,EAAE;AACR,YAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;;AAExC,QAAA,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;YAC5E,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,EAAE;AAC9B,SAAA,CAAC;;AAGF,QAAA,OAAO,eAAe,IAAI,cAAc,IAAI,MAAM,IAAI,KAAK;;;AAI7D,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAChC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAK;AAC7C,YAAA,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AACrE,SAAC,CAAC;QACF,IAAI,QAAQ,EAAE;AACZ,YAAA,OAAO,QAAQ;;aACV,IAAI,MAAM,EAAE;AACjB,YAAA,OAAO,MAAM;;AAEf,QAAA,OAAO,SAAS;;;AAIlB,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE;AACrC,QAAA,OAAO,QAAQ,CAAC,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;;;AAIrD,IAAA,MAAM,SAAS,CAAC,MAAA,GAA8B,EAAE,EAAA;AAC9C,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAI9D,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE;AACjC,QAAA,OAAO,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;;;AAIjD,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE;AAC5C,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;;;AAIpC,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE;AAC5C,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;;AAGpC;;;AAGG;AACH,IAAA,MAAM,gBAAgB,GAAA;QACpB,IAAI,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;AACnC,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,CAAC;;AAGnD;;;AAGG;AACH,IAAA,MAAM,cAAc,GAAA;QAClB,IAAI,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;AACnC,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC;;AAGjD;;;AAGG;AACH,IAAA,MAAM,cAAc,GAAA;QAClB,IAAI,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;AACnC,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC;;AAGjD;;;AAGG;AACH,IAAA,MAAM,gBAAgB,GAAA;QACpB,IAAI,EAAE,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE;AACnC,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,CAAC;;;AAI3C,IAAA,MAAM,eAAe,GAAA;AAC3B,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;;;;QAIhC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;AACpD,YAAA,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;AAC7B,YAAA,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;AAChC,SAAA,CAAC;QACF,OAAO,SAAS,IAAI,WAAW;;;;;;"}