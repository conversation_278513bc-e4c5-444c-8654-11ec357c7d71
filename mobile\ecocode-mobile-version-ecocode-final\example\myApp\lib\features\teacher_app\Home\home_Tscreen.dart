/*import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/discipline/discipline_Ascreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/observations/observation_TScreen.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/app_secondHeadBar.dart';
import 'package:NovaSchool/commun/teacher_app/app_TheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import '../../../commun/app_background.dart';
import 'home_TCategorie.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'home_TWelcome.dart';

class THomeScreen extends StatelessWidget {
  const THomeScreen({Key? key}) : super(key: key);

  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, String>>(
      future: _getUserInfo(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        } else if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Text(
                'Erreur: ${snapshot.error}',
                style: TextStyle(color: Colors.red),
              ),
            ),
          );
        } else if (snapshot.hasData) {
          final userInfo = snapshot.data!;
          return Scaffold(
            body: AppBackground(
              child: Column(
                children: [
                  PrimaryHeaderContainer(
                    child: Column(
                      children: [
                        TAppHeadBar(
                          profilPage: TProfilScreen(
                            firstName: userInfo['senderUsername'] ?? '',
                            lastName: userInfo['senderUserSurname'] ?? '',
                            email: userInfo['email'] ?? '',
                            profile: userInfo['profile'] ?? '',
                          ),
                        ),
                        THomeWelcome(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                        ),
                        SizedBox(height: 50),
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          THomeCategories(
                            image: AppImages.exercice,
                            title: 'Exercice',
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TExerciceScreen()),
                              );
                            },
                          ),
                          SizedBox(height: 25),
                          THomeCategories(
                            image: AppImages.cours,
                            title: 'Cours',
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TCoursScreen()),
                              );
                            },
                          ),
                          SizedBox(height: 25),
                          THomeCategories(
                            image: AppImages.observation,
                            title: 'Observations',
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TObservationsScreen()),
                              );
                            },
                          ),
                          // SizedBox(height: 25),
                          // THomeCategories(
                          //   image: AppImages.discipline,
                          //   title: 'Discipline',
                          //   onPressed: () {
                          //     Navigator.push(
                          //       context,
                          //       MaterialPageRoute(
                          //           builder: (context) => TDisciplineScreen()),
                          //     );
                          //   },
                          // ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          return Scaffold(
            body: Center(child: Text('Aucune donnée disponible')),
          );
        }
      },
    );
  }
}
*/
/******
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:flutter/animation.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/observations/observation_TScreen.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'home_TCategorie.dart';
import 'package:NovaSchool/services/auth_service.dart';

class THomeScreen extends StatelessWidget {


  const THomeScreen({Key? key}) : super(key: key);

  Future<Map<String, String>> _getUserInfo() async {
    //final Widget profilPage;
    final authService = AuthService();
    return await authService.getSenderDetails();
  }

  Widget _buildAlignedButton(BuildContext context,
      {required MainAxisAlignment alignment,
        required String image,
        required String title,
        required Widget screen}) {
    return Row(
      mainAxisAlignment: alignment,
      children: [
        Container(
          width: MediaQuery
              .of(context)
              .size
              .width * 0.7, // Taille des boutons
          child: THomeCategories(
            image: image,
            title: title,
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => screen),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => true,
      child: Scaffold(
        backgroundColor: Color(0xFFF2F2F2),
        drawer: TeacherDrawer(),
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              centerTitle: true,
              title: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
              actions: [
                IconButton(
                  icon: Icon(
                    Icons.person_outline, color: Colors.black, size: 25,),
                  onPressed: () async {
                    Map<String, String> userInfo = await _getUserInfo();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            TProfilScreen(
                              firstName: userInfo['senderUsername'] ?? '',
                              lastName: userInfo['senderUserSurname'] ?? '',
                              email: userInfo['email'] ?? '',
                              profile: userInfo['profile'] ?? '',
                            ),
                      ),
                    );
                  },
                ), /* _buildIconButton(
                  icon: Icons.person_outline,
                  tooltip: 'Profil',
                  onPressed: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => widget.profilPage),
                  ),
                ),*/

                /* IconButton(
                  icon: Icon(Icons.person_outline, color: Colors.black),
                  onPressed: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => TProfilScreen(firstName: userInfo['senderUsername'] ?? '',
                      lastName: userInfo['senderUserSurname'] ?? '',
                      email: userInfo['email'] ?? '',
                      profile: userInfo['profile'] ?? '',
                    )),
                  ),
                ),*/
              ],
            ),
          ),
        ),
        body: AppBackground(
          child: FutureBuilder<Map<String, String>>(
            future: _getUserInfo(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Erreur: \${snapshot.error}',
                    style: TextStyle(color: Colors.red),
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return Center(child: Text('Aucune donnée disponible'));
              }/*
// Dans ton return (après le check de snapshot dans le FutureBuilder)
              return SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: 30),
                    _buildAlignedButton(
                      context,
                      alignment: MainAxisAlignment.start, // à gauche
                      image: AppImages.exercice,
                      title: 'Exercice',
                      screen: TExerciceScreen(),
                    ),
                    SizedBox(height: 25),
                    _buildAlignedButton(
                      context,
                      alignment: MainAxisAlignment.end, // à droite
                      image: AppImages.cours,
                      title: 'Cours',
                      screen: TCoursScreen(),
                    ),
                    SizedBox(height: 25),
                    _buildAlignedButton(
                      context,
                      alignment: MainAxisAlignment.start, // à gauche
                      image: AppImages.observation,
                      title: 'Observations',
                      screen: TObservationsScreen(),
                    ),
                  ],
                ),
              );
            },
          ),),
      ),
    );
  }
}
*/



              return SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(height: 30),
                    THomeCategories(
                      image: AppImages.exercice,
                      title: 'Exercice',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => TExerciceScreen()),
                        );
                      },
                    ),
                    SizedBox(height: 25),
                    THomeCategories(
                      image: AppImages.cours,
                      title: 'Cours',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => TCoursScreen()),
                        );
                      },
                    ),
                    SizedBox(height: 25),
                    THomeCategories(
                      image: AppImages.observation,
                      title: 'Observations',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => TObservationsScreen()),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
***/
/**
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/observations/observation_TScreen.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'home_TCategorie.dart';
import 'package:NovaSchool/services/auth_service.dart';

class THomeScreen extends StatelessWidget {
  const THomeScreen({Key? key}) : super(key: key);

  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }

  Widget buildCustomCardButton(BuildContext context, {
    required String image,
    required String title,
    required Widget screen,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => screen),
          );
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          padding: EdgeInsets.symmetric(vertical: 20, horizontal: 15),
          child: Row(
            children: [
              Image.asset(
                image,
                height: 50,
                width: 50,
              ),
              SizedBox(width: 20),
              Text(
                title,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => true,
      child: Scaffold(
        backgroundColor: Color(0xFFF2F2F2),
        drawer: TeacherDrawer(),
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              centerTitle: true,
              title: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.person_outline, color: Colors.black, size: 25),
                  onPressed: () async {
                    Map<String, String> userInfo = await _getUserInfo();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        body: AppBackground(
          child: FutureBuilder<Map<String, String>>(
            future: _getUserInfo(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Erreur: ${snapshot.error}',
                    style: TextStyle(color: Colors.red),
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return Center(child: Text('Aucune donnée disponible'));
              }

              return SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(height: 50),
                    buildCustomCardButton(
                      context,
                      image: AppImages.exercice,
                      title: 'Exercices',
                      screen: TExerciceScreen(),
                    ),
                    SizedBox(height: 50),
                    buildCustomCardButton(
                      context,
                      image: AppImages.cours,
                      title: 'Cours',
                      screen: TCoursScreen(),
                    ),
                    SizedBox(height: 50),
                    buildCustomCardButton(
                      context,
                      image: AppImages.observation,
                      title: 'Observations',
                      screen: TObservationsScreen(),
                    ),
                    SizedBox(height: 50),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
**/
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/features/parent_app/Home/pedagogicalElement/emploi/emploi.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_TCategorie.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/Emploi/empoi_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/observations/observation_TScreen.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';

class THomeScreen extends StatefulWidget {
  const THomeScreen({Key? key}) : super(key: key);

  @override
  State<THomeScreen> createState() => _THomeScreenState();
}
class _THomeScreenState extends State<THomeScreen> {
  bool isLoading = false;

  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }

  Future<void> _fetchUnreadCounts() async {
    setState(() => isLoading = true);
    await Future.delayed(Duration(seconds: 1)); // Remplacer par ta logique
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => true,
      child: Scaffold(
        backgroundColor: Color(0xFFF2F2F2),
        drawer: TeacherDrawer(),
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              centerTitle: true,
              iconTheme: IconThemeData(color: Colors.white, size: 30),
              title: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),
                ],
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.logout, color: Colors.white),
                  //title: Text('Déconnexion'),
                  onPressed: () async {
                    await AuthService.logoutKeycloak();
                  },
                ),
                /*IconButton(
                  icon: Icon(Icons.person_outline, color: Colors.white, size: 30),
                  onPressed: () async {
                    Map<String, String> userInfo = await _getUserInfo();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),
                    );
                  },
                ),*/
              ],
            ),
          ),
        ),
        body: AppBackground(
          child: FutureBuilder<Map<String, String>>(
            future: _getUserInfo(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Erreur: ${snapshot.error}',
                    style: const TextStyle(color: Colors.red),
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return const Center(child: Text('Aucune donnée disponible'));
              }
              return RefreshIndicator(
                onRefresh: _fetchUnreadCounts,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    int crossAxisCount = constraints.maxWidth > 600 ? 3 : 2;

                    return SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 20),
                        child: Column(
                          children: [
                            // Bouton Refresh en haut
                            Align(
                              alignment: Alignment.center,
                              child: IconButton(
                                icon: isLoading
                                    ? const CircularProgressIndicator()
                                    : const Icon(
                                  Icons.refresh,
                                  color: Color(0xFF4099FF),
                                ),
                                onPressed: isLoading
                                    ? null
                                    : _fetchUnreadCounts,
                              ),
                            ),
                            const SizedBox(height: 40),
                            // Grille des catégories
                            Center(
                              child: ConstrainedBox(
                                constraints: const BoxConstraints(
                                    maxWidth: 600),
                                child: GridView.count(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  crossAxisCount: 2,//crossAxisCount,
                                  crossAxisSpacing: 10,
                                  mainAxisSpacing: 15,
                                  childAspectRatio: 0.8,
                                  children: [
                                    THomeCategories(
                                      iconimage: AppImages.cours,
                                      //iconData: Icons.book,
                                      title: 'Cours',
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  TCoursScreen()),
                                        );
                                      },
                                    ),
                                    THomeCategories(
                                      iconimage: AppImages.exercice,
                                      //iconData: Icons.assignment,
                                      title: 'Exercices',
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  TExerciceScreen()),
                                        );
                                      },
                                    ),
                                    THomeCategories(
                                      iconimage: AppImages.observation,
                                      //iconData: Icons.remove_red_eye,
                                      title: 'Observations',
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  TObservationsScreen()),
                                        );
                                      },
                                    ),
                                    THomeCategories(
                                      iconimage: AppImages.emploi,
                                      //iconData: Icons.schedule,
                                      title: 'Emploi',
                                      onPressed: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  TEmploiScreen()),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),),),);
  }
}


    /*  body: AppBackground(
          child: FutureBuilder<Map<String, String>>(
            future: _getUserInfo(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Erreur: ${snapshot.error}',
                    style: TextStyle(color: Colors.red),
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return Center(child: Text('Aucune donnée disponible'));
              }

              return LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 50, left: 15, right: 15),
                      child: Column(
                        children: [
                          THomeCategories(
                            iconData: Icons.book,
                            title: 'Cours',
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => TCoursScreen()),
                              );
                            },
                          ),
                          SizedBox(height: 60),
                          THomeCategories(
                            iconData: Icons.assignment,
                            title: 'Exercices',
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => TExerciceScreen()),
                              );
                            },
                          ),
                          SizedBox(height: 60),
                          THomeCategories(
                            iconData: Icons.remove_red_eye,
                            title: 'Observations',
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => TObservationsScreen()),
                              );

                            },
                          ),
    SizedBox(height: 60),
    THomeCategories(
    iconData: Icons.schedule,
    title: 'Emploi',
    onPressed: () {/*
    Navigator.push(
    context,
    MaterialPageRoute(builder: (context) => PEmploiScreen()),);*/},),

                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}*/
/*
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/commun/teacher_app/app_TnavBar.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:flutter/animation.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/observations/observation_TScreen.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'home_TCategorie.dart';
import 'package:NovaSchool/services/auth_service.dart';

class THomeScreen extends StatelessWidget {
  const THomeScreen({Key? key}) : super(key: key);

  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => true,
      child: Scaffold(
        backgroundColor: Color(0xFFF2F2F2),
        drawer: TeacherDrawer(),
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              centerTitle: true,
              iconTheme: IconThemeData(
                color: Colors.white, // 🔁 change la couleur ici selon ton besoin
                size: 30, // facultatif : ajuste la taille
              ),
              title: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),

                ],
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.person_outline, color: Colors.white,size: 30,),
                  onPressed: () async {
                    Map<String, String> userInfo = await _getUserInfo();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),
                    );
                  },
                ),
                /*IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  onPressed: () {},
                ),*/
              ],
            ),
          ),
        ),
        body: AppBackground(
          child: FutureBuilder<Map<String, String>>(
            future: _getUserInfo(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Erreur: ${snapshot.error}',
                    style: TextStyle(color: Colors.red),
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return Center(child: Text('Aucune donnée disponible'));
              }

              return LayoutBuilder(
                builder: (context, constraints) {
                  return SizedBox(
                    width: double.infinity,
                    height: constraints.maxHeight,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 50, left: 15, right: 15), // Fait monter les boutons
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start, // Commence en haut
                        children: [
                          SizedBox(height: 30),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: SizedBox(
                              width: double.infinity,
                              height: constraints.maxHeight * 0.15,
                              child: THomeCategories(
                                //iconimage: AppImages.cours,
                                iconData: Icons.book,
                                title: 'Cours',
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(builder: (context) => TCoursScreen()),
                                  );
                                },
                              ),
                            ),
                          ),
                          SizedBox(height: 100),
                          Padding(

                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: SizedBox(
                              width: double.infinity,
                              height: constraints.maxHeight * 0.15,
                              child: THomeCategories(
                                //iconimage: AppImages.exercice,
                                iconData: Icons.assignment,
                                title: 'Exercices',
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(builder: (context) => TExerciceScreen()),
                                  );
                                },
                              ),
                            ),
                          ),// Espace entre les boutons

                          SizedBox(height: 100), // Espace entre les boutons
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: SizedBox(
                              width: double.infinity,
                              height: constraints.maxHeight * 0.15,
                              child: THomeCategories(
                                //iconimage: AppImages.observation,
                                iconData: Icons.remove_red_eye,
                                title: 'Observations',
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => TObservationsScreen()),
                                  );
                                },
                              ),
                            ),
                          ),
                          SizedBox(height: 40),
                        ],
                      ),
                    ),
                  );

                },
              );
            },
          ),

        ),

      ),

      //bottomNavigationBar: (TeacherAppNavBar()),

    );
  }
}
*/
/*
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tscreen.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/cours/cours_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/exercice/exercice_TScreen.dart';
import 'package:NovaSchool/features/teacher_app/Home/pedagogicalElement/observations/observation_TScreen.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/services/auth_service.dart';

class THomeScreen extends StatelessWidget {
  const THomeScreen({Key? key}) : super(key: key);

  Future<Map<String, String>> _getUserInfo() async {
    final authService = AuthService();
    return await authService.getSenderDetails();
  }

  Widget _buildCategoryButton(
      BuildContext context, {
        required String image,
        required String title,
        required Widget screen,
      }) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(15),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => screen),
          );
        },
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 20, horizontal: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: Colors.white,
          ),
          child: Row(
            children: [
              Image.asset(
                image,
                width: 40,
                height: 40,
                fit: BoxFit.contain,
              ),
              SizedBox(width: 20),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => true,
      child: Scaffold(
        backgroundColor: Color(0xFFF2F2F2),
        drawer: TeacherDrawer(),
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              centerTitle: true,
              title: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.person_outline, color: Colors.black, size: 25),
                  onPressed: () async {
                    Map<String, String> userInfo = await _getUserInfo();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TProfilScreen(
                          firstName: userInfo['senderUsername'] ?? '',
                          lastName: userInfo['senderUserSurname'] ?? '',
                          email: userInfo['email'] ?? '',
                          profile: userInfo['profile'] ?? '',
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        body: AppBackground(
          child: FutureBuilder<Map<String, String>>(
            future: _getUserInfo(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                return Center(
                  child: Text(
                    'Erreur: ${snapshot.error}',
                    style: TextStyle(color: Colors.red),
                  ),
                );
              } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                return Center(child: Text('Aucune donnée disponible'));
              }

              return Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildCategoryButton(
                        context,
                        image: AppImages.exercice,
                        title: 'Exercice',
                        screen: TExerciceScreen(),
                      ),
                      SizedBox(height: 25),
                      _buildCategoryButton(
                        context,
                        image: AppImages.cours,
                        title: 'Cours',
                        screen: TCoursScreen(),
                      ),
                      SizedBox(height: 25),
                      _buildCategoryButton(
                        context,
                        image: AppImages.observation,
                        title: 'Observations',
                        screen: TObservationsScreen(),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),

      ),
    );
  }
}*/
