import 'package:NovaSchool/features/teacher_app/Home/T_appDrawer.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_TImage.dart';
import 'package:NovaSchool/features/teacher_app/Profil/profil_Tform.dart';

class TProfilScreen extends StatefulWidget {
  final String firstName;
  final String lastName;
  final String email;
  final String profile;

  const TProfilScreen(
      {Key? key, required this.firstName, required this.lastName, required this.email, required this.profile})
      : super(key: key);


  @override
  _TProfilScreenState createState() => _TProfilScreenState();
}
Future<Map<String, String>> _getUserInfo() async {
  final authService = AuthService();
  return await authService.getSenderDetails();
}

class _TProfilScreenState extends State<TProfilScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: TeacherDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            centerTitle: true,
            iconTheme: IconThemeData(
              color: Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            title: GestureDetector(
             /* onTap: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => THomeScreen()), // remplace THomeScreen par ton écran d'accueil
                );
              },*/
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/logos/ecocode.png',
                    height: 150,
                    color: Colors.white,
                  ),
                  SizedBox(width: 10),
                ],
              ),
            ),
            ///*
            ///Column(
            //               children: [
            //                 Image.asset(
            //                   'assets/logos/ecocode.png',
            //                   height: 50,
            //                   color: Colors.white,
            //                 ),
            //                 Text(
            //                   'Profil',
            //                   style: TextStyle(
            //                     fontSize: 14,
            //                     color: Colors.white70,
            //                     fontStyle: FontStyle.italic,
            //                   ),
            //                 ),
            //               ],
            //             ),
            ///
            /*Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/logos/ecocode.png',
                  height: 150,
                  color: Colors.white,
                ),
                SizedBox(width: 10),

              ],
            ),*/
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                icon: Icon(Icons.person_outline, color: Colors.white,size: 30,),
                onPressed: () async {
                  Map<String, String> userInfo = await _getUserInfo();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TProfilScreen(
                        firstName: userInfo['senderUsername'] ?? '',
                        lastName: userInfo['senderUserSurname'] ?? '',
                        email: userInfo['email'] ?? '',
                        profile: userInfo['profile'] ?? '',
                      ),
                    ),
                  );
                },
              ),*/

            ],
          ),
        ),
      ),
      resizeToAvoidBottomInset: false,
      body: AppBackground(
        child: Column(
          children: [
           // SecondHeadBar1(title: 'Profil',icon: Icons.person_outline,),
           /* PrimaryHeaderContainer(
              child: Container(
                height: 100, // Adjust the height here
                child: SecondHeadBar(
                  title: 'Profil',
                  titleColor: Colors.white,
                  iconColor: Colors.white,
                ),
              ),
            ),*/
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),),
              /*child: Text(
                '👤 Profil',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade800,
                ),
              ),
            ),*/
            SizedBox(height: 20,),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    TProfilImage(
                        firstName: widget.firstName,
                        lastName: widget
                            .lastName),
                    SizedBox(height: 10,),// Assurez-vous que ce widget est nécessaire
                    TProfilForm(
                      firstName: widget.firstName,
                      lastName: widget.lastName,
                      email: widget.email,
                      profile: widget.profile,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
