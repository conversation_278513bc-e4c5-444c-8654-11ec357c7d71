class HistoriquePaiementEleveDTO {
  final int? idHistoriquePaiementEleveEntity;
  final DateTime? datePaiementEleve;
  final double? montantPaiementEleve;
  final String? descriptionMontant;
  final DateTime? dateEncaissementCheque;
  final double? numeroCheque;
  final int? etatCheque;
  final String? typePaiement;

  HistoriquePaiementEleveDTO({
    this.idHistoriquePaiementEleveEntity,
    this.datePaiementEleve,
    this.montantPaiementEleve,
    this.descriptionMontant,
    this.dateEncaissementCheque,
    this.numeroCheque,
    this.etatCheque,
    this.typePaiement,
  });

  // Factory method to create an instance from a JSON map
  factory HistoriquePaiementEleveDTO.fromJson(Map<String, dynamic> json) {
    return HistoriquePaiementEleveDTO(
      idHistoriquePaiementEleveEntity: json['idHistoriquePaiementEleveEntity'],
      datePaiementEleve: json['datePaiementEleve'] != null ? DateTime.parse(json['datePaiementEleve']) : null,
      montantPaiementEleve: json['montantPaiementEleve'],
      descriptionMontant: json['descriptionMontant'],
      dateEncaissementCheque: json['dateEncaissementCheque'] != null ? DateTime.parse(json['dateEncaissementCheque']) : null,
      numeroCheque: json['numeroCheque'],
      etatCheque: json['etatCheque'],
      typePaiement: json['typePaiement'],
    );
  }

  // Method to convert an instance to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'idHistoriquePaiementEleveEntity': idHistoriquePaiementEleveEntity,
      'datePaiementEleve': datePaiementEleve?.toIso8601String(),
      'montantPaiementEleve': montantPaiementEleve,
      'descriptionMontant': descriptionMontant,
      'dateEncaissementCheque': dateEncaissementCheque?.toIso8601String(),
      'numeroCheque': numeroCheque,
      'etatCheque': etatCheque,
      'typePaiement': typePaiement,
    };
  }
}
