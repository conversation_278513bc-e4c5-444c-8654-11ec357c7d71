import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-teacher-classes',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="teacher-classes">
      <!-- Header -->
      <header class="page-header">
        <div class="header-content">
          <div class="header-left">
            <h1>🏫 My Classes</h1>
            <p>Manage your classes, students, and class activities</p>
          </div>
          <div class="header-actions">
            <button class="action-btn" (click)="showAddModal = true">
              <span class="icon">➕</span>
              Add Student
            </button>
            <button class="action-btn secondary" (click)="exportData()">
              <span class="icon">📊</span>
              Export Data
            </button>
          </div>
        </div>
      </header>

      <!-- Class Tabs -->
      <section class="class-tabs">
        <div class="tab-buttons">
          <button 
            class="tab-btn" 
            [class.active]="activeClass === class.id"
            (click)="setActiveClass(class.id)"
            *ngFor="let class of myClasses">
            {{class.subject}} - {{class.grade}}
            <span class="student-count">({{class.students.length}})</span>
          </button>
        </div>
      </section>

      <!-- Active Class Content -->
      <section class="class-content" *ngIf="getActiveClassData()">
        <div class="class-overview">
          <div class="class-info-card">
            <div class="class-header">
              <h2>{{getActiveClassData()?.subject}} - {{getActiveClassData()?.grade}}</h2>
              <div class="class-stats">
                <div class="stat">
                  <span class="stat-value">{{getActiveClassData()?.students.length}}</span>
                  <span class="stat-label">Students</span>
                </div>
                <div class="stat">
                  <span class="stat-value">{{getActiveClassData()?.averageGrade}}%</span>
                  <span class="stat-label">Avg Grade</span>
                </div>
                <div class="stat">
                  <span class="stat-value">{{getActiveClassData()?.attendance}}%</span>
                  <span class="stat-label">Attendance</span>
                </div>
              </div>
            </div>
            <div class="class-details">
              <div class="detail-item">
                <span class="label">Room:</span>
                <span class="value">{{getActiveClassData()?.room}}</span>
              </div>
              <div class="detail-item">
                <span class="label">Schedule:</span>
                <span class="value">{{getActiveClassData()?.schedule}}</span>
              </div>
              <div class="detail-item">
                <span class="label">Semester:</span>
                <span class="value">{{getActiveClassData()?.semester}}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Students List -->
        <div class="students-section">
          <div class="section-header">
            <h3>👥 Students ({{getActiveClassData()?.students.length}})</h3>
            <div class="search-filter">
              <input 
                type="text" 
                placeholder="Search students..." 
                [(ngModel)]="searchTerm"
                class="search-input">
              <select [(ngModel)]="filterStatus" class="filter-select">
                <option value="">All Students</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <div class="students-grid">
            <div class="student-card" *ngFor="let student of getFilteredStudents()">
              <div class="student-avatar">
                <img [src]="student.avatar" [alt]="student.name" *ngIf="student.avatar">
                <span *ngIf="!student.avatar">{{student.name.charAt(0)}}</span>
              </div>
              <div class="student-info">
                <h4>{{student.name}}</h4>
                <p>ID: {{student.id}}</p>
                <div class="student-stats">
                  <span class="grade-badge" [ngClass]="getGradeClass(student.currentGrade)">
                    {{student.currentGrade}}%
                  </span>
                  <span class="attendance-badge" [ngClass]="getAttendanceClass(student.attendance)">
                    {{student.attendance}}% attendance
                  </span>
                </div>
              </div>
              <div class="student-actions">
                <button class="action-btn small" (click)="viewStudent(student)">
                  <span class="icon">👁️</span>
                  View
                </button>
                <button class="action-btn small secondary" (click)="editStudent(student)">
                  <span class="icon">✏️</span>
                  Edit
                </button>
                <button class="action-btn small" (click)="contactParent(student)">
                  <span class="icon">📞</span>
                  Contact
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <h3>⚡ Quick Actions</h3>
          <div class="actions-grid">
            <button class="quick-action-btn" (click)="markAttendance()">
              <span class="action-icon">📋</span>
              <span class="action-text">Mark Attendance</span>
            </button>
            <button class="quick-action-btn" (click)="addGrades()">
              <span class="action-icon">📊</span>
              <span class="action-text">Add Grades</span>
            </button>
            <button class="quick-action-btn" (click)="createExercise()">
              <span class="action-icon">📝</span>
              <span class="action-text">Create Exercise</span>
            </button>
            <button class="quick-action-btn" (click)="sendMessage()">
              <span class="action-icon">💬</span>
              <span class="action-text">Send Message</span>
            </button>
          </div>
        </div>
      </section>

      <!-- Add Student Modal -->
      <div class="modal-overlay" *ngIf="showAddModal" (click)="showAddModal = false">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h2>➕ Add New Student</h2>
            <button class="close-btn" (click)="showAddModal = false">✕</button>
          </div>
          <form class="add-form">
            <div class="form-row">
              <div class="form-group">
                <label>Student Name</label>
                <input type="text" [(ngModel)]="newStudent.name" name="studentName" placeholder="Enter student name">
              </div>
              <div class="form-group">
                <label>Student ID</label>
                <input type="text" [(ngModel)]="newStudent.id" name="studentId" placeholder="Enter student ID">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>Email</label>
                <input type="email" [(ngModel)]="newStudent.email" name="studentEmail" placeholder="Enter email">
              </div>
              <div class="form-group">
                <label>Phone</label>
                <input type="tel" [(ngModel)]="newStudent.phone" name="studentPhone" placeholder="Enter phone">
              </div>
            </div>
            <div class="form-group">
              <label>Parent/Guardian Name</label>
              <input type="text" [(ngModel)]="newStudent.parentName" name="parentName" placeholder="Enter parent name">
            </div>
            <div class="modal-actions">
              <button type="button" class="cancel-btn" (click)="showAddModal = false">Cancel</button>
              <button type="button" class="save-btn" (click)="addStudent()">
                <span class="icon">💾</span>
                Add Student
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .teacher-classes {
      padding: 0;
      background: #f5f7fa;
      min-height: 100vh;
    }

    .page-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      margin: -30px -30px 30px -30px;
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-left h1 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
    }

    .header-left p {
      margin: 0;
      opacity: 0.9;
      font-size: 16px;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .action-btn.secondary {
      background: transparent;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .class-tabs {
      margin-bottom: 30px;
    }

    .tab-buttons {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      padding: 0 4px;
    }

    .tab-btn {
      background: white;
      border: none;
      padding: 12px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 500;
      color: #64748b;
      transition: all 0.3s ease;
      white-space: nowrap;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .tab-btn.active {
      background: #667eea;
      color: white;
    }

    .student-count {
      font-size: 12px;
      opacity: 0.8;
    }

    .class-content {
      display: grid;
      gap: 30px;
    }

    .class-info-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .class-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .class-header h2 {
      margin: 0;
      color: #2d3748;
      font-size: 24px;
    }

    .class-stats {
      display: flex;
      gap: 24px;
    }

    .stat {
      text-align: center;
    }

    .stat-value {
      display: block;
      font-size: 24px;
      font-weight: 700;
      color: #667eea;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #718096;
    }

    .class-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
    }

    .detail-item .label {
      color: #718096;
      font-weight: 500;
    }

    .detail-item .value {
      color: #2d3748;
      font-weight: 600;
    }

    .students-section {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .section-header h3 {
      margin: 0;
      color: #2d3748;
      font-size: 20px;
    }

    .search-filter {
      display: flex;
      gap: 12px;
    }

    .search-input,
    .filter-select {
      padding: 8px 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .students-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }

    .student-card {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      transition: transform 0.3s ease;
    }

    .student-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .student-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 18px;
      margin-bottom: 12px;
    }

    .student-avatar img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }

    .student-info h4 {
      margin: 0 0 4px 0;
      color: #2d3748;
      font-size: 16px;
    }

    .student-info p {
      margin: 0 0 12px 0;
      color: #718096;
      font-size: 14px;
    }

    .student-stats {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }

    .grade-badge,
    .attendance-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
    }

    .grade-badge.excellent { background: #d4edda; color: #155724; }
    .grade-badge.good { background: #cce5ff; color: #004085; }
    .grade-badge.average { background: #fff3cd; color: #856404; }
    .grade-badge.poor { background: #f8d7da; color: #721c24; }

    .attendance-badge.high { background: #d4edda; color: #155724; }
    .attendance-badge.medium { background: #fff3cd; color: #856404; }
    .attendance-badge.low { background: #f8d7da; color: #721c24; }

    .student-actions {
      display: flex;
      gap: 8px;
    }

    .action-btn.small {
      padding: 6px 12px;
      font-size: 12px;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
    }

    .action-btn.small.secondary {
      background: #6c757d;
    }

    .quick-actions {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .quick-actions h3 {
      margin: 0 0 20px 0;
      color: #2d3748;
      font-size: 20px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .quick-action-btn {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      padding: 16px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }

    .quick-action-btn:hover {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }

    .action-icon {
      font-size: 24px;
    }

    .action-text {
      font-weight: 500;
      font-size: 14px;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      border-bottom: 1px solid #e2e8f0;
    }

    .modal-header h2 {
      margin: 0;
      color: #2d3748;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #718096;
    }

    .add-form {
      padding: 24px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      margin-bottom: 8px;
      font-weight: 500;
      color: #2d3748;
    }

    .form-group input {
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 6px;
      font-size: 14px;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }

    .cancel-btn {
      background: #6c757d;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
    }

    .save-btn {
      background: #667eea;
      border: none;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .class-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }

      .class-stats {
        justify-content: center;
      }

      .section-header {
        flex-direction: column;
        gap: 16px;
      }

      .students-grid {
        grid-template-columns: 1fr;
      }

      .form-row {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class TeacherClassesComponent {
  activeClass = 1;
  showAddModal = false;
  searchTerm = '';
  filterStatus = '';

  newStudent = {
    name: '',
    id: '',
    email: '',
    phone: '',
    parentName: ''
  };

  myClasses = [
    {
      id: 1,
      subject: 'Mathematics',
      grade: 'Grade 5A',
      room: 'Room 101',
      schedule: 'Mon, Wed, Fri 9:00 AM',
      semester: 'Fall 2024',
      averageGrade: 85,
      attendance: 92,
      students: [
        { id: 'S001', name: 'John Smith', currentGrade: 88, attendance: 95, email: '<EMAIL>', phone: '************', parentName: 'Robert Smith', avatar: null },
        { id: 'S002', name: 'Emma Johnson', currentGrade: 92, attendance: 98, email: '<EMAIL>', phone: '************', parentName: 'Lisa Johnson', avatar: null },
        { id: 'S003', name: 'Michael Brown', currentGrade: 78, attendance: 85, email: '<EMAIL>', phone: '************', parentName: 'David Brown', avatar: null }
      ]
    },
    {
      id: 2,
      subject: 'Algebra',
      grade: 'Grade 5B',
      room: 'Room 101',
      schedule: 'Tue, Thu 10:30 AM',
      semester: 'Fall 2024',
      averageGrade: 78,
      attendance: 88,
      students: [
        { id: 'S004', name: 'Sarah Davis', currentGrade: 85, attendance: 90, email: '<EMAIL>', phone: '************', parentName: 'Jennifer Davis', avatar: null },
        { id: 'S005', name: 'Alex Wilson', currentGrade: 72, attendance: 82, email: '<EMAIL>', phone: '************', parentName: 'Mark Wilson', avatar: null }
      ]
    }
  ];

  constructor(private router: Router) {}

  setActiveClass(classId: number): void {
    this.activeClass = classId;
  }

  getActiveClassData() {
    return this.myClasses.find(c => c.id === this.activeClass);
  }

  getFilteredStudents() {
    const activeClass = this.getActiveClassData();
    if (!activeClass) return [];

    let filtered = activeClass.students;

    if (this.searchTerm) {
      filtered = filtered.filter(student => 
        student.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        student.id.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    return filtered;
  }

  getGradeClass(grade: number): string {
    if (grade >= 90) return 'excellent';
    if (grade >= 80) return 'good';
    if (grade >= 70) return 'average';
    return 'poor';
  }

  getAttendanceClass(attendance: number): string {
    if (attendance >= 90) return 'high';
    if (attendance >= 75) return 'medium';
    return 'low';
  }

  viewStudent(student: any): void {
    console.log('View student:', student);
  }

  editStudent(student: any): void {
    console.log('Edit student:', student);
  }

  contactParent(student: any): void {
    console.log('Contact parent:', student);
    this.router.navigate(['/teacher/messages'], { queryParams: { contact: student.parentName } });
  }

  markAttendance(): void {
    this.router.navigate(['/teacher/attendance'], { queryParams: { class: this.activeClass } });
  }

  addGrades(): void {
    this.router.navigate(['/teacher/grades'], { queryParams: { class: this.activeClass } });
  }

  createExercise(): void {
    this.router.navigate(['/teacher/exercises'], { queryParams: { class: this.activeClass } });
  }

  sendMessage(): void {
    this.router.navigate(['/teacher/messages'], { queryParams: { class: this.activeClass } });
  }

  addStudent(): void {
    if (this.newStudent.name && this.newStudent.id) {
      const activeClass = this.getActiveClassData();
      if (activeClass) {
        activeClass.students.push({
          ...this.newStudent,
          currentGrade: 0,
          attendance: 100,
          avatar: null
        });
        this.showAddModal = false;
        this.newStudent = { name: '', id: '', email: '', phone: '', parentName: '' };
      }
    }
  }

  exportData(): void {
    console.log('Export class data');
  }
}
