import 'dart:convert';

class Message {
  final int? id;
  final int? senderId;
  final int? recipientId;
  final String content;
  final String message;

  Message({
    this.id,
    this.senderId,
    this.recipientId,
    required this.content,
    required this.message,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'],
      senderId: json['senderId'] ?? 0,
      recipientId: json['recipientId'] ?? 0,
      content: json['content'] ?? '',
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'recipientId': recipientId,
      'content': content,
      'message': message,
    };
  }
}
