import 'dart:convert';
import 'dart:typed_data';

import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:NovaSchool/utils/constants/text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

class PHomeWelcome extends StatelessWidget {
  const PHomeWelcome({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ValueNotifier<ChildProfile?> selectedProfileNotifier =
    Get.find<ValueNotifier<ChildProfile?>>();
    final PNavigationController navigationController =
    Get.find<PNavigationController>();

    return Padding(
      padding: const EdgeInsets.only(top: 30, left: 20, right: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 🟢 Image de profil avec un effet premium
          Obx(() {
            final selectedProfile = navigationController.selectedProfile.value;
            final String? profileImageBase64 = selectedProfile?.profileImage;
            Uint8List? profileImageBytes;
            if (profileImageBase64 != null && profileImageBase64.isNotEmpty) {
              profileImageBytes = base64Decode(profileImageBase64);
            }

            return AnimatedContainer(
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              padding: EdgeInsets.all(2),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    spreadRadius: 2,
                    blurRadius: 6,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 37,
                backgroundColor: Colors.white,
                backgroundImage: profileImageBytes != null
                    ? MemoryImage(profileImageBytes)
                    : AssetImage(AppImages.imageLogo) as ImageProvider,
              ),
            );
          }),

          SizedBox(width: 10),
          // 🟢 Texte de bienvenue modernisé
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppText.Hometitle,
                style: GoogleFonts.dmSerifDisplay(
                  fontSize: 23,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              Obx(() {
                final selectedProfile =
                    navigationController.selectedProfile.value;
                final studentName =
                '${selectedProfile?.firstName ?? ''} ${selectedProfile?.name ?? ''}'
                    .trim();
                return AnimatedSwitcher(
                  duration: Duration(milliseconds: 300),
                  child: Text(
                    studentName.isNotEmpty ? studentName : "Élève inconnu",
                    key: ValueKey(studentName),
                    style: GoogleFonts.dmSerifDisplay(
                      fontSize: 25,
                      fontStyle: FontStyle.italic, // 🔥 Ajout de l'italique

                      fontWeight: FontWeight.w800,
                      color: Colors.orange.shade200//Color(0xFFE3A876),//peche clair Color(0xFFFFDAB9)//orange.shade200
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              }),
            ],
          ),
          Spacer(),


        ],
      ),
    );
  }
}
/***


import 'dart:convert';
import 'dart:typed_data';

import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:NovaSchool/commun/parent_app/app_PheadBar.dart';
import 'package:NovaSchool/utils/constants/text.dart';
import 'package:NovaSchool/utils/constants/images.dart';

class PHomeWelcome extends StatelessWidget {
  const PHomeWelcome({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ValueNotifier<ChildProfile?> selectedProfileNotifier =
        Get.find<ValueNotifier<ChildProfile?>>();

        final PNavigationController navigationController =
        Get.find<PNavigationController>();

    return Padding(
      padding: const EdgeInsets.only(
        top: 30,
        left: 20,
        right: 20,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppText.Hometitle,
                style: GoogleFonts.dmSerifDisplay(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
              Obx(() {
                final selectedProfile = navigationController.selectedProfile.value;
                final studentName = '${selectedProfile?.firstName ?? ''} ${selectedProfile?.name ?? ''}';
                return Text(
                  studentName,
                  style: GoogleFonts.dmSerifDisplay(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                );
              }),
            ],
          ),
          Spacer(),
          Obx(() {
            final selectedProfile = navigationController.selectedProfile.value;
            final String? profileImageBase64 = selectedProfile?.profileImage;
            Uint8List? profileImageBytes;
            if (profileImageBase64 != null && profileImageBase64.isNotEmpty) {
              profileImageBytes = base64Decode(profileImageBase64);
            }
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.0),
                  color: Color(0xFF4099FF),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.5),
                      spreadRadius: 0,
                      blurRadius: 5,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: profileImageBytes != null
                      ? Image.memory(
                          profileImageBytes,
                          fit: BoxFit.cover,
                        )
                      : Image.asset(
                          AppImages.imageLogo,
                          fit: BoxFit.cover,
                        ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
*/