import 'dart:convert';
import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/models/soldeEleve.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/tokenInterceptor.dart';
import 'package:http_interceptor/http_interceptor.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class SoldeEleveService {

  late final String baseURL;
  late final String baseUrl;
  final auth = AuthService();
  final httpClient =
      InterceptedClient.build(interceptors: [TokenInterceptor()]);

  SoldeEleveService() {
    baseURL = dotenv.get('baseURL');
    baseUrl = "$baseURL/solde-eleve";
  }


  // String baseURL = dotenv.get('baseURL');
  // final String baseUrl = "$baseURL/solde-eleve";
  // final auth = AuthService();
  // final httpClient =
  //     InterceptedClient.build(interceptors: [TokenInterceptor()]);
  Future<SoldeEleve?> getSoldeByEleveId(int eleveId) async {

    // String baseURL = dotenv.get('baseURL');
    // final baseUrl = "$baseURL/solde-eleve";

  // String baseURL = dotenv.get('baseURL');
  // final String baseUrl = "$baseURL/solde-eleve";
  // final auth = AuthService();
  // final httpClient =
  //     InterceptedClient.build(interceptors: [TokenInterceptor()]);

    final url = Uri.parse('$baseUrl/eleve/$eleveId');

    try {
      final response = await httpClient.get(url);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return SoldeEleve.fromJson(jsonData);
      } else {
        print('Failed to load solde: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error fetching solde: $e');
      return null;
    }
  }
}
