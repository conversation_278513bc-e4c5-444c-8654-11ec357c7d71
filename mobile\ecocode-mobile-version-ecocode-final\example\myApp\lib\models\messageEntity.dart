class MessageEntity { 
  final int idMessage;
  final String senderId;
  final String senderUsername;
  final String senderUserSurname;
  final String type;
  final String objet;
  final String body;
  final bool readStatus;
  final bool confirmed;
  final bool existFile;
  final int numberOfFile;
  final DateTime dateEnvoie;
  final DateTime? dateCorrectionExercice;

  MessageEntity({
    required this.idMessage,
    required this.senderId,
    required this.senderUsername,
    required this.senderUserSurname,
    required this.type,
    required this.objet,
    required this.body,
    required this.readStatus,
    required this.confirmed,
    required this.existFile,
    required this.numberOfFile,
    required this.dateEnvoie,
    this.dateCorrectionExercice,
  });

  factory MessageEntity.fromJson(Map<String, dynamic> json) {
    return MessageEntity(
      idMessage: json['idMessage'],
      senderId: json['senderId'],
      senderUsername: json['senderUsername'],
      senderUserSurname: json['senderUserSurname'],
      type: json['type'],
      objet: json['objet'],
      body: json['body'],
      readStatus: json['readStatus'],
      confirmed: json['confirmed'],
      existFile: json['existFile'],
      numberOfFile: json['numberOfFile'] is int  
          ? json['numberOfFile']
          : (json['numberOfFile'] as num).toInt(),
      dateEnvoie: DateTime.parse(json['dateEnvoie']),
      dateCorrectionExercice: json['dateCorrectionExercice'] != null
        ? DateTime.parse(json['dateCorrectionExercice'])
        : null,
    );
  }
}


