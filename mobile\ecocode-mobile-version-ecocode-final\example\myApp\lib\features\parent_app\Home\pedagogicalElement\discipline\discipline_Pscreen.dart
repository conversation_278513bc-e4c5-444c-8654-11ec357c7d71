import 'package:NovaSchool/commun/app_background.dart';
import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/commun/parent_app/app_secondheadBar.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:NovaSchool/models/disciplineEntity.dart';
import 'package:NovaSchool/models/messageResponse.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/discipline_service.dart';
import 'package:NovaSchool/services/message_services.dart';
import 'package:NovaSchool/utils/constants/images.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
class PDisciplineScreen extends StatefulWidget {
  final Function(String) onMessageRead;
  const PDisciplineScreen({Key? key, required this.onMessageRead})
      : super(key: key);

  @override
  _PDisciplineScreenState createState() => _PDisciplineScreenState();
}

class _PDisciplineScreenState extends State<PDisciplineScreen> {
  List<DisciplineEntity> disciplines = [];
  bool _isLoading = true;
  int _itemsPerPage = 0;
  int _currentPageIndex = 0;
  final int _pageSize = 5;
  int _totalPages = 0;
  ScrollController _scrollController = ScrollController();
  final AuthService auth = AuthService();
  final DisciplineServices disciplineService = DisciplineServices();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateItemsPerPage();
    });
    _fetchDisciplines();
  }

  Future<void> _fetchDisciplines() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final eleveId = await auth.getEleveId();
      final allDisciplines = await disciplineService.fetchDisciplinesByEleveId(
        eleveId: eleveId,
      );

      if (mounted) {
        setState(() {
          disciplines = allDisciplines;
          _totalPages = (disciplines.length / _pageSize).ceil();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching disciplines: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _calculateItemsPerPage() {
    final itemHeight = 80.0;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = AppBar().preferredSize.height;
    final padding = 24.0;
    final availableHeight = screenHeight - appBarHeight - padding;

    setState(() {
      _itemsPerPage = (availableHeight / itemHeight).floor();
    });
  }

  void _updatePageIndex(int newIndex) {
    if (newIndex >= 0 && newIndex < _totalPages) {
      setState(() {
        _currentPageIndex = newIndex;
        _isLoading = true; // Set loading to true before fetching new page
      });
      // _fetchDisciplines(page: newIndex);
    }
  }

  @override
  Widget build(BuildContext context) {
    List<DisciplineEntity> currentPageDisciplines = disciplines.isEmpty
        ? []
        : disciplines
            // .skip(_currentPageIndex * _pageSize)
            // .take(_pageSize)
            .toList();

    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<PNavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => ParentAppNavBar());
              },
              child: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      body: AppBackground(
        child: Column(
          children: [
            // Conteneur haut avec bouton retour + image sur la même ligne
            Padding(
              padding: EdgeInsets.all(15.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Tooltip(
                    message: 'Retour',
                    child: IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Icons.arrow_back_ios, color: Colors.blueGrey),
                    ),
                  ),
                  SizedBox(width: 35), // Espacement entre l'icône et l'image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10.0),
                    child: Image.asset(
                      AppImages.discipline ,
                      width: 200,
                      height: 100,
                      fit: BoxFit.contain,
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: _isLoading
                  ? Center(child: CircularProgressIndicator())
                  : Container(
                      height: 510,
                      child: disciplines.isEmpty
                          ? Center(
                              child: Text(
                                "Aucune discipline trouvée.",
                                style:
                                    TextStyle(fontSize: 18, color: Colors.grey),
                              ),
                            )
                          : RefreshIndicator(
                              onRefresh: _fetchDisciplines,
                              child: ListView(
                                padding: EdgeInsets.all(10.0),
                                children: currentPageDisciplines
                                    .map((discipline) => Card(
                                          elevation: 4,
                                          margin: EdgeInsets.symmetric(
                                              vertical: 8.0),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12.0),
                                          ),
                                          child: ExpansionTile(
                                            tilePadding: EdgeInsets.symmetric(
                                                horizontal: 16.0,
                                                vertical: 8.0),
                                            title: Text(
                                              discipline.typeDiscipline,
                                              style: TextStyle(
                                                fontSize: 18.0,
                                                fontWeight: FontWeight.bold,
                                                color: Color(0xFF4099FF),
                                              ),
                                            ),
                                            subtitle: Text(
                                              "Date: ${discipline.dateDiscipline.toLocal().toString().split(' ')[0]}",
                                              style: TextStyle(
                                                  color: Colors.grey[600]),
                                            ),
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 16.0,
                                                        vertical: 8.0),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    // Motif at the top of the expanded section
                                                    Text(
                                                      "Motif: ${discipline.motifDiscipline}",
                                                      style: TextStyle(
                                                        fontSize: 16.0,
                                                        color: Colors.grey[800],
                                                      ),
                                                    ),
                                                    // Any additional content can go below if needed
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ))
                                    .toList(),
                              ),
                            ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
