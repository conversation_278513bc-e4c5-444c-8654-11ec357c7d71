class Observation {
  final String id;
  final String titre;
  final DateTime date;
  final String description;
  final String fichier;
  final String niveau;
  final String classe;
  final String eleve;
  final bool toutesLesClasses;

  Observation({
    required this.id,
    required this.titre,
    required this.date,
    required this.description,
    required this.fichier,
    required this.niveau,
    required this.classe,
    required this.eleve,
    required this.toutesLesClasses,
  });

  factory Observation.fromJson(Map<String, dynamic> json) {
    return Observation(
      id: json['id'],
      titre: json['titre'],
      date: DateTime.parse(json['date']),
      description: json['description'],
      fichier: json['fichier'],
      niveau: json['niveau'],
      classe: json['classe'],
      eleve: json['eleve'],
      toutesLesClasses: json['toutesLesClasses'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'titre': titre,
      'date': date.toIso8601String(),
      'description': description,
      'fichier': fichier,
      'niveau': niveau,
      'classe': classe,
      'eleve': eleve,
      'toutesLesClasses': toutesLesClasses,
    };
  }
}
