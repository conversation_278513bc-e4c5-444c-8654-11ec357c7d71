class GetNumberOfNotRead {
  final int notReadCour;
  final int notReadObservation;
  final int notReadExercice;
  final int notReadDiscipline;

  GetNumberOfNotRead({
    required this.notReadCour,
    required this.notReadObservation,
    required this.notReadExercice,
    required this.notReadDiscipline,
  });

  // Factory constructor to create an object from a JSON map
  factory GetNumberOfNotRead.fromJson(Map<String, dynamic> json) {
    return GetNumberOfNotRead(
      notReadCour: json['notReadCour'] ?? 0,
      notReadObservation: json['notReadObservation'] ?? 0,
      notReadExercice: json['notReadExercixe'] ?? 0,
      notReadDiscipline: json['notReadDiscipline'] ?? 0,
    );
  }
}
