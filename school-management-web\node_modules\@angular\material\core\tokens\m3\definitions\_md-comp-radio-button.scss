//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-state';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-state': md-sys-state.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'disabled-selected-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-selected-icon-opacity': if($exclude-hardcoded-values, null, 0.38),
    'disabled-unselected-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-unselected-icon-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'icon-size': if($exclude-hardcoded-values, null, 20px),
    'selected-focus-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'selected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'selected-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'selected-hover-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'selected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'selected-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'selected-pressed-icon-color': map.get($deps, 'md-sys-color', 'primary'),
    'selected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'selected-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'state-layer-size': if($exclude-hardcoded-values, null, 40px),
    'unselected-focus-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'unselected-hover-icon-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'unselected-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-pressed-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'unselected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'unselected-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity')
  );
}
