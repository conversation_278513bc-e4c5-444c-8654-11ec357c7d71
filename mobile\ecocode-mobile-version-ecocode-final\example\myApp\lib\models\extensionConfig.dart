import 'dart:convert';

class ExtensionConfig {
  final int? id;
  final String message;
  final String path;
  final DateTime? creationDate;

  ExtensionConfig({
    this.id,
    required this.message,
    required this.path,
    this.creationDate,
  });

  // Factory method to parse JSON from API response
  factory ExtensionConfig.fromJson(Map<String, dynamic> json) {
    return ExtensionConfig(
      id: json['id'],
      message: json['message'],
      path: json['path'],
      creationDate: json['creationDate'] != null
          ? DateTime.parse(json['creationDate'])
          : null,
    );
  }

  // Convert object to JSON for API requests
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message': message,
      'path': path,
      'creationDate': creationDate?.toIso8601String(),
    };
  }
}
