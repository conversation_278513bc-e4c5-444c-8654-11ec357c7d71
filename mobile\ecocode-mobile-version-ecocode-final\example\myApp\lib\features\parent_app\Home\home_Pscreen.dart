import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/chat/test.dart';
import '../../../../commun/app_background.dart';
import '../../../../commun/parent_app/app_PheadBar.dart';
import '../../../chatbot/chatbot_screen.dart';
import 'pedagogicalElement/home_PpedagogicalElement.dart';
import 'home_Pwelcome.dart';

class PHomeScreen extends StatelessWidget {
  const PHomeScreen({Key? key}) : super(key: key);

  // 🎨 Palette de couleurs modernes et raffinées
  static const Color primaryColor = Color(0xFF4099FF); // Bleu profond raffiné
  static const Color secondaryColor = Color(0xFFF1F1F1); // Blanc cassé élégant
  static const Color backgroundColor = Color(0xFFF2F2F2); // Gris clair doux
  static const Color textColor = Color(0xFF2D3A45); // Texte plus foncé

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Handle back press to exit app
        return true; // Let the system handle back navigation (exit app)
      },
      child: Scaffold(
        backgroundColor: Color(0xFFF2F2F2),
        drawer: AppDrawer(),
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: Container(
            //
            width: double.infinity,

            ///
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: AppBar(
              iconTheme: IconThemeData(
                color:
                    Colors.white, // 🔁 change la couleur ici selon ton besoin
                size: 30, // facultatif : ajuste la taille
              ),
              backgroundColor: Colors.transparent,
              // Pour afficher le gradient
              elevation: 0,
              centerTitle: true,
              // ✅ Assure le centrage du titre
              title: Image.asset(
                'assets/logos/ecocode.png', // Remplace avec ton chemin
                height: 150, // Ajuste la taille
                color: Colors
                    .white, // Applique du blanc au logo (si PNG avec transparence)
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.logout, color: Colors.white),
                  //title: Text('Déconnexion'),
                  onPressed: () async {
                    await AuthService.logoutKeycloak();
                  },
                ),
                /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
              ],
            ),
          ),
        ),
        body: AppBackground(
          child: Column(
            children: [
              /*
      PrimaryHeaderContainer(
      child: Column(
      /*children: [
      PAppHeadBar(),
      PHomeWelcome(),
      SizedBox(height: 70),
      ],*/
      ),
      ),*/

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      PHomePedagogicalElement(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
/****

    class PHomeScreen extends StatelessWidget {
    const PHomeScreen({Key? key}) : super(key: key);

    // 🎨 Palette de couleurs modernes et raffinées
    static const Color primaryColor = Color(0xFF4099FF); // Bleu profond raffiné
    static const Color secondaryColor = Color(0xFFF1F1F1); // Blanc cassé élégant
    static const Color backgroundColor = Color(0xFFF2F2F2); // Gris clair doux
    static const Color textColor = Color(0xFF2D3A45); // Texte plus foncé

    @override
    Widget build(BuildContext context) {
    return WillPopScope(
    onWillPop: () async {
    // Handle back press to exit app
    return true; // Let the system handle back navigation (exit app)
    },
    child: Scaffold(

    body: AppBackground(
    child: Column(
    children: [
    PrimaryHeaderContainer(
    child: Column(
    children: [
    PAppHeadBar(),
    PHomeWelcome(),
    SizedBox(height: 70),
    ],
    ),
    ),
    Expanded(
    child: SingleChildScrollView(
    child: Column(
    children: [
    PHomePedagogicalElement(),
    ],
    ),
    ),
    ),
    ],
    ),
    ),
    ),
    );
    }
    }

 */
/*

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => true,

      child: Scaffold(
        backgroundColor: backgroundColor,
        /* drawer:  Drawer(
        backgroundColor: primaryColor, // Remplace par la couleur souhaitée
        child: AppDrawer(),
      ),*/

        appBar: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: Container(
            //
            width: double.infinity,

            ///
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              // Pour afficher le gradient
              elevation: 0,
              centerTitle: true,
              // ✅ Assure le centrage du titre
              title: Image.asset(
                'assets/logos/ecocode.png', // Remplace avec ton chemin
                height: 50, // Ajuste la taille
                color: Colors
                    .white, // Applique du blanc au logo (si PNG avec transparence)
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.notifications, color: Colors.black),
                  // ✅ Ajout de l'icône de chat
                  onPressed: () {
                    //Navigator.push(
                    //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                  },
                ),
              ],
            ),
          ),
        ),

        body: SafeArea(
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(), // Effet de défilement fluide
            child: Column(
              children: [
                // Utilisation de `const` pour éviter des reconstructions inutiles
                PHomePedagogicalElement(),
                //SizedBox(height: 0),
                ParentAppNavBar(),
              ],
            ),
          ),
        ),

      ),

    );
  }
}*/
