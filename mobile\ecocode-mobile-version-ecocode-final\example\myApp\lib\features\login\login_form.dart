import 'package:NovaSchool/commun/admin_app/admin_navbar.dart';
import 'package:flutter/material.dart';
//import 'package:NovaSchool/commun/base_url.dart';
import 'package:NovaSchool/features/teacher_app/Home/home_Tscreen.dart';
import 'package:NovaSchool/services/auth_service.dart';
import '../../utils/constants/text.dart';
import '../../utils/constants/colors.dart';
import '../../commun/parent_app/app_PnavBar.dart';
import '../../commun/teacher_app/app_TnavBar.dart';
import '../admin/home/<USER>';
import 'login_services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:NovaSchool/services/eleve_services.dart';
class LoginForm extends StatefulWidget {
  final GlobalKey<FormState> formKey;

  LoginForm({Key? key, required this.formKey}) : super(key: key);

  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final AuthService _loginService = AuthService();

  bool isPasswordVisible = false;
  bool isUsernameEmpty = false;
  bool isPasswordEmpty = false;
   final _tuteurService = EleveServices();

  // Messages d'erreur
  final String usernameErrorMessage =
      'Le nom d\'utilisateur ne peut pas être vide';
  final String passwordErrorMessage = 'Le mot de passe ne peut pas être vide';
  final String loginFailedMessage =
      'Échec de connexion. Nom d\'utilisateur ou mot de passe incorrect.';

  Future<void> _login() async {
    // Reset error states
    setState(() {
      isUsernameEmpty = usernameController.text.isEmpty;
      isPasswordEmpty = passwordController.text.isEmpty;
    });

    // Check if fields are not emptyne
    if (!isUsernameEmpty && !isPasswordEmpty) {
      // Validate form
      if (widget.formKey.currentState!.validate()) {
        try {
          // Attempt login
          await _loginService.login(
              usernameController.text, passwordController.text);

          if (await _loginService.isAuthenticated() == false) {
            _showErrorDialog(
                'An error occurred while logging. Please try again later.');
          } else {
            await _tuteurService.updateFirebaseToken(); 
            if (await _loginService.isAdmin() == true) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => AdminAppNavBar()),
              );
            } else if (await _loginService.isParent() == true) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => ParentAppNavBar()),
              );
            } else if (await _loginService.isEnseiganat() == true) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => TeacherAppNavBar()),
              );
            } else {
              _showErrorDialog('Rôle utilisateur inconnu ou non autorisé.');
            }
          }
        } catch (e) {
          // Display error dialog for failed login attempt
          _showErrorDialog(loginFailedMessage);
        }
      }
    }
  }

  void _showErrorDialog(String message) {
    if (!isUsernameEmpty && !isPasswordEmpty) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Erreur'),
            content: Text(message),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text('OK'),
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> _launchForgotPasswordURL() async {
    
    String forgotPasswordUrl = dotenv.get('forgetPasswordURL');
    if (await canLaunch(forgotPasswordUrl)) {
      await launch(forgotPasswordUrl);
    } else {
      _showErrorDialog('Could not launch the password recovery page.');
    }
  }

  void _showSuccessDialog(String message, String route) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Success'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushReplacementNamed(context, route);
              },
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  // Fonction pour réinitialiser les états d'erreur lors de la modification du nom d'utilisateur
  void _resetUsernameError() {
    if (isUsernameEmpty) {
      setState(() {
        isUsernameEmpty = false;
      });
    }
  }

  // Fonction pour réinitialiser les états d'erreur lors de la modification du mot de passe
  void _resetPasswordError() {
    if (isPasswordEmpty) {
      setState(() {
        isPasswordEmpty = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      autovalidateMode: AutovalidateMode.disabled,
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Username field
            TextFormField(
              controller: usernameController,
              onChanged: (value) {
                _resetUsernameError(); // Réinitialiser l'état d'erreur du nom d'utilisateur
              },
              decoration: InputDecoration(
                labelText: AppText.Username,
                labelStyle: TextStyle(color: Colors.black54),
                prefixIcon: Padding(
                  padding: EdgeInsets.all(5.0),
                  child: Icon(Icons.person, color: Color(0xFF4099FF)),
                ),
                errorText: isUsernameEmpty ? usernameErrorMessage : null,
                focusedErrorBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.red),
                ),
              ),
            ),
            SizedBox(height: 20),
            // Password field
            TextFormField(
              controller: passwordController,
              onChanged: (value) {
                _resetPasswordError(); // Réinitialiser l'état d'erreur du mot de passe
              },
              obscureText: !isPasswordVisible,
              decoration: InputDecoration(
                labelText: AppText.Password,
                labelStyle: TextStyle(color: Colors.black54),
                prefixIcon: Padding(
                  padding: EdgeInsets.all(5.0),
                  child: Icon(Icons.lock_rounded, color: Color(0xFF4099FF)),
                ),
                suffixIcon: IconButton(
                  icon: Icon(
                    isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                    color: Color(0xFF4099FF),
                  ),
                  onPressed: () {
                    setState(() {
                      isPasswordVisible = !isPasswordVisible;
                    });
                  },
                ),
                errorText: isPasswordEmpty ? passwordErrorMessage : null,
                focusedErrorBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.red),
                ),
              ),
            ),
            SizedBox(height: 20),
            /*SizedBox(
              width: double.infinity,
              height: 60,
              child: ElevatedButton(
                onPressed: _login,

                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: Text(AppText.LoginButton),
              ),
            ),*/
            SizedBox(
              width: double.infinity,
              height: 60,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: TextButton(
                  onPressed: _login,
                  style: TextButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    minimumSize: Size(double.infinity, 60),
                  ),
                  child: Text(
                    AppText.LoginButton,
                    style: TextStyle(color: Colors.white,fontSize: 18), // Texte en blanc pour meilleur contraste
                  ),
                ),
              ),
            ),
            SizedBox(height: 10),
            Center(
              child: TextButton(
                onPressed: _launchForgotPasswordURL,
                child: Text(
                  'Forgot Password?',
                  style: TextStyle(color: Colors.blue),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}



// import 'package:flutter/material.dart';
// import '../../utils/constants/text.dart';
// import '../../utils/constants/colors.dart';
// import '../../commun/parent_app/app_PnavBar.dart';
// import '../../commun/teacher_app/app_TnavBar.dart';
// import '../admin/home/<USER>';
// import 'login_services.dart';

// class LoginForm extends StatefulWidget {
//   final GlobalKey<FormState> formKey;

//   LoginForm({Key? key, required this.formKey}) : super(key: key);

//   @override
//   _LoginFormState createState() => _LoginFormState();
// }

// class _LoginFormState extends State<LoginForm> {
//   final TextEditingController usernameController = TextEditingController();
//   final TextEditingController passwordController = TextEditingController();

//   final LoginService _loginService = LoginService();

//   bool isPasswordVisible = false;
//   bool isUsernameEmpty = false;
//   bool isPasswordEmpty = false;

//   // Messages d'erreur
//   final String usernameErrorMessage = 'Le nom d\'utilisateur ne peut pas être vide';
//   final String passwordErrorMessage = 'Le mot de passe ne peut pas être vide';
//   final String loginFailedMessage = 'Échec de connexion. Nom d\'utilisateur ou mot de passe incorrect.';

//   Future<void> _login() async {
//     // Reset error states
//     setState(() {
//       isUsernameEmpty = usernameController.text.isEmpty;
//       isPasswordEmpty = passwordController.text.isEmpty;
//     });

//     // Check if fields are not empty
//     if (!isUsernameEmpty && !isPasswordEmpty) {
//       // Validate form
//       if (widget.formKey.currentState!.validate()) {
//         try {
//           // Attempt login
//           Map<String, dynamic> loginResult = await _loginService.login(usernameController.text, passwordController.text);
//           String role = loginResult['role'] as String;

//           // Redirect based on role
//           if (role == "ADMIN") {
//             Navigator.pushReplacement(
//               context,
//               MaterialPageRoute(builder: (context) => AHomeScreen()),
//             );
//           } else if (role == "ENSEIGNANT") {
//             Navigator.pushReplacement(
//               context,
//               MaterialPageRoute(builder: (context) => TAppNavBar()),
//             );
//           } else if (role == "PARENT") {
//             Navigator.pushReplacement(
//               context,
//               MaterialPageRoute(builder: (context) => ParentAppNavBar()),
//             );
//           } else {
//             _showErrorDialog('Rôle utilisateur inconnu ou non autorisé.');
//           }
//         } catch (e) {
//           // Display error dialog for failed login attempt
//           _showErrorDialog(loginFailedMessage);
//         }
//       }
//     }
//   }

//   void _showErrorDialog(String message) {
//     if (!isUsernameEmpty && !isPasswordEmpty) {
//       showDialog(
//         context: context,
//         builder: (BuildContext context) {
//           return AlertDialog(
//             title: Text('Erreur'),
//             content: Text(message),
//             actions: <Widget>[
//               TextButton(
//                 onPressed: () {
//                   Navigator.of(context).pop();
//                 },
//                 child: Text('OK'),
//               ),
//             ],
//           );
//         },
//       );
//     }
//   }

//   // Fonction pour réinitialiser les états d'erreur lors de la modification du nom d'utilisateur
//   void _resetUsernameError() {
//     if (isUsernameEmpty) {
//       setState(() {
//         isUsernameEmpty = false;
//       });
//     }
//   }

//   // Fonction pour réinitialiser les états d'erreur lors de la modification du mot de passe
//   void _resetPasswordError() {
//     if (isPasswordEmpty) {
//       setState(() {
//         isPasswordEmpty = false;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Form(
//       key: widget.formKey,
//       autovalidateMode: AutovalidateMode.disabled,
//       child: Padding(
//         padding: const EdgeInsets.all(15.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Username field
//             TextFormField(
//               controller: usernameController,
//               onChanged: (value) {
//                 _resetUsernameError(); // Réinitialiser l'état d'erreur du nom d'utilisateur
//               },
//               decoration: InputDecoration(
//                 labelText: AppText.Username,
//                 labelStyle: TextStyle(color: Colors.black54),
//                 prefixIcon: Padding(
//                   padding: EdgeInsets.all(5.0),
//                   child: Icon(Icons.person, color: Color(0xFF4099FF)),
//                 ),
//                 errorText: isUsernameEmpty ? usernameErrorMessage : null,
//                 focusedErrorBorder: OutlineInputBorder(
//                   borderSide: BorderSide(color: Colors.red),
//                 ),
//               ),
//             ),
//             SizedBox(height: 20),
//             // Password field
//             TextFormField(
//               controller: passwordController,
//               onChanged: (value) {
//                 _resetPasswordError(); // Réinitialiser l'état d'erreur du mot de passe
//               },
//               obscureText: !isPasswordVisible,
//               decoration: InputDecoration(
//                 labelText: AppText.Password,
//                 labelStyle: TextStyle(color: Colors.black54),
//                 prefixIcon: Padding(
//                   padding: EdgeInsets.all(5.0),
//                   child: Icon(Icons.lock_rounded, color: Color(0xFF4099FF)),
//                 ),
//                 suffixIcon: IconButton(
//                   icon: Icon(
//                     isPasswordVisible ? Icons.visibility : Icons.visibility_off,
//                     color: Color(0xFF4099FF),
//                   ),
//                   onPressed: () {
//                     setState(() {
//                       isPasswordVisible = !isPasswordVisible;
//                     });
//                   },
//                 ),
//                 errorText: isPasswordEmpty ? passwordErrorMessage : null,
//                 focusedErrorBorder: OutlineInputBorder(
//                   borderSide: BorderSide(color: Colors.red),
//                 ),
//               ),
//             ),
//             SizedBox(height: 20),
//             SizedBox(
//               width: double.infinity,
//               height: 60,
//               child: ElevatedButton(
//                 onPressed: _login,
//                 style: ElevatedButton.styleFrom(
//                   shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(10),
//                   ),
//                 ),
//                 child: Text(AppText.LoginButton),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }


