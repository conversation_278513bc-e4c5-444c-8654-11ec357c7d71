import 'package:NovaSchool/commun/parent_app/P_appDrawer.dart';
import 'package:NovaSchool/commun/parent_app/app_PnavBar.dart';
import 'package:NovaSchool/features/teacher_app/Home/appbar.dart';
import 'package:NovaSchool/models/trimesterDataDto.dart';
import 'package:NovaSchool/services/auth_service.dart';
import 'package:NovaSchool/services/carnet_service.dart';
import 'package:flutter/material.dart';
import 'package:NovaSchool/features/chat/test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../../../../commun/app_background.dart';
import '../../../../commun/parent_app/app_secondheadBar.dart';
import 'package:get/get.dart';
class PCarnetScreen extends StatefulWidget {
  const PCarnetScreen({Key? key}) : super(key: key);

  @override
  _PCarnetScreenState createState() => _PCarnetScreenState();
}

class _PCarnetScreenState extends State<PCarnetScreen> {
  final carnet = dotenv.get('CARNET_NAME');
  String? selectedTrimester;
  String selectedVersion = 'Version privée';
  late Future<TrimesterDataDto> carnetDataFuture;
  final CarnetService carnetService = CarnetService();
  AuthService auth = AuthService();
  @override
  void initState() {
    super.initState();
    // Initialize data
    fetchCarnetData();
  }

  void fetchCarnetData() async {
    carnetDataFuture = carnetService.getElevesCarnet();
    print('carnetDataFuture $carnetDataFuture');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF2F2F2),
      drawer: AppDrawer(),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: Container(
          //
          width: double.infinity,

          ///
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [Colors.blue, Colors.cyan]),
          ),
          child: AppBar(
            iconTheme: IconThemeData(
              color:
              Colors.white, // 🔁 change la couleur ici selon ton besoin
              size: 30, // facultatif : ajuste la taille
            ),
            backgroundColor: Colors.transparent,
            // Pour afficher le gradient
            elevation: 0,
            centerTitle: true,
            // ✅ Assure le centrage du titre
            title: GestureDetector(
              onTap: () {
                // Réinitialiser l'index pour revenir directement à PHomeScreen
                final controller = Get.find<PNavigationController>();
                controller.resetToHome();

                // Naviguer vers ParentAppNavBar
                Get.to(() => ParentAppNavBar());
              },
              child: Image.asset(
                'assets/logos/ecocode.png',
                height: 150,
                color: Colors.white,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.logout, color: Colors.white),
                //title: Text('Déconnexion'),
                onPressed: () async {
                  await AuthService.logoutKeycloak();
                },
              ),
              /*IconButton(
                              icon: Icon(Icons.notifications, color: Colors.white),
                              // ✅ Ajout de l'icône de chat
                              onPressed: () {
                                  //Navigator.push(
                                  //context,MaterialPageRoute(builder: (context) => PChatScreen()),);
                              },
                          ),*/
            ],
          ),
        ),
      ),
      resizeToAvoidBottomInset: false,
      body: AppBackground(
        child: FutureBuilder<TrimesterDataDto>(
          future: carnetDataFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Center(child: Text("Erreur de chargement des données"));
            } else if (!snapshot.hasData) {
              return Center(child: Text("Aucune donnée disponible"));
            } else {
              // Data successfully fetched
              final data = snapshot.data!;
              return SingleChildScrollView(
                child: Column(
                  children: [
                    SecondHeadBar1(title: 'Carnet',icon: Icons.school,),

                    /* PrimaryHeaderContainer(
                      child: Container(
                        height: 100,
                        child: SecondHeadBar(
                          title: carnet,
                          titleColor: Colors.white,
                          iconColor: Colors.white,
                        ),
                      ),
                    ),*/
                    const SizedBox(height: 20),
                    buildTrimesterSelector(data.trimesters.keys.toList()),
                    const SizedBox(height: 10),
                    if (selectedTrimester != null) buildVersionSelector(),
                    const SizedBox(height: 20),
                    if (selectedVersion != null && selectedTrimester != null)
                      (data.trimesters.containsKey(selectedTrimester!)
                          ? buildMatiereList(data.trimesters[selectedTrimester!]!)
                          : Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 40.0),
                        child: Text(
                          "Aucune donnée disponible pour $selectedTrimester.",
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                      )),

                    /*if (selectedVersion != null && selectedTrimester != null)
                      buildMatiereList(
                        data.trimesters[selectedTrimester!]!,
                      ),*/
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget buildTrimesterSelector(List<String> trimesters) {
    // Define the desired order for trimesters
    final List<String> desiredOrder = [
      "Trimestre 1",
      "Trimestre 2",
      "Trimestre 3",
      "Annuel"
    ];

    // Filter and sort the trimesters based on the desired order
    final List<String> sortedTrimesters = desiredOrder
        .where((order) => trimesters.contains(order))
        .toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          /*Text(
          "Année scolaire 2024/2025",
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),*/Text(
            "Année scolaire ${getAcademicYear()}",
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
////
          // 🟢 Bouton "Moyenne Annuelle" statique
          GestureDetector(
            onTap: () {
              setState(() {
                selectedTrimester = "Annuel"; // Tu peux utiliser ce que tu veux ici
                selectedVersion = 'Version étatique';
              });
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              margin: const EdgeInsets.symmetric(horizontal: 15),
              decoration: BoxDecoration(
                color: selectedTrimester == "Annuel"
                    ? Colors.lightBlue
                    : Colors.blue.withOpacity(0.5),
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: Center(
                child: Text(
                  'Moyenne Annuelle',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
/////
          const SizedBox(height: 20),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: sortedTrimesters.map((String trimester) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5.0),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedTrimester = trimester;
                        selectedVersion = 'Version étatique';
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.all(10.0),
                      decoration: BoxDecoration(
                        color: selectedTrimester == trimester?Colors.lightBlue : Colors.blue.withOpacity(0.5),
                        // selectedTrimester == trimester? Color(0xFF4099FF): Colors.grey,
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      child: Center(
                        child: Text(
                          trimester,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

/*
  Widget buildVersionSelector() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        children: [
          buildClickableBox(
            'VERSION ÉTATIQUE',
            selectedVersion == 'VERSION ÉTATIQUE',
            () {
              setState(() {
                selectedVersion = 'VERSION ÉTATIQUE';
              });
            },
          ),
          const SizedBox(height: 15),
          buildClickableBox(
            'VERSION PRIVÉE',
            selectedVersion == 'VERSION PRIVÉE',
            () {
              setState(() {
                selectedVersion = 'VERSION PRIVÉE';
              });
            },
          ),
        ],
      ),
    );
  }*/
  Widget buildVersionSelector() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0,),

      child: Row(
        mainAxisAlignment: MainAxisAlignment.center, // Centre les boutons horizontalement
        children: [
          buildClickableBox(
            'Version étatique',
            selectedVersion == 'Version étatique',
                () {
              setState(() {
                selectedVersion = 'Version étatique';
              });
            },
          ),
          const SizedBox(width: 23),//15), // Espacement horizontal entre les boutons
          buildClickableBox(
            'Version privée',
            selectedVersion == 'Version privée',
                () {
              setState(() {
                selectedVersion = 'Version privée';
              });
            },
          ),
        ],
      ),
    );
  }


  Widget buildClickableBox(String text, bool isSelected, VoidCallback onTap) {
    return Container(
      height: 40,
      width: 150,
      margin: EdgeInsets.symmetric(vertical: 2.0),
      decoration: BoxDecoration(
        color: isSelected ? Colors.lightBlue/* Color(0xFF4099FF)*/ : Colors.grey,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: InkWell(
        onTap: onTap,
        child: Center(
          child: Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 15,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget buildMatiereList(Trimester trimester) {
    final version = selectedVersion == 'Version étatique'
        ? trimester.versionEtatique
        : trimester.versionPrive;

    if (!version.acces || version.modules == null || version.modules!.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text(
            "Les notes ne sont pas encore disponibles.",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return Column(
      children: [
        // General average display
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
          child: Container(
            padding: const EdgeInsets.all(15.0),
            decoration: BoxDecoration(
              color: Colors.white,//Color(0xFFE3F2FD),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Moyenne Générale:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text(
                  version.moyenneGenerale?.toStringAsFixed(2) ?? '-',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ),
        // Modules
        ...version.modules!.map((module) {
          return Padding(
            padding:
            const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
            child: Container(
              padding: const EdgeInsets.all(15.0),
              decoration: BoxDecoration(
                color: Colors.white,//Color(0xFFEDF4FF),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    module.module,
                    style: TextStyle(
                      fontSize: 19,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF4099FF),
                    ),
                  ),
                  const SizedBox(height: 10),
                  Column(
                    children: module.subjects.asMap().entries.map((entry) {
                      final index = entry.key;
                      final subject = entry.value;

                      return Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(subject.matiere,
                                  style: TextStyle(fontSize: 15,fontWeight:  FontWeight.w500 )),
                              Text(
                                  subject.nonClassed
                                      ? 'معفى'
                                      : subject.valeur.toStringAsFixed(2),
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold)),
                            ],
                          ),
                          if (index != module.subjects.length - 1)
                          //Divider(color: Color(0xFFF2F2F2)),
                            Divider(color: Colors.grey.shade300),
                        ],
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Moyenne:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      Text(
                        module.average
                            .toStringAsFixed(2), // Use the provided average
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ],
    );

  }

  String getAcademicYear() {
    final DateTime currentDate = DateTime.now();
    final int currentYear = currentDate.year;
    final int currentMonth = currentDate.month;

    if (currentMonth >= 9) {
      // Après ou en septembre, nouvelle année scolaire
      final int startYear = currentYear;
      final int endYear = currentYear + 1;
      return '$startYear/$endYear';
    } else {
      // Avant septembre, toujours l'année scolaire précédente
      final int startYear = currentYear - 1;
      final int endYear = currentYear;
      return '$startYear/$endYear';
    }
  }

}
